<script lang='ts' setup>
import { provide, ref, watch, computed } from 'vue'
import type { ILeadData } from '@/types/lead'
import CrmBaseInfoReport from '@/components/crm/crm-base-info/CrmBaseInfoReport.vue'

const props = defineProps<{
    visible: boolean
    companyInfo?: ILeadData
}>()
const dialogVisible = ref(props.visible)
const crmDetail = computed(() => {
    return props.companyInfo
})
watch(() => props.visible, (newVal) => {
    dialogVisible.value=newVal
})

provide('crmDetail', crmDetail)
const emit = defineEmits(['close','update:visible'])
const handleClose = () => {
    emit('update:visible', false)
}
</script>
<template>
    <div>
        <el-drawer v-model="dialogVisible" direction="rtl" size="400" @close="handleClose">
            <template #header>
                <div class="title">相关报告</div>
            </template>
            <template #default>
                <div>
                    <CrmBaseInfoReport :span="24"></CrmBaseInfoReport>
                </div>
            </template>
        </el-drawer>
    </div>
</template>
<style scoped lang='scss'>
</style>
