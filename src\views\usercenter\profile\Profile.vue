<script lang="ts" setup>
import UpdatePasswordDialog from './components/UpdatePasswordDialog.vue'
import { computed, ref } from 'vue'
import UpdatePhoneDialog from './components/UpdatePhoneDialog.vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { maskPhone } from '@/utils/format'
import OrgCard from '@/components/org/OrgCard.vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user || null
})

const orgList = computed(() => {
    const { account } = store.state.user
    const { orgs } = account || {}
    return orgs || []
})

// ====================== Refs & Reactive State ======================
const updatePasswordDialogVisible = ref(false)
const updatePhoneDialogVisible = ref(false)

// ====================== Methods ======================
const changePassword = () => {
    console.log('changePassword')
    updatePasswordDialogVisible.value = true
}

const changePhone = () => {
    console.log('changePhone')
    updatePhoneDialogVisible.value = true
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-column back-color-white tb-padding-45 lr-padding-40 height-100 gap-24 mw-280">
        <div class="flex flex-row top-bottom-center gap-8">
            <div class="flex h-14 w-4 back-color-blue border-radius-14"></div>
            <div class="flex font-16 color-black font-weight-500">账户信息</div>
        </div>
        <div class="flex flex-column gap-24">
            <div class="flex flex-row gap-24">
                <div class="mw-80 color-two-grey">姓名</div>
                <div class="color-black">{{ userInfo?.nickname || '' }}</div>
            </div>

            <div class="flex flex-row gap-24">
                <div class="mw-80 color-two-grey">登录账号</div>
                <div class="flex flex-row gap-8 color-black">
                    <div>{{ userInfo?.username || '' }}</div>
                    <div class="pointer color-blue" @click="changePassword">修改密码</div>
                </div>
            </div>

            <div class="flex flex-row gap-24">
                <div class="mw-80 color-two-grey">绑定手机号</div>
                <div class="flex flex-row gap-8 color-black">
                    <div>{{ maskPhone(userInfo?.mobile || '') }}</div>
                    <div class="pointer color-blue" @click="changePhone">更换手机号</div>
                </div>
            </div>

            <div class="flex flex-row gap-24">
                <div class="mw-80 color-two-grey">用户角色</div>
                <div class="flex flex-row gap-8 flex-wrap">
                    <el-tag type="info" v-for="(role, index) in userInfo?.roleName || []" :key="index">
                        {{ role }}
                    </el-tag>
                </div>
            </div>

            <div class="flex flex-row gap-24">
                <div class="mw-80 color-two-grey">所属组织</div>
                <div class="flex flex-row gap-8 flex-wrap">
                    <OrgCard v-for="org in orgList" :key="org.id" :name="org.name" :id="org.id" :hide-switch="true" />
                </div>
            </div>
        </div>
        <UpdatePasswordDialog v-model:visible="updatePasswordDialogVisible" />
        <UpdatePhoneDialog v-model:visible="updatePhoneDialogVisible" />
    </div>
</template>

<style lang="scss" scoped></style>
