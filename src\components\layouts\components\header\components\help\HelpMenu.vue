<script lang="ts" setup>
import Icon from '@/components/common/Icon.vue'
</script>

<template>
    <div class="flex flex-column gap-8 all-padding-8">
        <div class="flex flex-row top-bottom-center gap-8 back-tag-bg--hover all-padding-12 border-radius-4 pointer">
            <Icon icon="icon-icon_Controls" size="16" />
            <div class="color-black font-16 lh-16">操作手册</div>
        </div>
        <div class="flex flex-row top-bottom-center gap-8 back-tag-bg--hover all-padding-12 border-radius-4 pointer">
            <Icon icon="icon-icon_idea" size="16" />
            <div class="color-black font-16 lh-16">意见反馈</div>
        </div>
        <div class="flex flex-row top-bottom-center gap-8 back-tag-bg--hover all-padding-12 border-radius-4 pointer">
            <Icon icon="icon-icon_service" size="16" />
            <div class="color-black font-16 lh-16">客服电话</div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
