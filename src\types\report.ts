import type { IPaginationResponse, ICommonResponse } from './axios'
import type { IAllRecord } from './record'

export interface SearchCollectLogResponse extends IPaginationResponse {
  data: SearchCollectLogItem[];
}

export interface SearchCollectLogItem {
  collectType: string;
  companyId?: string;
  companyName: string;
  createTime: number;
  createUser: string;
  deductType?: string;
  errMsg?: string;
  executionTime: number;
  id: string;
  nickname?: string;
  orgId: string;
  orgName: string;
  requestId?: string;
  socialCreditCode: string;
  status: string;
  tenantId: string;
  updateTime?: number;
}

export interface CollectLogParams extends IAllRecord {
  collectType?: string;
  companyName?: string;
  createUser?: string;
  orgId?: string;
  socialCreditCode?: string;
  status?: string;
  tenantId?: string;
  page?: number;
  pageSize?: number;
  requestId?: string;
}


export interface ExportParams {
  collectType?: string | null;
  companyName?: string;
  createUser?: string;
  ids: object;
  nums: number;
  orgId?: string;
  socialCreditCode?: string;
  status?: string | null;
  tenantId?: string;
}

export interface DownlodaReportParams {
  deductType?: string;
  socialCreditCode?: string;
  requestId?: string;
  taxRequestId?: string;
}

export interface DownloadReportResponse extends ICommonResponse {
  data: { isBuy: boolean, url: string }
}

export interface CollectLogOption {
  key: string
  type: 'input' | 'select' | 'multipleSelect' | 'date' | 'cascader' | 'slider' | 'checkAllMultipleSelect'
  placeholder: string
  label: string
  editable: boolean
  isShow: boolean
  options?: Array<{
    label: string
    value: number | string
  }>
  props?: {
    label?: string
    value?: string
    emitPath?: boolean
    checkStrictly?: boolean
  },
}