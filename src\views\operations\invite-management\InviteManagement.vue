<script lang="ts" setup>
import moment from 'moment'
import { reactive, ref, onMounted, computed } from 'vue'
import searchBox from '@/components/common/SearchBox.vue'
import { useStore } from 'vuex'
import type { Ref } from 'vue'
import orderService from '@/service/orderService'
import systemService from '@/service/systemService'

import type { IOrderInviteRecordListItem, IOrderInviteRecordParams } from '@/types/order'

type inviteConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}

const store = useStore()
const isAdminOrYunwei = computed(() => {
    let user = store.state.user?.account?.user || []
    return user.role.includes('admin') || user?.role?.includes('yunwei')
})

const tenantId = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId
})

const pageInfo = reactive({ page: 1, pageSize: 20, total: 0 })

const tableData: Ref<IOrderInviteRecordListItem[]> = ref([] as IOrderInviteRecordListItem[])

const tableLoading: Ref<boolean> = ref(false)

let queryParams = reactive<IOrderInviteRecordParams>({
    page: 1,
    pageSize:20,
})

const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    search()
}
const updateSearchParams = (params: IOrderInviteRecordParams) => {
    queryParams = params
    search()
}

const search = () => {
    tableLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    queryParams.type = 1
    if(!isAdminOrYunwei.value){
        queryParams.tenantId = tenantId.value
    }
    orderService.orderInviteNewInviteRecord(queryParams)
        .then(res => {
            tableData.value = res.data
            pageInfo.total = res.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

const searchOptionKey = ref('INVITE_LIST_SEARCH_OPTIONS')

const searchConfig = ref<inviteConfig>()

onMounted(() => {
    search()
    if (isAdminOrYunwei.value) {
        searchOptionKey.value = 'INVITE_LIST_SEARCH_OPTIONS_FORADMIN'
        systemService.tenantList().then((res) => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantId: res.map((item) => ({
                    label: item.name,
                    value: item.id,
                })),
            }
        })
    }
})

</script>
<template>
    <div ref="mainContentRef" class="height-100 display-flex flex-column">
        <!-- 搜索栏 -->
        <div ref="searchContentRef" class="b-margin-16 back-color-white">
            <div class="all-padding-16">
                <searchBox :searchOptionKey="searchOptionKey" :customConfig="searchConfig" @updateSearchParams="updateSearchParams"></searchBox>
            </div>
        </div>
        <!-- 表格栏 -->
        <div class="all-padding-16 back-color-white border-box flex-grow-1">
            <!-- 表格 -->
            <el-table 
                ref="tableListRef" 
                :data="tableData" 
                v-loading="tableLoading" 
                row-key="id"
                header-row-class-name="tableHeader"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <el-table-column label="好友账号" prop="relationUserMobile"></el-table-column>
                <el-table-column label="好友用户名" prop="relationUserName"></el-table-column>
                <el-table-column label="好友手机号" prop="relationUserMobile"></el-table-column>
                <el-table-column label="好友状态">
                    <template #default="">
                        <span>注册成功</span>
                    </template>
                </el-table-column>
                <el-table-column label="奖励积分" prop="amount"></el-table-column>
                <el-table-column label="完成时间" prop="createTime">
                    <template #default="scope">
                        {{ scope.row.createTime ? moment(scope.row.createTime).format("YYYY-MM-DD HH:mm") : "-" }}
                    </template>
                </el-table-column>
                <el-table-column label="邀请人" prop="userName"></el-table-column>
                <el-table-column v-if="isAdminOrYunwei" label="所属租户" prop="tenantName"></el-table-column>
            </el-table>
            <!-- 分页器 -->
        </div>
        <div class="pagination-bar back-color-white display-flex justify-end b-padding-6 r-padding-16">
            <el-pagination v-model:currentPage="pageInfo.page" v-model:page-size="pageInfo.pageSize"
                           :total="pageInfo.total" :page-sizes="[20, 40, 60, 100]" layout="total, sizes, prev, pager, next, jumper" @change="pageChange"
            />
        </div>
    </div>
</template>
<style scoped lang="scss"></style>
