<template>
    <div class="flex space-between top-bottom-center">
        <div class="flex flex-column gap-8" style="width: 48%;">
            <span>数据大屏标题</span>
            <el-input
                v-model="dashboardTitle"
                placeholder="请输入数据大屏标题"
            ></el-input>
        </div>
    </div>
    <div class="flex top-bottom-center t-margin-16">
        <div class="flex flex-column gap-8" style="width: 100%;">
            <span>授权协议</span>
            <div ref="quillEditor" style="height: 300px;"></div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, watch, nextTick, onMounted, onUnmounted, onBeforeMount } from 'vue'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import type { other } from '@/types/OEM'
const props = defineProps<{
    modelValue: other
    editData: other
}>()

watch(() => props.editData, (newValue) => {
    console.log('newValueOther',newValue)
    setTimeout(() => {
        dashboardTitle.value = newValue.dashboardTitle || ''
        if (quillInstance.value) {
            quillInstance.value.root.innerHTML = newValue.sqxy || ''
        }
    },100)
},{immediate: true, deep: true })
const emit = defineEmits<{
    (e: 'update:modelValue', value: other): void
}>()
const formOthers = ref<other>(props.modelValue)
watch(formOthers, (newValue) => {
    emit('update:modelValue', newValue)
})

// ======其他======
const dashboardTitle = ref('')
const authAgreement = ref('')

// Quill富文本编辑器相关
const quillEditor = ref<HTMLElement | null>(null)
const quillInstance = ref<Quill | null>(null)

// 初始化Quill编辑器
const initQuillEditor = () => {
    if (quillEditor.value) {
        quillInstance.value = new Quill(quillEditor.value, {
            theme: 'snow',
            placeholder: '请输入授权协议内容...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'align': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['link'],
                    ['clean']
                ]
            }
        })

        // 监听内容变化
        quillInstance.value.on('text-change', () => {
            if (quillInstance.value) {
                authAgreement.value = quillInstance.value.root.innerHTML
            }
        })

        // 设置初始内容
        if (authAgreement.value) {
            quillInstance.value.root.innerHTML = authAgreement.value
        }
    }
}

// 其他
watch([dashboardTitle, authAgreement],() => {
    formOthers.value.dashboardTitle = dashboardTitle.value
    formOthers.value.sqxy = authAgreement.value
})

// 组件卸载时销毁编辑器实例
onUnmounted(() => {
    console.log('组件卸载',quillInstance.value)
    if (quillInstance.value) {
        quillInstance.value.root.innerHTML = ''  
        quillInstance.value = null
    }
})

onBeforeMount(() => {
    nextTick(() => {
        initQuillEditor()
    }) 
})

onMounted(() => {

})
</script>

<style lang='scss' scoped>


:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}

:deep(.upload-demo){
    display: flex;
    width: 300px;
}
:deep(.el-upload-list__item){
    width: 180px;
}

:deep(.inline-upload) {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 20px;
}

// Quill编辑器样式
:deep(.ql-toolbar) {
  border: 1px solid #dcdfe6;

  background-color: #fafafa;
  border-radius: 4px 4px 4px 4px;
}

:deep(.ql-container) {
  border: 1px solid #dcdfe6;
  font-size: 14px;
  border-radius: 0 0 4px 4px;
}

:deep(.ql-editor) {
  min-height: 200px;
  line-height: 1.6;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}
</style>