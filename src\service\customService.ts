import http from '@/axios'

import type { IGetCrmLeadParams, IGetLeadResponse, IGetTabPool, IUpdateMinistrantParams, INormalLeadResponse, ICustomerTabPoolParams } from '@/types/lead'

export default {
    // 列表上方线索池tab信息接口
    customTabPool(body: ICustomerTabPoolParams): Promise<IGetTabPool[]>{
        return http.get(`/api/zhenqi-crm/custom/tab-pool`,{
            params: body
        })
    },
    // 列表数据接口
    customList(data: IGetCrmLeadParams): Promise<IGetLeadResponse> {
        return http.get(`/api/zhenqi-crm/custom/list`, {
            params: data,
            hideError: true
        })
    },
    // 添加协作人
    customUpdateMinistrant(data:IUpdateMinistrantParams): Promise<INormalLeadResponse>{
        return http.post('/api/zhenqi-crm/custom/update-ministrant', data)
    },

}
