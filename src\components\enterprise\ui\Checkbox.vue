<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps<{
    label: string
    value: string
    onChange: (value: string) => void
    checked: boolean
}>()

const isChecked = ref(false)
const onCheck = () => {
    isChecked.value = !isChecked.value
    props.onChange(props.value)
}

watch(
    () => props.checked,
    (value) => {
        isChecked.value = value
    }
)
</script>

<template>
    <div class="flex flex-row left-right-center gap-8 pointer no-select" @click="onCheck">
        <div
            class="border w-14 h-14 border-radius-4 flex top-bottom-center left-right-center"
            :class="{ 'back-color-blue': isChecked, 'border-color-blue': isChecked }"
        >
            <el-icon :size="10" v-if="isChecked" class="color-white"><Select /></el-icon>
        </div>
        <div class="font-14 lh-15 color-black">{{ props.label }}</div>
    </div>
</template>

<style lang="scss" scoped></style>
