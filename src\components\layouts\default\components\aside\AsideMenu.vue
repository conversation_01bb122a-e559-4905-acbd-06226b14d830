<script lang="ts" setup>
import { computed, onBeforeMount, ref } from 'vue'
import Menus from './menu/Menus.vue'
import type { IMenuResponse } from '@/types/menu'
import { useRoute, type RouteLocationNormalizedLoadedGeneric } from 'vue-router'
import store from '@/store'
import clientRoutes from '../../../../../router/routes/clientRoutes'
import { flatten } from '@/utils/flatten'

const route = useRoute()

const menus = ref<IMenuResponse[]>([])

const props = defineProps<{
    isCollapse: boolean
    color?: string
    size?: string
}>()

onBeforeMount(async () => {
    const menuTree: IMenuResponse[] = await store.dispatch('menu/loadMenuTreeIfNeeded')
    menus.value = menuTree
})

const openList = computed(() => {
    const { matched } = route || {}
    const result = matched.map((item) => item.name)
    return result
})

const defaultActive = (route: RouteLocationNormalizedLoadedGeneric) => {
    const flattenMenu = flatten(clientRoutes)
    const target = flattenMenu.find((e) => e.name === route.name)
    if (!target) return route.name

    const { meta } = target || {}
    const { defaultActive } = meta || {}

    if (defaultActive) {
        return defaultActive
    } else {
        return route.name
    }
}
</script>

<template>
    <div class="flex-1 oh">
        <el-scrollbar view-class="height-100 flex flex-column">
            <el-menu
                class="el-menu-vertical flex-1"
                :collapse="props.isCollapse"
                :collapse-transition="false"
                :default-openeds="openList"
                :default-active="defaultActive(route)"
                :unique-opened="true"
            >
                <Menus :menu="menu" v-for="menu in menus" :key="menu.id" :collapse="props.isCollapse" />
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<style lang="scss" scoped>
.el-menu-vertical {
    border: none;
    padding-top: 16px;
}
.el-menu-vertical:not(.el-menu--collapse) {
    width: 200px;
}
</style>
