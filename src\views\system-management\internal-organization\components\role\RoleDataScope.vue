<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemService from '@/service/systemService'
import type { IRoleItem, IDataScopeListItem } from '@/types/role'
import type { RootState } from '@/types/store'
import RoleForm from './components/RoleForm.vue'
import permissionService from '@/service/permissionService'

const props = defineProps<{
    height?: number
}>()
const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})

/* 角色的操作 */
const roleList = ref<IRoleItem[]>([])
const searchVal = ref('')
const checkedRole = ref<IRoleItem>({
    id: '',
    menuIds: [],
    roleId: '',
    roleName: '',
    scopeData: [],
    tenantId: '',
})
const roleFormVisible = ref(false)
const roleFormType = ref('add')
const getRoleList = async () => {
    let queryObj = {}
    if (searchVal.value) {
        queryObj = {
            roleName: searchVal.value,
        }
    }
    systemService.roleListByName(queryObj).then((res) => {
        const { errCode, data } = res
        if (errCode === 0) {
            data.forEach((item) => {
                // 如果返回scopeData为空数组,那么默认各类目数据权限给默认值
                if (item.scopeData.length === 0) {
                    item.scopeData = dataScopeList.value.map((item) => {
                        return {
                            dataKey: item.dataKey,
                            scopeName: item.scopeName,
                            scopeType: item.defaultValue,
                        }
                    })
                }
            })
            // 将系统角色排到前面 目前判断系统角色依据 roleId === 'yuanqu_admin'
            data.sort((a, b) => {
                if (a.roleId === 'yuanqu_admin') return -1
                if (b.roleId === 'yuanqu_admin') return 1
                return 0
            })
            roleList.value = data
            if (checkedRole.value.id === '') {
                handleClickRole(data[0]) //默认选中第一项
            }
        } else {
            roleList.value = []
            ElMessage({
                type: 'error',
                message: '获取角色列表失败',
            })
        }
    })
}
let timeoutId: number | null = null
watch(searchVal, (newVal: string) => {
    if (timeoutId) {
        clearTimeout(timeoutId)
    }
    timeoutId = window.setTimeout(async () => {
        console.log(newVal)
        getRoleList()
    }, 500)
})
const handleClickRole = (row: IRoleItem) => {
    checkedRole.value = row
}
const handleAddRole = () => {
    roleFormType.value = 'add'
    roleFormVisible.value = true
}
let editItem = reactive<IRoleItem>({
    id: '',
    menuIds: [],
    roleId: '',
    roleName: '',
    scopeData: [],
    tenantId: '',
})
const handleEditRole = (row: IRoleItem) => {
    roleFormType.value = 'edit'
    editItem = row
    roleFormVisible.value = true
}
const handleDelRole = (row: IRoleItem) => {
    ElMessageBox.confirm('是否确认删除当前角色', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        let resRes = await systemService.roleRemove({ roleId: row.roleId })
        if (resRes.success === true) {
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
            checkedRole.value.id = ''
            checkedRole.value.menuIds = []
            checkedRole.value.roleId = ''
            checkedRole.value.roleName = ''
            checkedRole.value.scopeData = []
            checkedRole.value.tenantId = ''
            if (searchVal.value) {
                getRoleList()
            } else {
                getRoleList()
            }
        }
    })
}
const handleCloseRoleForm = () => {
    roleFormVisible.value = false
    getRoleList()
}

/* 数据权限 */
const dataPermissionList = [
    {
        label: '个人',
        key: 1,
    },
    {
        label: '所属组织',
        key: 2,
    },
    {
        label: '所属组织及下属组织',
        key: 3,
    },
    {
        label: '全部组织',
        key: 4,
    },
]
const checkedRadio = ref(0)
const handleChangeRadio = async (val: number) => {
    if (checkedRole.value.id) {
        dataScopeList.value.forEach((item) => {
            item.scopeType = item.values.some((v) => v.value === val) ? val : item.scopeType
        })
        checkedRole.value.scopeData = dataScopeList.value
        let editRes = await systemService.roleEdit(checkedRole.value)
        if (editRes.success === true) {
            ElMessage({
                type: 'success',
                message: '编辑成功',
            })
            getRoleList()
        }
    }
}
const handleChangeSelect = async () => {
    if (checkedRole.value.id) {
        checkedRole.value.scopeData = dataScopeList.value
        let editRes = await systemService.roleEdit(checkedRole.value)
        if (editRes.success === true) {
            const isSameScopeType = checkedRole.value.scopeData.every(
                (item, _, arr) => item.scopeType === arr[0].scopeType
            )
            if (isSameScopeType) {
                checkedRadio.value = checkedRole.value.scopeData[0].scopeType
            } else {
                checkedRadio.value = 0
            }
            ElMessage({
                type: 'success',
                message: '编辑成功',
            })
            getRoleList()
        }
    }
}
const isDisabled = ref(false)
watch(checkedRole, (newVal) => {
    isDisabled.value =
        (!userInfo.value?.role.includes('admin') && newVal.roleId === 'yuanqu_admin') ||
        !permissionService.isRoleEditPermitted() //非admin账号并且是系统角色 或者 没有编辑角色权限 ==> 开启禁用

    // 1.将选中角色的数据权限 回显
    dataScopeList.value.forEach((a) => {
        newVal.scopeData.forEach((b) => {
            if (a.dataKey === b.dataKey) {
                a.scopeType = b.scopeType
            }
        })
    })
    // 2.判断数据权限是否相同 回显
    checkedRadio.value = 0
    if (newVal.scopeData.length > 0) {
        const isSameScopeType = newVal.scopeData.every((item, _, arr) => item.scopeType === arr[0].scopeType)
        if (isSameScopeType) {
            checkedRadio.value = newVal.scopeData[0].scopeType
        }
    }
})
const dataScopeList = ref<IDataScopeListItem[]>([]) // 数据权限列表
const getDataScopeList = async () => {
    let res = await systemService.dataScopeList()
    dataScopeList.value = res
}

onMounted(async () => {
    await getDataScopeList()
    getRoleList()
})
</script>
<template>
    <div class="height-100">
        <div class="display-flex" :style="{ height: props.height ? props.height + 'px' : '' }">
            <div class="role-box">
                <div class="display-flex space-between top-bottom-center b-margin-12">
                    <div class="title-left">角色列表</div>
                    <div v-if="permissionService.isRoleAddPermitted()" class="title-right" @click="handleAddRole()">
                        新增
                    </div>
                </div>
                <el-input
                    style="width: 100%; margin-bottom: 12px"
                    placeholder="搜索角色"
                    :prefix-icon="Search"
                    v-model="searchVal"
                />
                <div class="role-content">
                    <div
                        class="role-item"
                        :class="{ 'active-role': checkedRole.id === role.id }"
                        v-for="role in roleList"
                        :key="role.id"
                        @click="handleClickRole(role)"
                    >
                        <div class="role-item-left">{{ role.roleName }}</div>
                        <!-- 如果是admin账号 -->
                        <div v-if="userInfo?.role.includes('admin')">
                            <div class="display-flex top-bottom-center">
                                <el-icon v-if="permissionService.isRoleEditPermitted()" @click="handleEditRole(role)">
                                    <Edit />
                                </el-icon>
                                <el-icon
                                    v-if="permissionService.isRoleDeletePermitted()"
                                    style="margin-left: 5px"
                                    @click="handleDelRole(role)"
                                >
                                    <Delete />
                                </el-icon>
                            </div>
                        </div>
                        <div v-else>
                            <div
                                v-if="role.roleId === 'yuanqu_admin'"
                                class="display-flex top-bottom-center system-role"
                            >
                                系统
                            </div>
                            <div v-else class="display-flex top-bottom-center">
                                <el-icon v-if="permissionService.isRoleEditPermitted()" @click="handleEditRole(role)">
                                    <Edit />
                                </el-icon>
                                <el-icon
                                    v-if="permissionService.isRoleDeletePermitted()"
                                    style="margin-left: 5px"
                                    @click="handleDelRole(role)"
                                >
                                    <Delete />
                                </el-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="permission-box">
                <div class="b-margin-12">
                    <div class="title-left">数据权限设置</div>
                </div>
                <div class="data-permission-column">
                    <el-radio-group
                        style="display: flex; align-items: flex-start"
                        v-model="checkedRadio"
                        @change="handleChangeRadio"
                        :disabled="isDisabled"
                    >
                        <el-radio v-for="item in dataPermissionList" :key="item.key" :value="item.key" size="large">
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                    <div class="display-flex" v-for="obj in dataScopeList" :key="obj.dataKey">
                        <div class="data-permission-title">{{ obj.scopeName }}</div>
                        <div class="data-permission-content">
                            <el-select
                                class="no-border-select"
                                placeholder="所属权限"
                                style="width: 100%; height: 100%"
                                v-model="obj.scopeType"
                                @change="handleChangeSelect"
                                :disabled="isDisabled"
                            >
                                <el-option
                                    v-for="a in obj.values"
                                    :key="a.value"
                                    :label="a.name"
                                    :value="a.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <RoleForm
            :visible="roleFormVisible"
            :from="roleFormType"
            :editItem="editItem"
            @closeVisible="handleCloseRoleForm"
        ></RoleForm>
    </div>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
.active-role {
    background-color: #f6f8fa;
}

.role-box {
    width: 297px;
    height: 100%;
    display: flex;
    flex-direction: column;
    // border-right: 1px solid #e8e8e8;
    padding: 16px;
    .title-left {
        font-size: 16px;
        color: var(--main-black);
    }
    .title-right {
        font-size: 14px;
        color: var(--main-blue-);
    }
    .role-content {
        flex: 1;
        overflow: auto;
        // background-color: pink;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        .role-item {
            // border: 1px solid red;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 16px;
            margin-bottom: 12px;
            border-radius: 4px;
            &:hover {
                background-color: #f6f8fa;
            }
            .role-item-left {
                font-size: 14px;
                color: var(--main-black);
            }
            .system-role {
                font-size: 14px;
                color: var(--three-grey);
            }
            .role-item-right {
                font-size: 14px;
                color: var(--three-grey);
            }
        }
    }
}
.permission-box {
    flex: 1;
    padding: 16px;
    border-left: 2px solid var(--el-border-color-light);
    .permission-table {
        --el-table-row-hover-bg-color: transparent !important;
        .data-permission-column {
            position: absolute;
            top: 16px;
        }
    }
    .data-permission-title {
        width: 96px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        font-size: 16px;
        color: var(--main-black);
        border: 1px solid #e8e8e8;
    }
    .data-permission-content {
        // flex: 1;
        width: 253px;
        text-align: center;
        align-content: center;
        border: 1px solid #e8e8e8;
    }
}

:deep(.el-table__cell) {
    height: 110px;
}

.no-border-select {
    :deep(.el-select__wrapper) {
        width: 100%;
        height: 100%;
        box-shadow: none !important;
        border: none !important;
    }

    :deep(.el-select__inner) {
        box-shadow: none !important;
        border: none !important;
    }
    :deep(.el-select__wrapper:hover) {
        box-shadow: none !important;
        border: none !important;
        background-color: #fff !important;
    }
    :deep(.el-select__wrapper:focus) {
        box-shadow: none !important;
        border: none !important;
        background-color: #fff !important;
    }
}
</style>
