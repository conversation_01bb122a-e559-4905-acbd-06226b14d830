<script lang="ts" setup>
import { ref } from 'vue'
import ProfileGroup from './ProfileGroup.vue'
import OrgList from './OrgList.vue'

const showOrgSwitch = ref(false)

const showSwitcher = () => {
    showOrgSwitch.value = true
}

const hideSwitcher = () => {
    showOrgSwitch.value = false
}
</script>

<template>
    <ProfileGroup v-if="!showOrgSwitch" :showSwitcher="showSwitcher" />
    <OrgList v-if="showOrgSwitch" :hideSwitcher="hideSwitcher" />
</template>

<style lang="scss" scoped></style>
