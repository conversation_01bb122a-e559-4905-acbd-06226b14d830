type EventCallback = (msg: string) => void;
class EventBus {
    private events: Record<string, EventCallback[] | unknown[]> = {}

    // 订阅事件
    $on(event: string, callback: EventCallback): void {
        if (!this.events[event]) {
            this.events[event] = []
        }
        this.events[event].push(callback)
    }

    // 发布事件
    $emit(event: string, ...args: unknown[]): void {
        if (this.events[event]) {
            this.events[event].forEach(callback => {
                callback(...args)
            })
        }
    }

    // 取消订阅事件
    $off(event: string, callback: EventCallback): void {
        if (!this.events[event]) return
        this.events[event] = this.events[event].filter(cb => cb !== callback)
    }

    // 只触发一次的订阅事件
    $once(event: string, callback: EventCallback): void {
        const onceCallback = (...args: unknown[]) => {
            callback(...args)
            this.$off(event, onceCallback) // 执行一次后取消订阅
        }
        this.$on(event, onceCallback)
    }
}

// 创建一个事件总线实例
const eventBus = new EventBus()
export default eventBus