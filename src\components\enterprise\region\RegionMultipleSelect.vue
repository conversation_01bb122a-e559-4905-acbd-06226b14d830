<script lang="ts" setup>
import { ref, watch } from 'vue'

interface Option {
    value: string
    label: string
}
const props = defineProps<{
    onChange: (regions: string[]) => void
    list: Option[]
    placeholder: string
    defaultValue: string[]
}>()

const selectedProvinceRef = ref<string[]>([])

const reset = () => {
    selectedProvinceRef.value = []
}

watch(
    () => props.defaultValue,
    (value) => {
        selectedProvinceRef.value = value
    },
    { immediate: true }
)

defineExpose({ reset })
</script>

<template>
    <div class="flex flex-row left-right-center gap-16 pointer no-select top-bottom-center">
        <el-select
            v-model="selectedProvinceRef"
            :placeholder="placeholder"
            style="width: 492px"
            clearable
            no-data-text="无数据"
            class="cust-select"
            collapse-tags
            :max-collapse-tags="5"
            multiple
            @change="onChange"
        >
            <el-option v-for="item in list" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
    </div>
</template>

<style lang="scss" scoped></style>
