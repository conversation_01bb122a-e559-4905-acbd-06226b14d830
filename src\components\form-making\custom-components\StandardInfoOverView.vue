<script lang="ts" setup>
import { onMounted, ref, inject } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IStandardInfoOverViewResponseItem } from '@/types/model'
import StandardInfoOverViewDetailDialog from '@/components/search/custom-modal/components/StandardInfoOverViewDetailDialog.vue'
const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()
const tableLoading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})
const lockStatus = ref(false)
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tableData = ref<IStandardInfoOverViewResponseItem[]>([])
const columns = [
    { label: '标准号', prop: 'standardNumber' },
    { label: '标准名称', prop: 'standardName' },
    { label: '标准级别', prop: 'standardRank' },
    { label: '标准性质', prop: 'standardProperty' },
    { label: '发布日期', prop: 'releaseDate' },
    { label: '标准状态', prop: 'standardState' },
]
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
        })
        .then((res) => {
            const { isLock, total, items } = res
            lockStatus.value = isLock === 1 ? true : false
            tableData.value = items as IStandardInfoOverViewResponseItem[]
            pageInfo.value.total = total
            tableLoading.value = false
        })
}
onMounted(() => {
    getModelData()
})
</script>
<template>
    <el-table
        v-loading="tableLoading"
        :data="tableData"
        tooltip-effect="dark"
        border
        table-layout="fixed"
        fit
        :header-cell-style="{
            background: '#ECF5FF',
        }"
        size="large"
        empty-text="暂无数据"
    >
        <el-table-column type="index" label="序号" width="80" align="center" header-align="center"></el-table-column>
        <el-table-column
            align="center"
            header-align="center"
            v-for="(item, index) in columns"
            :key="index"
            :label="item.label"
            :prop="item.prop"
        >
            <template #default="{ row }">
                <div v-if="item.prop === 'standardName'">
                    <StandardInfoOverViewDetailDialog :row="row" />
                </div>
                <div v-else>
                    {{ row[item.prop] || '-' }}
                </div>
            </template>
        </el-table-column>
    </el-table>
</template>
<style scoped lang="scss"></style>
