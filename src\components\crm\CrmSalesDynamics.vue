<template>
    <!-- 销售动态 -->
    <view class="absolute border-radius-6 flex flex-column t-margin-16 r-margin-16" style="height: 100%;">
        <view class="com-padding b-margin-16">
            <view class="display-flex space-between top-bottom-center">
                <view class="font-18 color-black">销售动态</view>
                <el-button v-if="isFollowUp" type="primary" @click="addSalesDynamics">写跟进</el-button>
            </view>
            <view class="display-flex space-between top-bottom-center t-margin-16">
                <el-select 
                    v-model="queryParams.activityType" 
                    placeholder="跟进类型" 
                    clearable class="r-margin-10"
                    @change="activityTypeChange"
                    style="max-width: 150px;"
                >
                    <el-option 
                        v-for="item in searchOptions" 
                        :key="item.value" 
                        :label="item.text"
                        :value="item.value"
                    ></el-option>
                </el-select>
                <el-date-picker 
                    v-model="queryData" 
                    value-format="YYYY-MM-DD" 
                    @change="dateChange" 
                    type="daterange"
                    range-separator="-" 
                    start-placeholder="开始时间" 
                    end-placeholder="结束时间" 
                    clearable
                />
            </view>
        </view>
        <view v-if="salesList && salesList.length === 0">
            <div class="display-flex flex-column top-bottom-center">
                <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                <div class="font-first-title-unactive color-two-grey">暂无数据</div>
            </div>
        </view>
        <view v-else style="height: 100%; overflow-y: auto;">
            <el-scrollbar height="100%">
                <view class="infinite-list" style="overflow-y: auto">

                    <el-timeline v-loading="loadingStatus">
                        <el-timeline-item 
                            v-for="(i, index) in groupedList" 
                            :key="index" 
                            :type="'primary'"
                            placement="top" 
                            :timestamp="`${i.date}`" 
                            size="large" 
                            class="timestamp-date"
                        >
                            <view class="timeline">
                                <view v-for="(item, index) in i.items" :key="index" class="timeline-item">
                                    <view class="com-padding">

                                        <view class="display-flex">
                                            <div class="r-margin-6">
                                                <Icon icon="icon-a-huaban91" size="14px" color="#E9E9E9" />
                                            </div>
                                            <!-- 智能外呼 -->
                                            <view v-if="item.saleableType && item.saleableType === 'outbound'">
                                                <div class="color-three-grey font-16">
                                                    {{ moment(parseInt(item.createTime)).format('HH:mm') }}
                                                    <view class="l-margin-8 font-14 color-black">
                                                        {{ item.username }}
                                                    </view>
                                                </div>
                                                <!-- 智能外呼：未接通 -->
                                                <view v-if="item.outboundResult && item.outboundResult.result.includes('未接通')">
                                                    <view class="font-14 t-margin-8 display-block">外呼结果：{{item.outboundResult.result }}
                                                    </view>
                                                    <text class="display-flex font-14 t-margin-8 b-margin-10" >
                                                        <text class="color-three-grey" style="min-width: 70px;">来自外呼任务：</text>
                                                        <text class="color-primary pointer" style="min-width: 200px;"  @click="linkToOutboundTask(item.outboundResult!.taskCode)">{{ item.referName || "-" }}</text>
                                                    </text>
                                                    <div class="border-tag"></div>
                                                </view>
                                                <!-- 智能外呼：已接通 -->
                                                <view v-else>
                                                    <view class="font-14 t-margin-8 display-block">外呼结果：{{item.outboundResult!.result }}</view>
                                                    <text class="display-flex font-14 t-margin-8 b-margin-10">
                                                        <text class="color-three-grey"
                                                              style="min-width: 70px;">接通号码：</text>
                                                        <text style="min-width: 200px;">{{
                                                            item.outboundResult!.callPhone }}</text>
                                                    </text>
                                                    <text class="display-flex font-14 t-margin-8 b-margin-10">
                                                        <text class="color-three-grey"
                                                              style="min-width: 70px;">联系人名称：</text>
                                                        <text style="min-width: 200px;">{{ item.outboundResult!.name}}</text>
                                                    </text>
                                                    <text class="display-flex font-14 t-margin-8 b-margin-10">
                                                        <text class="color-three-grey"
                                                              style="min-width: 70px;">对话标签：</text>
                                                        <text style="min-width: 200px;">{{ item.outboundResult?.aiTag || "-" }}</text>
                                                    </text>
                                                    <text class="display-flex font-14 t-margin-8 b-margin-10" >
                                                        <text class="color-three-grey" style="min-width: 70px;">来自外呼任务：</text>
                                                        <text class="color-primary pointer" style="min-width: 200px;" @click="linkToOutboundTask(item.outboundResult!.taskCode)">{{ item.referName || "-" }}</text>
                                                    </text>
                                                    <div class="border-tag"></div>
                                                </view>
                                            </view>
                                            <view v-else>
                                                <div class="color-three-grey font-16">
                                                    {{ moment(parseInt(item.createTime)).format('HH:mm') }}
                                                    <view class="l-margin-6 font-14 color-black">
                                                        {{ item.username }}
                                                    </view>
                                                    <el-tag 
                                                        v-if="item.followType" 
                                                        class="l-margin-6" 
                                                        type="primary"
                                                        effect="light" round
                                                        size="small"
                                                    >   
                                                        <a class="font-12">{{ item.followType }}</a>
                                                    </el-tag>
                                                </div>
                                                <view v-if="item.activityType === 'create'">
                                                    <!-- 写跟进 -->
                                                    <div
                                                        class="font-16 t-margin-8 lh-24 description-text r-padding-24"
                                                        v-if="item.description">{{ item.description }}
                                                    </div>
                                                    <view class="font-14 t-margin-8 display-flex gap-20"
                                                          v-if="item.nextFollowDate || item.realFollowDate">
                                                        <view class="flex-1 text-nowrap">
                                                            <span class="font-four-level color-three-grey">实际跟进时间:</span>
                                                            {{
                                                                item.realFollowDate
                                                                    ? moment(parseInt(item.realFollowDate)).format("YYYY-MM-DD")
                                                                    : "-"
                                                            }}</view>
                                                        <view class="flex-1 text-nowrap">
                                                            <span class="font-four-level color-three-grey">下次跟进时间:</span>
                                                            {{
                                                                item.nextFollowDate
                                                                    ? moment(parseInt(item.nextFollowDate)).format("YYYY-MM-DD")
                                                                    : "-"
                                                            }}</view>
                                                    </view>
                                                    <div class="display-flex top-bottom-center flex-wrap" v-if="item.followImg">
                                                        <el-image 
                                                            v-for="(img, i) in item.followImg" 
                                                            :key="i"
                                                            fill="contain"
                                                            style="width: 4rem; height: 4rem"
                                                            class="r-margin-6 border-radius-6 pointer t-margin-6"
                                                            :initial-index="i" :src="fileService.getFileUrl(img.name)"
                                                            :zoom-rate="2" fit="cover" :preview-teleported="true"
                                                            :preview-src-list="item.followImg.map((i) => {
                                                                return fileService.getFileUrl(i.name);
                                                            })
                                                            " />
                                                    </div>

                                                    <view class="t-margin-10" v-if="item.followFiles">
                                                        <view
                                                            class="t-margin-6 border-radius-6 font-14 display-flex top-bottom-center"
                                                            v-for="(file, i) in item.followFiles" :key="i"
                                                            style="width: 300px; white-space: nowrap;" text-overflow:
                                                            ellipsis>
                                                            <text :title="file.originalName"
                                                                  style="text-overflow: ellipsis; overflow: hidden; display: inline-block;">{{
                                                                      file.originalName }}</text>
                                                            <text class="l-margin-10 font-14 color-primary pointer"
                                                                  @click="downloadFile(file.name)">下载
                                                            </text>
                                                        </view>
                                                    </view>
                                                </view>

                                                <view class="font-16 t-margin-8 display-block lh-24 description-text r-padding-24"
                                                      v-else-if="item.activityType === 'update' || item.activityType === 'toUser' || item.activityType === 'other'">
                                                    <!--跟新状态 --> <!-- 转移人员 --> <!-- 其他 -->
                                                    {{ item.description }}
                                                </view>

                                                <view class="font-14 t-margin-8"
                                                      v-else-if="item.activityType === 'turn'">
                                                    <!-- 线索转移 -->
                                                    <view class="display-flex font-14 t-margin-8 lh-24 description-text r-padding-24">
                                                        <view class="color-black">
                                                            {{ item.description }}
                                                        </view>
                                                    </view>
                                                </view>
                                                <text class="display-flex font-14 t-margin-8 b-margin-10">
                                                    <text class="color-three-grey" style="min-width: 70px;">来自{{
                                                        item.referType == "lead"
                                                            ? "线索"
                                                            : item.referType == "customer"
                                                                ? "客户"
                                                                : ""
                                                    }}：</text>
                                                    <text class="color-primary" style="min-width: 200px;">{{
                                                        item.referName || "-" }}</text>
                                                </text>
                                                <div class="border-tag"></div>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </el-timeline-item>
                    </el-timeline>
                </view>
            </el-scrollbar>
        </view>
        <!-- 动态列表 -->

        <AddSalesDynamics 
            v-if="showAddSalesDynamics" 
            :showAddSalesDynamics="showAddSalesDynamics"
            :permission="permission" 
            :crmItem="crmDetail"
            @closeAddSalesDynamics="closeAddSalesDynamics" 
            @getSalesList="addSuccessChangeList"
            @refreshData="refreshData"
            @getSalesDynamicsList="getSalesDynamicsList">
        </AddSalesDynamics>
    </view>
</template>

<script lang='ts' setup>

import { ref, onMounted, inject, computed, onBeforeUnmount, defineProps } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import type { Ref } from 'vue'
import type { ICrmGetActiviesItem, ICrmGetActiviesParams } from '@/types/lead'
import type { ILeadData } from '@/types/lead'
import AddSalesDynamics from './salse-dynamics/AddSalesDynamics.vue'
import eventBus from '@/utils/eventBus'
import Icon from '@/components/common/Icon.vue'
import moment from 'moment'
import crmService from '@/service/crmService'
import commonData from '@/js/common-data'
import fileService from '@/service/fileService'
// import { useRouter } from 'vue-router'

// const router = useRouter()
const props = defineProps({
    crmType: {
        type: String,
        required: true
    }
})

const groupedSalesListByDate = computed(() => {
    const groups: { date: string, items: ICrmGetActiviesItem[] }[] = []
    const map = new Map<string, number>()
    if (salesList.value) {
        salesList.value.forEach(item => {
            console.log('item', item)
            const date = moment(parseInt(item.createTime)).format('YYYY-MM-DD')
            const index = map.get(date)

            if (index !== undefined) {
                groups[index].items.push(item)
            } else {
                map.set(date, groups.length)
                groups.push({
                    date: date,
                    items: [item]
                })
            }
        })
    }

    console.log('groups', groups)
    return groups
})
const groupedList = ref<{ date: string, items: ICrmGetActiviesItem[] }[]>([])
const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})

// isFollowUp：根据权限判断是否显示写跟进
const isFollowUp = ref<boolean>(true)

// type SearchOption = {
//     value: string;
//     text: string;
// };

const permission = ref('')
const emit = defineEmits(['refreshData'])
const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>
const refreshData = () => {
    emit('refreshData')
}

const queryData = ref<number[]>([])
const loadingStatus = ref(false)
const loadingText = ref('more')
const showAddSalesDynamics = ref(false)
const salesList = ref<ICrmGetActiviesItem[]>([])

const options = ref({
    pageSize: 1000,
    pageNum: 1,
})
const crmId = ref(crmDetail.value.id)

const getSalesDynamicsList = async (queryParams: ICrmGetActiviesParams) => {
    // console.log('queryParams', queryParams)
    loadingStatus.value = true
    loadingText.value = 'loading'
    const res = await crmService.crmGetActivities(queryParams)
    salesList.value = res.data
    groupedList.value = groupedSalesListByDate.value
    loadingStatus.value = false
}

const queryParams = ref<ICrmGetActiviesParams>({
    leadId: crmId.value,
    activityType: '',
    page: options.value.pageNum,
    pageSize: options.value.pageSize
})

const searchOptions = commonData.salesDynamicsTypes
// const searchOptions: SearchOption[] = [
//     {
//         value: 'turn',
//         text: '新增',
//     },
//     {
//         value: 'toUser',
//         text: '转移',
//     },
//     {
//         value: 'create',
//         text: '跟进',
//     },
//     {
//         value: 'update',
//         text: '跟进状态',
//     },
//     {
//         value: 'outbound',
//         text: '智能外呼',
//     },
//     {
//         value: 'other',
//         text: '其他',
//     },
// ]
// 写跟进
const addSalesDynamics = () => {
    showAddSalesDynamics.value = true
}

const activityTypeChange = (e: string) => {
    if (e) {
        queryParams.value.activityType = e
        console.log('跟进类型改变成了 ', e)
    } else {
        queryParams.value.activityType = ''
        console.log('跟进类型置空')
    }
    salesList.value = []
    options.value.pageNum = 1
    getSalesDynamicsList(queryParams.value)
}
const dateChange = (arr: number[]) => {
    if (arr) {
        console.log('日期范围改变成了 ', arr)
        const startDate = new Date(arr[0])
        startDate.setHours(0, 0, 0, 0)
        const endDate = new Date(arr[1])
        endDate.setHours(23, 59, 59, 999)
        console.log('startDate', startDate)
        console.log('endDate', endDate)
        queryParams.value.createTime = [
            startDate.getTime(),
            endDate.getTime()
        ]
        console.log('queryParams', queryParams.value)
        queryData.value = [arr[0], arr[1]]

    } else {
        queryParams.value.createTime = []
    }
    salesList.value = []
    options.value.pageNum = 1
    getSalesDynamicsList(queryParams.value)
}

const downloadFile = (name: string) => {
    let fileUrl = fileService.getFileUrl(name)
    if (fileUrl) {
        window.location.href = fileUrl
    }
}

const closeAddSalesDynamics = () => {
    showAddSalesDynamics.value = false
}

const addSuccessChangeList = () => {
    options.value.pageNum = 1
    queryParams.value = {
        leadId: crmId.value,
        createTime: [],
        activityType: '',
        page: options.value.pageNum,
        pageSize: 10
    }
    salesList.value = []
    getSalesDynamicsList(queryParams.value)
}


const linkToOutboundTask = (taskCode: string) => {
    console.log('taskCode', taskCode)
    // router.push({path:'/auto-dialer/dialing-task/detail',query:{taskCode:taskCode}})
    window.open(`${window.location.origin}/auto-dialer/dialing-task/detail?taskCode=${taskCode}`)
}

const refreshActivitiesHandler = () => {
    getSalesDynamicsList(queryParams.value)
}

onBeforeUnmount(() => {
    console.log('销毁销售动态')
    eventBus.$off('refreshActivities', refreshActivitiesHandler)
})
const crmType = ref(props.crmType)
onMounted(async () => {
    // 判断是否有权限写跟进
    // 管理员,负责人，协作人可写跟进
    if (user.value) {
        if (user.value?.role.includes('yuanqu_admin') || user.value.nickname === crmDetail.value.user || crmDetail.value.ministrantInfos.some(info => info.nickname === user.value?.nickname)) {
            isFollowUp.value = true
        }
    }
    console.log('crmType', crmType.value)
    if(crmType.value.includes('pool')){
        isFollowUp.value = false
    }

    if (crmDetail.value.isCustomer === '1') {
        permission.value = 'customer'
    } else {
        permission.value = 'lead'
    }

    getSalesDynamicsList(queryParams.value)
    eventBus.$on('refreshActivities',refreshActivitiesHandler)
})

defineExpose({
    getSalesDynamicsList
})

</script>

<style lang='scss' scoped>
.border-tag {
    border: 1px solid var(--tag-border);
    width: 360px;
    margin-top: 20px;
}

.com-padding {
    padding: .12rem .16rem;
}

.infinite-list {
    height: 8rem;
    padding: 0;
    padding-top: 10px;
    margin: 0;
    list-style: none;

    .el-timeline-item {
        margin-left: 4px;
    }
}

.font-four-level {
    font-size: 14px;
}

.display-block {
    display: block;
}

::v-deep .timestamp-date .el-timeline-item__timestamp {
    font-size: 18px !important;
    color: #1966FF !important;
}

::v-deep .timestamp-date .el-timeline-item__wrapper{
    padding-left: 20px !important;
}

.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 3.5px;
    bottom: 0;
    height: 94%;
    width: 1px;
    border: 1px dashed #E9E9E9;
}

.timeline-item:first-child:not(:last-child)::after {
    left: 6px;
}

.description-text {
    word-wrap: break-word;
    word-break: break-all;
    white-space: pre-wrap;
}
</style>
