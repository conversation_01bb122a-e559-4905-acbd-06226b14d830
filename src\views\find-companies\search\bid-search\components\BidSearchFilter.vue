<script lang="ts" setup>
import { computed, onBeforeMount, onMounted, provide, ref } from 'vue'
import aicService from '@/service/aicService'
import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { formatSelectedLabel } from '@/utils/enterprise/region'
import {
    MappedFilter,
    MultiProvinceFilter,
    MultiSelectFilter,
    RegionFilter,
    SelectFilter,
} from '@/components/enterprise/filters'

const storeInitAction = 'enterprise/initTenderParams'
const storeSetAction = 'enterprise/setTenderSearchParams'
const store = useStore<RootState>()
const rules = ref<IAicNormalSearchRules[]>([])
const config = ref<IAicConditionData | null>(null)
const compressRules = ref<IAicNormalSearchRules[]>([])
const collapse = ref(true)

const cachedRules = computed(() => {
    const { tenderSearchRulesData } = store.state.app || {}
    return JSON.parse(JSON.stringify(tenderSearchRulesData)) || []
})

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

const tenderFilterParams = computed<INormalFilterParams[]>(() => {
    const { tenderFilterParams } = store.state.enterprise || {}
    return JSON.parse(JSON.stringify(tenderFilterParams)) || []
})

const getRules = async () => {
    if (cachedRules.value && Array.isArray(cachedRules.value) && cachedRules.value.length > 0) {
        rules.value = formatRules(cachedRules.value)
        compressRules.value = rules.value.slice(5)
        getStaticConfig()
    } else {
        const rulesRes = await aicService.conditionGetInfoForNomal({ searchType: 'searchTender' })
        store.dispatch('app/setTenderSearchRulesData', rulesRes)

        rules.value = formatRules(JSON.parse(JSON.stringify(rulesRes)))
        compressRules.value = rules.value.slice(5)

        getStaticConfig()
    }
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
    }
}

const formatRules = (rules: IAicNormalSearchRules[]) => {
    return rules
}

const isAreaFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'area'
}

const isMappedFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'mapped'
}

const isSelectFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'select'
}

const isMultiSelectFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'multiSelect'
}

const isCollapse = (index: number) => {
    return index >= 2 && collapse.value
}

const isMultiProvinceFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'mulipleProvince'
}

const toggleCollapse = () => {
    collapse.value = !collapse.value
}

const isHasSelected = computed(() => {
    const { tenderFilterParams } = store.state.enterprise
    return tenderFilterParams.length > 0
})

const clearAllSelect = () => {
    if (isHasSelected.value) {
        store.dispatch(storeInitAction)
    } else {
        toggleCollapse()
    }
}

const removeSelected = (params: INormalFilterParams) => {
    store.dispatch(storeSetAction, { ...params, checked: false })
}

// 推送到vuex
const pushToGlobal = (params: INormalFilterParams[]) => {
    console.log('push to global tender filter params', params)
    store.dispatch(storeSetAction, params)
}

// 子组件使用
provide('pushToGlobal', pushToGlobal)

onMounted(() => {
    getRules()
})

onBeforeMount(() => {
    store.dispatch(storeInitAction)
})
</script>

<template>
    <div>
        <div class="flex flex-column gap-18">
            <template v-for="(item, index) in rules" :key="item.key">
                <template v-if="isAreaFilterShown(item)">
                    <RegionFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.tenderFilterParams"
                    />
                </template>
                <template v-if="isMappedFilterShown(item)">
                    <MappedFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.tenderFilterParams"
                    />
                </template>
                <template v-if="isSelectFilterShown(item)">
                    <SelectFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.tenderFilterParams"
                    />
                </template>
                <template v-if="isMultiSelectFilterShown(item)">
                    <MultiSelectFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.tenderFilterParams"
                    />
                </template>
                <template v-if="isMultiProvinceFilterShown(item)">
                    <MultiProvinceFilter
                        :data="item"
                        v-show="!isCollapse(index) && config"
                        :store-action="storeSetAction"
                        :storeParams="store.state.enterprise.tenderFilterParams"
                    />
                </template>
            </template>
        </div>
        <div class="split-line t-margin-20 b-margin-20"></div>
        <div class="flex flex-row">
            <div class="w-140 color-blue font-14 pointer" @click="clearAllSelect">
                <span v-show="isHasSelected">清空所有</span>
            </div>
            <div
                class="flex flex-row top-bottom-center left-right-center font-16 gap-8 pointer flex-1"
                @click="toggleCollapse"
            >
                <span v-show="!collapse">收起</span>
                <span v-show="collapse">展开</span>
                <el-icon>
                    <CaretTop v-show="!collapse" />
                    <CaretBottom v-show="collapse" />
                </el-icon>
            </div>
            <div class="w-140 pointer" @click="toggleCollapse"></div>
        </div>
        <div class="flex tb-padding-16" v-if="tenderFilterParams.length > 0">
            <el-scrollbar view-class="flex flex-row gap-8 flex-wrap top-bottom-start max-height-86">
                <div
                    v-for="params in tenderFilterParams"
                    :key="params.categoryKey + params.value"
                    class="font-14 tb-padding-4 lr-padding-6 border-color-blue border-radius-4 flex flex-row pointer tag"
                    @click="removeSelected(params)"
                >
                    {{ params.category }}：
                    <span class="color-blue"> {{ formatSelectedLabel(params) }}</span>
                    <span class="flex top-bottom-center left-right-center l-padding-2">
                        <el-icon class="close-btn"><CloseBold /></el-icon>
                    </span>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.tag:hover .close-btn {
    color: var(--dark-red-);
}
</style>
