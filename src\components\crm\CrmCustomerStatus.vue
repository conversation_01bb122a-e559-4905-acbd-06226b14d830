<template>
    <div class="tb-padding-12 lr-padding-16 border-radius-4">
        <div class="display-flex t-margin-12 space-between   gap-20 r-padding-12">
            <div v-for="(status, idx) in customerStatusEnum" :key="idx" class="flex-1 flex-nowrap" :class="crmDetailComponents.status === 4 ? 'not-allow' : 'pointer'">
                <div class="status-item text-center relative" @click="changeStatus(status.value)"
                     :class="crmDetailComponents.status == status.value ? 'active-status-item ac-border-color active-status-item-bk' : 'no-ac-border-color'">
                    <div class="absolute left-border"
                         :class="crmDetailComponents.status == status.value ? 'ac-border-color back-color-white' : 'no-ac-border-color'">
                    </div>

                    <Icon :icon="status.icon" :size="'14px'"
                          :color="crmDetailComponents.status == status.value ? commonColor.mainBlue : ''" />
                    <span class="font-12 l-margin-4"
                          :class="crmDetailComponents.status == status.value ? 'color-blue' : ''">{{
                              status.label }}</span>

                    <div class="absolute right-border"
                         :class="crmDetailComponents.status == status.value ? 'ac-border-color active-status-item-bk' : 'no-ac-border-color'">
                    </div>
                </div>

            </div>
        </div>
    </div>


</template>

<script lang='ts' setup>
import { ref, onMounted, getCurrentInstance, inject, watch, defineEmits } from 'vue'
import type { Ref } from 'vue'
import Icon from '@/components/common/Icon.vue'
import type { ILeadData } from '@/types/lead'

const instance = getCurrentInstance()
const customerStatusEnum = JSON.parse(JSON.stringify(instance?.appContext.config.globalProperties.$commom.commonData.customerStatusEnum))

const commonColor = instance?.appContext.config.globalProperties.$commom.color

const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

const crmDetailComponents: Ref<ILeadData> = ref({} as Ref<ILeadData>)

watch(crmDetail, (newVal) => {
    crmDetailComponents.value = newVal
})

// const stateLineClass = (status: number) => {

//     if (crmDetailComponents.value.status === status) {

//         return 'status-now'
//     } else if (crmDetailComponents.value.status > status) {
//         return 'status-over'
//     } else {
//         return 'status-no'
//     }
// }

const emits = defineEmits(['changeStatus'])

const changeStatus = (status: number) => {
    emits('changeStatus', status)
}

onMounted(() => {
    crmDetailComponents.value = crmDetail.value
})

</script>

<style lang='scss' scoped>
.status-line-box {
    .status-line {
        width: 100%;
        height: 6px;
        margin-left: -3px;
    }
}

.last {
    flex-basis: 100%;
}

.status-over {
    background-color: var(--main-blue-);
}

.status-now {
    background: linear-gradient(to right, var(--main-blue-), var(--light-blue-));
}

.status-no {
    background-color: var(--light-blue-);
}

.status-item {
    border-top: 1px solid;
    border-bottom: 1px solid;
    height: 44px;
    line-height: 44px;
}

.active-status-item {
    border-top: 1px solid;
    border-bottom: 1px solid;
}

.active-status-item-bk {
    background-color: rgba(209, 224, 255, 1);
}

.left-border {
    width: 31px;
    height: 31px;
    border-top: 1px solid;
    border-right: 1px solid;
    transform: rotate(45deg);
    transform-origin: center center;
    left: -16px;
    top: 5px;
}

.right-border {
    width: 30px;
    height: 30px;
    border-top: 1px solid;
    border-right: 1px solid;
    transform: rotate(45deg);
    transform-origin: center center;
    right: -15px;
    top: 6px;
    z-index: 2;
}

.no-ac-border-color {
    border-color: var(--border-color);
}

.ac-border-color {
    border-color: var(--main-blue-);
}
</style>