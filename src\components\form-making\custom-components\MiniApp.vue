<script lang="ts" setup>
import { onMounted, ref, inject, defineEmits } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IGetMiniAppResponseItem } from '@/types/model'
import MiniAppDetailDialog from '@/components/search/custom-modal/components/MiniAppDetailDialog.vue'
const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()
const tableLoading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})

const emits = defineEmits(['updateTotal'])

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tableData = ref<IGetMiniAppResponseItem[]>([])
const columns = [
    { label: '小程序名称', prop: 'name' },
    { label: '发布时间', prop: 'releaseDate' },
    { label: '发布平台', prop: 'comeFrom' },
    { label: '分类', prop: 'classification' }
]
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
        })
        .then((res) => {
            const { total, items } = res
            tableData.value = items as IGetMiniAppResponseItem[]
            pageInfo.value.total = total
            emits('updateTotal', total)
            if (pageInfo.value.page !== 1) {
                jumpHref()
            }
        })
        .finally(() => {
            tableLoading.value = false
        })
}

const jumpHref = () => {
    document.getElementById(`model-${props.modelItem.name}`)?.scrollIntoView({
        behavior: 'smooth',
        inline: 'nearest',
    })
}

//计算表格的index
const indexFilter = (index: number) => {
    return (pageInfo.value.page - 1) * pageInfo.value.pageSize + index + 1
}

const moreInfoDialogShow = ref<boolean>(false)
const moreInfoStr = ref<string | undefined | null | number>('')

const handleShowMore = (row: IGetMiniAppResponseItem, prop: keyof IGetMiniAppResponseItem) => {
    moreInfoStr.value = row[prop]
    moreInfoDialogShow.value = true
}

onMounted(() => {
    getModelData()
})
</script>
<template>
    <el-table
        v-loading="tableLoading"
        :data="tableData"
        tooltip-effect="dark"
        border
        table-layout="fixed"
        fit
        :header-cell-style="{
            background: '#ECF5FF',
        }"
        size="large"
        empty-text="暂无数据"
    >
        <el-table-column type="index" label="序号" width="80" align="center" :index="indexFilter" header-align="center"></el-table-column>
        <el-table-column
            align="center"
            header-align="center"
            v-for="(item, index) in columns"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
        >
            <template #default="{ row }">
                <div>
                    {{ row[item.prop] && row[item.prop].length > 20 ? row[item.prop].slice(0, 20) + '...' : row[item.prop] || '-' }}
                </div>
                <el-button
                    v-if="row[item.prop] && row[item.prop]?.length > 20"
                    type="primary"
                    text
                    @click="handleShowMore(row, item.prop as keyof IGetMiniAppResponseItem)"
                >
                    更多
                </el-button>
            </template>
        </el-table-column>
        <el-table-column label="内容" width="90" align="center" header-align="center">
            <template #default="{ row }">
                <MiniAppDetailDialog :row="row" />
            </template>
        </el-table-column>
    </el-table>
    <el-pagination
        v-if="pageInfo.total > 0"
        class="flex justify-end"
        v-model:current-page="pageInfo.page"
        v-model:page-size="pageInfo.pageSize"
        :page-sizes="[10]"
        layout="total, prev, pager, next"
        :total="pageInfo.total"
        @change="getModelData"
    />
    <el-dialog ref="moreInfoDialog" v-model="moreInfoDialogShow">
        <div class="margin-left-30 margin-right-30 vm" v-html="moreInfoStr"></div>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
