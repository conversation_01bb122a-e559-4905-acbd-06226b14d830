<template>
    <div>
        <div v-if="isLock" class="no-pay-item display-flex width-100 border-radius-8 oh" style="height: 400px">
            <div style="margin: auto" class="display-flex flex-column">
                <div style="text-align: center; margin-bottom: 5px">
                    <el-icon size="50" color="#409eff">
                        <Lock />
                    </el-icon>
                </div>
                <el-button size="large" type="primary" @click="pullLock"> 点击查看 </el-button>
            </div>
        </div>
        <div v-else>
            <div v-if="modelItem?.currentVersion?.jsonStr && modelData">
                <div v-for="model in mergeList" :key="model.name">
                    <div v-if="model.model === 'table'">
                        <!-- <SearchCompanyFmForm :modelItem="model" :outModelData="modelData" /> -->
                        <component :is="getComponent(model.innerModel)" :modelData="modelData[model.innerModel]">
                        </component>
                    </div>
                    <div v-if="model.model === 'list'">
                        <SearchCompanyFmTable :modelItem="model" :outModelData="modelData" />
                    </div>
                    <div v-if="model.model === 'tag'">
                        <el-tag
                            class="r-margin-10 b-margin-5"
                            type="primary"
                            v-for="item in modelData[model.innerModel]"
                            :key="item"
                        >
                            {{ item.name }}
                        </el-tag>
                    </div>
                    <!-- <div v-if="model.model === 'merge'">
                    <SearchCompanyFmMerge :modelItem="model" />
                </div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, computed, inject, defineEmits } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { Ref } from 'vue'
import type { IModelJson } from '@/types/model'
import type { SearchCompanyGsInfoResponse } from '@/types/company'
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
// import SearchCompanyFmForm from '@/components/form-making/SearchCompanyFmGenerateForm.vue'
import SearchCompanyFmTable from '@/components/form-making/SearchCompanyFmGenerateTable.vue'

import B2BInfo from './custom-components/B2BInfo.vue'

console.log(B2BInfo)

const emits = defineEmits(['updateBuyStatus'])
const props = defineProps({
    modelItem: Object,
})

// console.log('mergeName', JSON.parse(JSON.stringify(props.modelItem)))

const isLock: Ref<boolean> = ref(false)

const modelData: Ref<SearchCompanyGsInfoResponse | null> = ref(null)

const componentsMap = {
    B2BInfo,
}

type ComponentKey = keyof typeof componentsMap

type ComponentValue = (typeof componentsMap)[ComponentKey]
const getComponent = (innerModel: ComponentKey): ComponentValue => {
    return componentsMap[innerModel]
}

const mergeList = computed(() => {
    if (!props.modelItem) {
        return []
    }
    let deepMoedl = JSON.parse(JSON.stringify(props.modelItem))
    let Jstr = JSON.parse(deepMoedl.currentVersion.jsonStr)
    let res = Jstr.list
        .map((item: IModelJson) => {
            // console.log('mergeItem', item)
            if (item.model === 'table') {
                console.log('.....', item)
                return {
                    ...item,
                    innerModel: item.list?.[0].model,
                    list: item.list,
                }
            } else if (item.model === 'list') {
                return {
                    ...item,
                    innerModel: 'items', //兼容老表单数据，写死，应变成可配
                    list: item.list,
                }
            } else if (item.model === 'tag') {
                return {
                    ...item,
                    innerModel: item.list?.[0].model,
                    list: item.list,
                }
            } else {
                return null
            }
        })
        .filter((item: IModelJson | null) => {
            return item !== null
        })
        .map((item: IModelJson) => {
            return {
                ...item,
                currentVersion: {
                    jsonStr: JSON.stringify(item),
                },
            }
        })

    console.log('----', res)
    return res
})
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const companyName: Ref<string> = inject('companyName', ref(''))
const getModelData = () => {
    if (!props.modelItem?.name) {
        console.error('modelItem is undefined')
        return
    }
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
        })
        .then((res) => {
            console.log('mergeRes', res)
            isLock.value = res.isLock === 1 ? true : false
            modelData.value = res
        })
}

const pullLock = () => {
    ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            orderService
                .orderBuyLegal({
                    socialCreditCode: socialCreditCode.value,
                    companyName: companyName.value,
                    serviceKey: 'xs',
                })
                .then(() => {
                    isLock.value = false
                    emits('updateBuyStatus', true)
                    ElMessage({
                        message: '使用成功',
                        type: 'success',
                    })
                })
        })
        .catch(() => {})
}

onMounted(() => {
    getModelData()
})
</script>

<style lang="scss" scoped>
.no-pay-item {
    background: url('@/assets/images/model-lock.jpg') no-repeat;
    width: 100%;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    border: 3px solid var(--border-color);
}
</style>
