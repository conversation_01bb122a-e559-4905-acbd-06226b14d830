import type { ICommonResponse,IPaginationResponse } from './axios'
import type { IAllRecord } from './record'

export interface ITaskListRequest extends IAllRecord{
    createTimes?: string,
    page: number,
    pageSize: number,
    status?: number,
    taskName?: string,
    taskType?: number,
}

export interface ITaskEnums extends ICommonResponse{
    data:{
        taskType:TaskType[],
        taskStatus:taskStatus[]
    }
}

export interface TaskType {
    label: string,
    value: number
}

export interface taskStatus {
    label: string,
    value: number
}

export interface ITaskListResponse extends IPaginationResponse{
    data: ITaskListResponseItem[]
}

export interface ITaskListResponseItem {
    id:string,
    taskName:string,
    taskType:number,
    taskTypeStr:string,
    taskInfo:{
        name:string,
        originalName:string
    }
    status:number,
    statusStr:string,
    createTime:number,
    updateTime:number,
    isDel:number,
}