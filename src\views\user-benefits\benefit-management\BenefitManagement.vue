<template>
    <div ref="mainContentRef" class="height-100" style="background-color: #f7f7f7; ">
        <div ref="searchContentRef" v-if="isPlatManager" class="b-margin-16" style=" background-color: #fff; padding: 16px">
            <!-- 搜索公共组件 -->
            <searchBox
                searchOptionKey="BENEFIT_MANAMENT_SEARCH_OPTIONS"
                @updateSearchParams="updateSearchParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <el-table
                ref="tableList"
                :data="tableData"
                :style="{ 'min-height': tableHeight + 'px' }"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="权益名称" prop="serviceKey" min-width="150" >
                    <template #default="scope">
                        {{ scope.row.serviceName }}
                    </template>
                </el-table-column>
                <el-table-column label="剩余额度" prop="remainingAmount" />
                <el-table-column label="已使用额度" prop="usedAmount" />
                <el-table-column label="过期额度" prop="expiredAmount" />
                <el-table-column label="累计总额度" prop="totalAmount" />
                <el-table-column label="即将到期（30天）" prop="dueSoon" min-width="100">
                    <template #default="scope">
                        <span class="pointer" style="color: #1966FF;" @click="showDialog(scope.row)">{{ scope.row.dueSoon }} </span>
                    </template>
                </el-table-column>
                <el-table-column label="最后一笔订单到期时间" prop="latestOrderExpireTime">
                    <template #default="scope">
                        {{ scope.row.latestOrderExpireTime? moment(scope.row.latestOrderExpireTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </template>
                </el-table-column>
                <el-table-column v-if="isPlatManager" label="所属租户" prop="tenantName" />
                <el-table-column fixed="right" label="操作" min-width="120">
                    <template #default="scope">
                        <div class="display-flex gap-16">
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="toBenefitList(scope.row)"
                            >
                                权益列表
                            </div>
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="openBenefitDrawer(scope.row)"
                            >
                                消费明细
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix> 
        </div>
    </div>
    <div>
        <BenefitDrawer v-model:visible="benefitDrawerVisible" :serviceKey="serviceKey" :tenantId="tenantId" :transId="transId"/>
    </div>
    <BenefitListDialog v-model:visible="benefitListDialogVisible" :tenantId="tenantId" :serviceKey="serviceKey" />
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, reactive, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import searchBox from '@/components/common/SearchBox.vue'
import systemService from '@/service/systemService'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import type { IServiceOrderResponseItem, IOrderLegalStatisticParams, IOrderLegalStatisticResponseItem } from '@/types/order'
import orderService from '@/service/orderService'
import BenefitDrawer from '../components/BenefitDrawer.vue'
import BenefitListDialog from '../benefit-management/components/benefitListDialog.vue'

const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value ) {
        if(searchContentRef.value){
            tableHeight.value =
            mainContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
        }else{
            tableHeight.value =
            mainContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
        }
    }
}
const router = useRouter()
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const serviceKey = ref('')
const benefitListDialogVisible = ref(false)
const tableLoading = ref<boolean>(false)
const store = useStore<RootState>()

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

const tenantId = ref('')
const transId = ref('')  
type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}

const searchConfig = ref<CustomConfig>()

const updateSearchParams = (params:IOrderLegalStatisticParams) =>{
    queryParams.value = params
    search(queryParams.value)
}

const queryParams = ref({
    page:1,
    pageSize:20,
})

const tableData = ref<IOrderLegalStatisticResponseItem[]>([])
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    search(queryParams.value)
}

const search = (Params: IOrderLegalStatisticParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    console.log('queryParams', queryParams.value)
    orderService.orderLegalStatisticList(Params).then(res => {
        console.log('res',res)
        tableData.value = res.data
        totalNum.value = res.total
        console.log('tableData', tableData.value)
    }).finally(() => {
        tableLoading.value = false
    })
}

const showDialog = (service: IServiceOrderResponseItem) => {
    console.log(service)
    benefitListDialogVisible.value = true
    serviceKey.value = service.serviceKey
    tenantId.value = service.tenantId
    transId.value = service.transId
}

const toBenefitList = (service: IServiceOrderResponseItem) => {
    console.log(service)
    router.push({
        path:'/user-benefits/benefit-list',
        query:{serviceKey:service.serviceKey},
    })
}

const benefitDrawerVisible = ref(false)
const openBenefitDrawer = (service: IServiceOrderResponseItem) => {
    console.log(service)
    benefitDrawerVisible.value = true
    serviceKey.value = service.serviceKey
    tenantId.value = service.tenantId
    transId.value = service.transId
}

onMounted(() => {
    if(isPlatManager.value){
        systemService.tenantList().then(response => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantIds:response.map(item => ({
                    label:item.name,
                    value:item.id
                }))
            }
        })
    }
    getTableHeight()
    search(queryParams.value)
})

</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}

</style>