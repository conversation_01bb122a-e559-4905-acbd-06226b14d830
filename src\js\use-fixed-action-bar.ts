import { ref, onMounted, onBeforeUnmount } from 'vue'
import type { Ref } from 'vue'

export function useFixedActionBar(
    tableContentRef: Ref<HTMLElement | null>,
) {
    const distanceFromTop = ref(0) // 目标元素距离顶部的距离
    const handleScroll = () => {
        if (!tableContentRef.value) return

        const rect = tableContentRef.value.getBoundingClientRect()
        distanceFromTop.value = rect.top
        // console.log('目标元素距离顶部的距离',distanceFromTop.value)
        const actionBar = document.querySelector('.action-bar')
        const scrollContainer = document.querySelector('.oa')
        if (!actionBar || !scrollContainer) return

        if (distanceFromTop.value < 40) {
            // console.log('actionBar的高度', actionBar.getBoundingClientRect().height)
            const actionBarHeight = actionBar.getBoundingClientRect().height
            actionBar.classList.add('action-bar-fixed')
            const containerWidth = scrollContainer.getBoundingClientRect().width
            actionBar.style.width = `${containerWidth + 32}px`

            // 添加占位伪元素
            if (!actionBar.parentElement?.querySelector('.action-bar-placeholder')) {
                const placeholder = document.createElement('div')
                placeholder.className = 'action-bar-placeholder'
                placeholder.style.height = `${actionBarHeight}px`
                actionBar.parentElement?.insertBefore(placeholder,actionBar.nextSibling)
            }
        } else {
            actionBar.classList.remove('action-bar-fixed')
            actionBar.style.width = 'auto'

            // 移除占位元素
            const placeholder = actionBar.parentElement?.querySelector('.action-bar-placeholder')
            if (placeholder) {
                placeholder.remove()
            }
        }
    }

    onMounted(() => {
        const container = document.querySelector('.oa')
        container?.addEventListener('scroll', handleScroll)
    })

    onBeforeUnmount(() => {
        const container = document.querySelector('.oa')
        container?.removeEventListener('scroll', handleScroll)
    })
}
