<script setup lang="ts">
import type { IOrgTreeItem } from '@/types/org'
import { Search } from '@element-plus/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'
import ORGPNG from '@/assets/images/header/org.png'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemService from '@/service/systemService'
import type Node from 'element-plus/es/components/tree/src/model/node.mjs'
import OrgUpdateDialog from './OrgUpdateDialog.vue'
import { flatten } from '@/utils/flatten'
import type { ITenantPageItem } from '@/types/tenant'
import { useRoute } from 'vue-router'

const input = ref('')
const currentOrg = ref<IOrgTreeItem | null>(null)
const dialogVisible = ref(false)
const searching = ref(false)
const updateType = ref('add')
const tenantList = ref<ITenantPageItem[]>([])
const currentTenant = ref<ITenantPageItem | null>(null)
const route = useRoute()
const { params } = route

const props = defineProps<{
    getOrgList: () => void
    orgList: IOrgTreeItem[]
    searchOrgById: (v: string) => void
    searchList: IOrgTreeItem[] | null
    setCurrentOrg: (v: IOrgTreeItem) => void
}>()

const treeProps = {
    value: 'id',
    label: 'name',
    children: 'children',
}

const addNewOrg = (org: IOrgTreeItem) => {
    updateType.value = 'add'
    currentOrg.value = org
    dialogVisible.value = true
}

const editOrg = (v: IOrgTreeItem) => {
    if (!v) return
    updateType.value = 'edit'
    currentOrg.value = v
    dialogVisible.value = true
}

const deleteOrg = (v: IOrgTreeItem) => {
    if (!v) return
    const { id } = v || {}

    ElMessageBox.confirm('是否确认删除该组织?', '删除组织', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        systemService
            .orgRemove({ orgId: id })
            .then((res) => {
                const { errCode } = res
                if (errCode === 0) {
                    ElMessage.success('删除成功')
                } else {
                    ElMessage.error('删除失败')
                }
            })
            .catch(() => {
                ElMessage.error('删除失败，请稍后再试')
            })
            .finally(() => {
                if (currentTenant.value) {
                    searching.value = true
                    props.searchOrgById(currentTenant.value.id)
                }
            })
    })
}

const onUpdateDialogVisible = (refresh?: boolean) => {
    dialogVisible.value = false
    if (refresh && currentTenant.value) {
        searching.value = true
        props.searchOrgById(currentTenant.value.id)
    }
}

const nodeClick = (node: IOrgTreeItem) => {
    props.setCurrentOrg(node)
}

const searchTenant = (v: string) => {
    props.searchOrgById('')
    systemService
        .tenantPage({ name: v, page: 1, pageSize: 99 })
        .then((res) => {
            searching.value = false
            const { errCode, data } = res || {}
            if (errCode === 0) {
                if (input.value === '') {
                    tenantList.value = []
                } else {
                    tenantList.value = data
                }
            }
        })
        .catch(() => {
            searching.value = false
        })
}

const onSearchOrg = (tenant: ITenantPageItem) => {
    if (tenant.id === currentTenant.value?.id) {
        currentTenant.value = null
    } else {
        searching.value = true
        currentTenant.value = tenant
        props.searchOrgById(tenant.id)
    }
}

const expandedKeys = computed(() => {
    const ids = props.orgList.map((item) => item.id)
    return ids
})

watch(
    () => expandedKeys.value,
    (value) => {
        console.log('expandedKeys', value)
        if (value && value.length > 0) {
            const target = value[0]
            const node = props.orgList.find((e) => e.id === target)
            if (node) {
                nodeClick(node)
            }
        }
    }
)

watch(
    () => input.value,
    (value) => {
        currentOrg.value = null
        currentTenant.value = null
        if (value) {
            searchTenant(value)
        } else {
            tenantList.value = []
        }
    }
)

watch(
    () => props.orgList,
    () => {
        searching.value = false
    }
)

onMounted(() => {
    const { name, type } = params
    if (!name || !type) return
    if (typeof name === 'string' && typeof type === 'string') {
        input.value = name
    }
})
</script>

<template>
    <div class="height-100 flex flex-column w-265 gap-16">
        <div class="organization-input">
            <el-input
                v-model="input"
                style="height: 36px"
                size="large"
                placeholder="输入租户名称搜索"
                :prefix-icon="Search"
                :clearable="true"
            />
        </div>
        <div class="flex flex-column gap-8 tree-list" style="overflow: auto;">
            <template v-for="tenant in tenantList" :key="tenant.id">
                <div
                    class="h-36 flex flex-row gap-6 top-bottom-center all-padding-8 border border-radius-4"
                    @click="onSearchOrg(tenant)"
                >
                    <div class="flex top-bottom-center left-right-center w-16 h-16">
                        <img :src="ORGPNG" alt="org" class="width-100" />
                    </div>
                    <div class="font-14 lh-15 flex-1 text-nowrap text-ellipsis pointer">
                        {{ tenant.name }}
                    </div>
                    <el-icon :size="12" class="pointer">
                        <CaretRight v-if="currentTenant?.id !== tenant.id" />
                        <CaretBottom v-if="currentTenant?.id === tenant.id" />
                    </el-icon>
                </div>
                <template v-if="tenant.id === currentTenant?.id">
                    <div class="flex left-right-center">
                        <el-icon v-if="searching" class="is-loading"><Loading /></el-icon>
                    </div>
                    <el-tree
                        :data="orgList"
                        :props="treeProps"
                        :draggable="false"
                        @node-click="nodeClick"
                        node-key="id"
                        :default-expanded-keys="expandedKeys"
                        v-if="!searching"
                    >
                        <template #default="{ node, data }: { node: Node; data: IOrgTreeItem }">
                            <el-popover placement="top" trigger="hover" popper-class="popover-org">
                                <template #default>
                                    <div class="flex flex-row gap-10">
                                        <el-icon class="pointer color-blue--hover" @click="editOrg(data)">
                                            <Edit />
                                        </el-icon>
                                        <el-icon class="pointer color-blue--hover" @click="addNewOrg(data)">
                                            <Plus />
                                        </el-icon>
                                        <el-icon
                                            v-if="data.parentId !== ''"
                                            class="pointer color-blue--hover"
                                            @click="deleteOrg(data)"
                                        >
                                            <Delete />
                                        </el-icon>
                                    </div>
                                </template>
                                <template #reference>
                                    <div class="width-100">
                                        <span>{{ node.label }}</span>
                                    </div>
                                </template>
                            </el-popover>
                        </template>
                    </el-tree>
                </template>
            </template>
        </div>
        <div>
            <el-empty v-if="!input" :image-size="90" description="输入租户名称关键字进行搜索" />
            <el-empty v-if="input && !searching && tenantList.length === 0" :image-size="90" description="暂无数据" />
        </div>
        <OrgUpdateDialog
            :visible="dialogVisible"
            :onClose="onUpdateDialogVisible"
            :org="currentOrg"
            :orgList="flatten(orgList)"
            :type="updateType"
        />
    </div>
</template>

<style scoped>
.organization-input :deep(.el-input__inner) {
    height: 36px;
    font-size: 14px;
}
.tree-list :deep(.el-tree-node__content) {
    border-radius: 4px;
}

.el-tree {
    --el-tree-node-content-height: 36px;
}
</style>
<style lang="scss">
.el-popover.el-popper.popover-org {
    min-width: 20px !important;
    width: auto !important;
}
</style>
