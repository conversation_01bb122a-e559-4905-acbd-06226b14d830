<script lang="ts" setup>
import Icon from '@/components/common/Icon.vue'
import type { RootState } from '@/types/store'
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore<RootState>()

const nikename = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { nickname } = user || {}
    return nickname || ''
})

const username = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { username } = user || {}
    return username || ''
})
</script>

<template>
    <div class="flex flex-column left-right-center top-bottom-center gap-4 b-margin-12">
        <div>
            <Icon icon="icon-a-huaban268" color="#80AAFF" class="w-50 h-50" style="width: 50px; height: 50px" />
        </div>
        <div class="font-16 font-weight-400">{{ nikename }}</div>
        <div class="flex flex-row">
            <div class="font-14 font-weight-400 color-two-grey">登录账号：</div>
            <div class="font-14 font-weight-400 color-black">{{ username }}</div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
