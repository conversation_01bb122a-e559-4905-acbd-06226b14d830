<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue'

import type { FormInstance, FormRules } from 'element-plus'
import type { ICompanyInfo } from '@/types/company'
import type { ILeadData } from '@/types/lead'

import aicService from '@/service/aicService'
import { ElMessage, ElMessageBox } from 'element-plus'
import crmService from '@/service/crmService'

const props = defineProps<{
    visible: boolean
    from?: string
    leadInfo?: ILeadData
}>()
const dialogVisible = ref(props.visible)

const titleType = computed(() => {
    if (props.leadInfo?.id) {
        return '编辑'
    } else {
        return '新增'
    }
})
const isAdd = computed(() => {
    return props.leadInfo?.id ? false : true
})
watch(props, (newVal) => {
    dialogVisible.value = newVal.visible
    if (newVal.leadInfo) {
        const { companyName, socialCreditCode, channel, note, contactInfo } = newVal.leadInfo
        const { mobile } = contactInfo || {}
        form.companyName = companyName || ''
        form.socialCreditCode = socialCreditCode || ''
        form.phone = mobile
        form.channel = channel || ''
        form.note = note || ''
    }
})
type FormType = {
    leadId?: string,
    companyName: string,
    socialCreditCode: string,
    phone?: string,
    channel?: string,
    note?: string,
    clueType?: number,
    source?: number
}
const form = reactive<FormType>({
    leadId: '',
    companyName: '',
    socialCreditCode: '',
    phone: '',
    channel: '',
    note: '',
    clueType: props.from === 'lead' ? 2 : 3,
    source: 14 // 线索来源: 新增
})

const submitForm = async(formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            if (isAdd.value) {
                ElMessageBox.confirm(`新增${props.from === 'lead' ? '线索' : '客户'}将会扣除对应线索权益额度，是否确定使用?`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    handleSubmit(crmService.crmAdd as ActionType, form)
                })
            } else {
                if (props.leadInfo?.id) {
                    form.leadId = props.leadInfo.id
                    handleSubmit(crmService.crmUpdate as ActionType, form)
                }
            }
        } else {
            console.log('form表单效验不通过', fields)
        }
    })   
}
const emit = defineEmits(['refreshData','update:visible'])
type ActionType = (form: FormType) => Promise<boolean>
const handleSubmit = (action: ActionType, form: FormType) => {
    return action(form).then(() => {
        ElMessage({
            type: 'success',
            message: props.leadInfo?.id ? '编辑成功' : '新增成功'
        })
        handleClose()
        emit('refreshData')
    })
}
const handleClose = () => {
    form.leadId = ''
    form.companyName = ''
    form.socialCreditCode = ''
    form.phone = ''
    form.channel = ''
    form.note = ''
    form.clueType = props.from === 'lead' ? 2 : 3
    form.source = 14 //线索来源 新增
    emit('update:visible',false)
}

const rules = reactive<FormRules>({
    companyName: [{ required: true, message: '请输入企业名称', trigger: 'change' }],
    socialCreditCode: [{ required: true, message: '请输入企业税号', trigger: 'change' }],
    phone: [
        {
            validator: (rule, value, callback) => {
                console.log('rule', rule, value, callback)
                // 如果没填写手机号，直接通过校验
                if (!value) {
                    callback()
                    return
                }
                // 如果填写了，检查手机号格式
                const phoneRegex = /^1[3-9]\d{9}$/ // 11位，1开头
                if (!phoneRegex.test(value)) {
                    callback(new Error('请输入正确的手机号'))
                } else {
                    callback()
                }
            },
            trigger: 'blur',
        },
    ],
})

const formRef = ref<FormInstance>()

const companySearchLoading = ref(false)
const companyList = ref<ICompanyInfo[]>([])
const searchCompany = (query: string) => {
    if (query) {
        companySearchLoading.value = true
        setTimeout(async () => {
            let getCompanyByCodeRes = await aicService.searchEnterprise({ keyword: query, scope: '0' })
            if (getCompanyByCodeRes.success === true) {
                companyList.value = getCompanyByCodeRes.data
                companySearchLoading.value = false
            } else {
                ElMessage({
                    type: 'error',
                    message: getCompanyByCodeRes.errMsg
                })
                companySearchLoading.value = false
            }
            
        }, 200)
    } else {
        companyList.value = []
    } 
}
const compamyChange = (name: string) => {
    let chooseItem = companyList.value.find((item) => {
        return item.name === name
    })
    if (chooseItem) {
        form.socialCreditCode = chooseItem.socialCreditCode
    }
}
</script>
<template>
    <el-dialog 
        v-model="dialogVisible"
        :title="props.from === 'lead' ? `${titleType}线索` : `${titleType}客户`"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()">
        <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
            <el-form-item label="企业名称" prop="companyName">
                <el-select v-model.trim="form.companyName" filterable remote reserve-keyword placeholder="请输入企业名称"
                           :remote-method="searchCompany" :loading="companySearchLoading" @change="compamyChange" :disabled="!isAdd">
                    <el-option v-for="item in companyList" :key="item.socialCreditCode" :label="item.name"
                               :value="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item label="企业税号" prop="socialCreditCode">
                <el-input placeholder="请输入企业税号" v-model="form.socialCreditCode" :disabled="!isAdd"/>
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
                <el-input placeholder="请输入手机号" v-model="form.phone" />
            </el-form-item>
            <el-form-item :label="props.from === 'lead' ? '线索渠道' : '客户渠道'" prop="channel">
                <el-input :placeholder="props.from === 'lead' ? '请输入线索渠道' : ' '" v-model="form.channel" />
            </el-form-item>
            <el-form-item label="备注" prop="note">
                <el-input placeholder="请输入备注" v-model="form.note" />
            </el-form-item>
            <div v-if="isAdd" class="font-size-14 color-three-grey b-margin-24">提示：新增时将扣除一次线索权益(每家企业仅在首次新增时扣减一次线索权益，后续重复新增不再扣减)。</div>

            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
