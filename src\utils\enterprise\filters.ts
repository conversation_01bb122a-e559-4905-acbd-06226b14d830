import type { INormalFilterParams } from '@/types/aic'
import type { FilterResultValue } from '@/types/company'
export const formatFilters = (data: INormalFilterParams[]): Record<string, string | string[]> => {
    const result: Record<string, string | string[]> = {}
    const valueMap: Record<string, string[]> = {}

    data.forEach((item) => {
        const key = item.categoryKey
        if (!valueMap[key]) {
            valueMap[key] = []
        }

        if (item.value) {
            valueMap[key].push(item.value)
        }
    })

    Object.keys(valueMap).forEach((key) => {
        const hasAreaType = data.some(
            (item) =>
                item.categoryKey === key &&
                (item.type === 'area' ||
                    item.type === 'multiSelect' ||
                    item.type === 'mapped' ||
                    item.type === 'mulipleProvince')
        )

        if (hasAreaType) {
            result[key] = valueMap[key]
        } else {
            result[key] = valueMap[key].length > 1 ? valueMap[key] : valueMap[key][0]
        }
    })

    return result
}

export const formatBidFilters = (data: INormalFilterParams[]): Record<string, FilterResultValue> => {
    const result: Record<string, FilterResultValue> = {}
    const valueMap: Record<string, string[]> = {}
    const mappedMap: Record<string, Record<string, string[]>> = {}
    const areaMap: Record<string, { province: string[]; city: string[]; district: string[] }> = {}

    data.forEach((item) => {
        const key = item.categoryKey

        if (item.type === 'mapped') {
            if (!mappedMap[key]) {
                mappedMap[key] = {}
            }

            const [code, label, level] = item.value.split(',')
            console.log(label)
            const levelKey = level === '1' ? `${key}First` : `${key}Second`

            if (!mappedMap[key][levelKey]) {
                mappedMap[key][levelKey] = []
            }

            mappedMap[key][levelKey].push(code)
        } else if (item.type === 'mulipleProvince' || item.type === 'area') {
            if (!areaMap[key]) {
                areaMap[key] = { province: [], city: [], district: [] }
            }

            const valLen = item.value.length
            if (valLen === 2) {
                areaMap[key].province.push(item.value)
            } else if (valLen === 4) {
                areaMap[key].city.push(item.value)
            } else if (valLen === 6) {
                areaMap[key].district.push(item.value)
            }
        } else {
            if (!valueMap[key]) {
                valueMap[key] = []
            }

            if (item.value || item.value === '') {
                valueMap[key].push(item.value)
            }
        }
    })

    // 添加普通类型的处理
    Object.keys(valueMap).forEach((key) => {
        const hasAreaType = data.some(
            (item) =>
                item.categoryKey === key &&
                (item.type === 'area' ||
                    item.type === 'multiSelect' ||
                    item.type === 'mapped' ||
                    item.type === 'mulipleProvince')
        )

        const hasParamsType = data.some((item) => item.categoryKey === key && item.paramType)

        if (hasAreaType) {
            result[key] = valueMap[key]
        } else if (hasParamsType) {
            const paramType = data.filter((e) => e.categoryKey === key)[0].paramType
            if (paramType === 'array') {
                result[key] = valueMap[key]
            } else {
                result[key] = valueMap[key].length > 1 ? valueMap[key] : valueMap[key][0]
            }
        } else {
            result[key] = valueMap[key].length > 1 ? valueMap[key] : valueMap[key][0]
        }
    })

    // 添加 mapped 类型的处理
    Object.keys(mappedMap).forEach((key) => {
        result[key] = [mappedMap[key]]
    })

    // mulipleProvince area 类型
    Object.keys(areaMap).forEach((key) => {
        result[key] = areaMap[key]
    })

    console.log('resultresult')
    console.log(result)

    return result
}
