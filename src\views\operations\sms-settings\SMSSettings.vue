<template>
    <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
        <el-tabs v-model="activeName" class="demo-tabs" >
            <el-tab-pane label="模板管理" name="model">
                <SMSModel />
            </el-tab-pane>
            <el-tab-pane v-if="!isAdmin" label="发送记录" name="record">
                <SMSRecord />
            </el-tab-pane>
        </el-tabs>
    </div>

</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SMSModel from './components/SMSModel.vue'
import SMSRecord from './components/SMSRecord.vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const store = useStore<RootState>()
const isAdmin = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})
const activeName = ref('model')

</script>


