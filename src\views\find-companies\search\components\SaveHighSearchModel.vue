<template>
    <div class="display-flex flex-column height-100">
        <div class=" b-padding-40 flex-1">
            <el-row :gutter="16">
                <el-col :xs="12" :sm="12" :md="8" :lg="6" :xl="4" v-for="(item) in saveModelList"
                        @click="useTemplete(item)" @mouseover="hoverMarketId = item.id" @mouseleave="hoverMarketId = ''"
                        :key="item.id" draggable="true" @dragover.prevent @dragstart="dragstart(item)" @drop="onDrop(item)"
                        class="b-margin-10 market-item">
                    <div class="flex-1 border all-padding-16 border-radius-4 pointer justify-between top-bottom-center">
                        <div class="display-flex b-padding-24">
                            <div
                                class="name-item text-ellipsis flex-1 font-weight-500  font-first-title-active r-padding-5">
                                {{ item.name }}
                            </div>
                            <div class="icon-box border-radius-4 display-flex left-right-center top-bottom-center"
                                 :class="hoverMarketId == item.id ? 'back-color-blue' : ''">
                                <Icon icon="icon-icon_city" size="20px"
                                      :color="hoverMarketId == item.id ? commonColor.mainWhite : commonColor.mainBlue">
                                </Icon>
                            </div>
                        </div>
                        <div class="display-flex menu-box t-padding-12">
                            <div class="flex-1">
                                <el-tooltip effect="dark" content="使用" placement="top">
                                    <Icon icon="icon-a-huaban264" class="r-margin-35" size="20px"
                                          :color="hoverMarketId == item.id ? commonColor.mainBlue : commonColor.mainBlack">
                                    </Icon>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="编辑" placement="top">
                                    <Icon icon="icon-a-huaban321" size="20px"
                                          :color="hoverMarketId == item.id ? commonColor.mainBlue : commonColor.mainBlack"
                                          @click.stop="editTemplete(item)"></Icon>
                                </el-tooltip>
                            </div>
                            <div class="flex-1 display-flex justify-flex-end">
                                <el-tooltip effect="dark" content="分享" placement="top">
                                    <Icon icon="icon-fenxiang"  class="r-margin-24" size="20px"
                                          :color="hoverMarketId == item.id ? commonColor.mainBlue : commonColor.mainBlack"
                                          @click.stop="shareTemplete(item)"></Icon>
                                </el-tooltip>
                                <Icon icon="icon-icon_delete" size="20px"
                                      :color="hoverMarketId == item.id ? commonColor.mainRed : commonColor.mainBlack"
                                      @click.stop="delTemplete(item)"></Icon>
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>

        <div v-if="!saveModelList.length" class="flex-grow-1 top-bottom-center">
            <el-empty />
        </div>
        <div class="page-box display-flex justify-flex-end tb-padding-10">
            <el-pagination @change="getUserSaveTemplate()" v-model:page-size="pageInfo.pageSize"
                           :page-sizes="[10, 30, 50, 100]" v-model:current-page="pageInfo.page"
                           layout="total,sizes,prev, pager, next" :total="pageInfo.total" />
        </div>
    </div>
    <TemplateShareDialog 
        v-if="templateShareDialogVisible"
        v-model:visible="templateShareDialogVisible"
        :templateInfo="targetTemplateInfo"/>
</template>

<script lang='ts' setup>
import { ref, onMounted, onUpdated, onUnmounted, getCurrentInstance, reactive } from 'vue'
import { useRouter } from 'vue-router'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'

import { ElMessageBox, ElMessage } from 'element-plus'
import type { ISearchGetTemplateItem, ISearchGetTemplateResponse } from '@/types/company'
import TemplateShareDialog from '@/views/find-companies/search/components/TemplateShareDialog.vue'

const saveModelList: Ref<ISearchGetTemplateItem[]> = ref([])
const hoverMarketId = ref('')
const instance = getCurrentInstance()
const commonColor = instance?.appContext.config.globalProperties.$commom.color
const router = useRouter()
const pageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})
const templateShareDialogVisible = ref(false)
const targetTemplateInfo=ref<ISearchGetTemplateItem | null>(null)

const startTemplete: Ref<ISearchGetTemplateItem> = ref({} as ISearchGetTemplateItem)


const dragstart = (templateItem: ISearchGetTemplateItem) => {
    startTemplete.value = templateItem
}
const onDrop = (templateItem: ISearchGetTemplateItem) => {
    aicService.searchUpdateTemplate({
        sort: templateItem.sort,
        templateId: startTemplete.value.id
    }).then(() => {
        getUserSaveTemplate()
    })
}


const useTemplete = (item: ISearchGetTemplateItem) => {
    router.push({
        name: 'more-search-company',
        params: {
            templeteId: item.id,
            type: 'save'
        },
    })
}

const getUserSaveTemplate = () => {
    aicService.searchGetTemplate({
        searchType: '2',
        page: pageInfo.page,
        pageSize: pageInfo.pageSize
    }).then((res: ISearchGetTemplateResponse) => {
        if (res.errCode) {
            saveModelList.value = []
            ElMessage({
                type: 'error',
                message: '获取用户保存的模板失败',
            })
            return
        }
        saveModelList.value = res.data
        pageInfo.total = res.total
    })
}

const editTemplete = (item: ISearchGetTemplateItem) => {
    ElMessageBox.prompt('请输入模板名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^(?!\s*$).+/,
        inputErrorMessage: '模板名称不能为空',
        inputValue: item.name
    })
        .then(({ value }) => {
            //存模板逻辑
            aicService.searchUpdateTemplate({
                name: value,
                templateId: item.id
            }).then(() => {
                ElMessage({
                    type: 'success',
                    message: '保存成功',
                })
                item.name = value
            })
            console.log(value)
        })
        .catch(() => { })
}

const shareTemplete = (item: ISearchGetTemplateItem) => {
    targetTemplateInfo.value = item
    templateShareDialogVisible.value = true
}

const delTemplete = (item: ISearchGetTemplateItem) => {
    ElMessageBox.confirm('是否要删除模板?', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            aicService.searchDeleteTemplate(item.id).then(() => {

                pageInfo.page = 1
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
                getUserSaveTemplate()
            })
        })
        .catch(() => { })
}


onMounted(() => { getUserSaveTemplate() })
onUpdated(() => { })
onUnmounted(() => { })
</script>

<style lang='scss' scoped>
.market-item {
    height: 170px;

    .name-item {
        height: 70px;
    }

    &:hover {
        border-color: var(--main-blue-);
    }

    .icon-box {
        width: 40px;
        height: 40px;
        box-shadow: 0px 2px 4px rgb(0, 0, 0, 0.1);
    }

    .menu-box {
        border-top: 1px solid var(--border-color);
    }
}

.font-first-title-active {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 3;
    text-overflow: ellipsis;
    line-height: 1.5;
    height: 50px;
    overflow: hidden;
}

.page-box {
    height: 50px;
}
</style>