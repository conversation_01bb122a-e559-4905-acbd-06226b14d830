<script lang="ts" setup>
// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================

// ====================== Methods ======================

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-row lr-padding-14 top-bottom-center">
        <slot></slot>
    </div>
</template>

<style lang="scss" scoped></style>
