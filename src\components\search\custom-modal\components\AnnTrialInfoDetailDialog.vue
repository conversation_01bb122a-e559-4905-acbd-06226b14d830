<script lang="ts" setup>
import { ref, defineProps, getCurrentInstance, onBeforeMount } from 'vue'

const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    },
})

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const dialogVisible = ref(false)

onBeforeMount(() => {
    console.log('props', props.row)
})

const handleOpenDetail = () => {
    dialogVisible.value = true
}

</script>

<template>
    <span class="pointer !color-blue" @click="handleOpenDetail()">查看详情</span>
    <el-dialog v-model="dialogVisible" title="开庭公告详情" append-to-body style="height: 600px; overflow-y: auto">
        <div class="font-14 color-two-grey">
            <div class="b-margin-24">
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >案号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.caseNum || '-'}}</el-col>

                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >开庭日期
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ moment(row.contentDate).format('YYYY-MM-DD') || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >开庭地址</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        row?.trialLocation || '-' }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >审判法院
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        row?.court || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >案件类型</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row?.caseType || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >案由</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row?.caseTypeDesc || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >起诉方</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{  row?.accuser ? JSON.parse(row?.accuser).join('；') : '-'  }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >被诉方</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{  row?.accused ? JSON.parse(row?.accused).join('；') : '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >第三方</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row.thirdParty ? JSON.parse(row?.thirdParty).join('；') : '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >刊登封面</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row?.publishedEdition || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >公告内容</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row?.content || '-' }}
                    </el-col>
                </el-row>
            </div>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
