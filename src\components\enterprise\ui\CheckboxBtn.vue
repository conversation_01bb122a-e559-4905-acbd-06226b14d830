<script lang="ts" setup>
const props = defineProps<{
    onChange: (value: string) => void
    checked: boolean
}>()
const onCheck = () => {
    props.onChange('unlimited')
}
</script>

<template>
    <div
        :class="{
            '!border-color-blue': checked,
            'back-color-three-blue': checked,
            '!color-blue': checked,
        }"
        class="flex top-bottom-center left-right-center w-40 h-22 font-12 color-black border-radius-4 pointer border-trans no-select lh-22"
        @click="onCheck"
    >
        不限
    </div>
</template>

<style lang="scss" scoped></style>
