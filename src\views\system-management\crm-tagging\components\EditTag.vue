<template>
    <el-dialog
        v-model="editTagDialogVisible"
        title="编辑"
        width="504"
        @close="handleClose"
    >
        <div class="t-margin-8">
            <div class="display-flex justify-between">
                <div class="font-16 color-red r-margin-6">*</div>
                <div class="font-16 color-two-grey b-margin-16">标签名称</div>
            </div>
            <el-input class="b-margin-16" v-model="tagName" placeholder="请输入标签名称" style="width: 100%;" maxlength="10"></el-input>
            <div class="display-flex justify-between">
                <div class="font-16 color-red r-margin-6">*</div>
                <div class="font-16 color-two-grey b-margin-16">标签颜色</div>
            </div>
            <div class="lr-padding-16">
                <a v-for="item in colors" :key="item.id" :value="item.color" class="r-margin-16">
                    <el-tag
                        class="pointer no-border"
                        style="width: 28px;height: 28px;"
                        :color="item.color"
                        @click="handleTagClick(item.id)"
                        round
                    >
                        <Icon v-if="item.id === selectedTag" color="#fff" icon="icon-a-huaban74" size="large"></Icon>
                        <!-- <el-icon v-if="item.id === selectedTag" color="#fff" class="display-flex justify-center align-center" size="large"><Check /></el-icon> -->
                    </el-tag>
                </a>
            </div>
        </div>
        <div class="display-flex justify-flex-end all-padding-10 t-margin-20">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="loading" >确 定</el-button>
        </div>
    </el-dialog>


</template>

<script lang='ts' setup>

import { ref, onMounted, watch , computed } from 'vue'
import type { IGetTagList, IUpdateTagParams } from '@/types/lead'
import { ElMessage } from 'element-plus'
import crmService from '@/service/crmService'
import Icon from '@/components/common/Icon.vue'

// const TagParams = ref<IAddTagParams>({
//     tagName: '',
//     color: ''
// }
// )
const updateParams = ref<IUpdateTagParams>({
    id: '',
    tagName: '',
    color: ''
})
const props = defineProps<{
    visible: boolean,
    editRowData:IGetTagList
    refresh: () => void;
}>()

watch(() => props.editRowData, (val) => {
    console.log('editRowData',val)
    if (val){
        tagName.value = val.tagName
        selectedTag.value = colors.find(item => item.color === val.color)!.id
    }
})

const emit = defineEmits(['update:visible'])
const editTagDialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const colors = [
    {
        id: 1,
        color: '#7ACCCC'
    }, 
    {
        id: 2,
        color: '#7AA4CC'
    },
    {
        id: 3,
        color: '#4F73DE'
    },
    {
        id: 4,
        color: '#9977D4'
    },
    {
        id: 5,
        color: '#AA9F70'
    },
    {
        id: 6,
        color: '#7AAC4E'
    },
    {
        id: 7,
        color: '#CF7CC5'
    },
    {
        id: 8,
        color: '#C8C985'
    },
    {
        id: 9,
        color: '#DF974E'
    },
    {
        id: 10,
        color: '#E84838'
    }
]

const tagName = ref(props.editRowData.tagName)
// selectedTag用来选择颜色
const selectedTag = ref(colors.find(item => item.color === props.editRowData.color)!.id)
const loading = ref(false)
const handleTagClick = (id: number) => {
    selectedTag.value = id
}
 
const handleClose = () => {
    cancel()
}
const cancel = () => {
    editTagDialogVisible.value = false
}

const confirm = () => {
    loading.value = true
    // 添加编辑的逻辑
    console.log(tagName.value, selectedTag.value,props.editRowData)
    if(!tagName.value){
        ElMessage.error('标签名称不能为空')
    }else{
        updateParams.value.id = props.editRowData.id
        updateParams.value.tagName = tagName.value
        updateParams.value.color = colors.find(item => item.id === selectedTag.value)!.color
        console.log(updateParams.value)
        crmService.crmTagUpdate(updateParams.value).then((res) => {
            if (res.success) {
                ElMessage.success('修改成功')
                editTagDialogVisible.value = false
                props.refresh()
            } else{
                ElMessage.error(res.errMsg)
            }
        })
    }
    loading.value = false
}

onMounted(() => {
})
</script>

<style lang='scss' scoped>
.no-border {
    border: none;
}
</style>