<script lang="ts" setup>
import aicService from '@/service/aicService'
import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams, Region } from '@/types/aic'
import type { RootState } from '@/types/store'
import { computed, onMounted, provide, ref, watch } from 'vue'
import { useStore } from 'vuex'
import RegionCascadeSelect from '../region/RegionCascadeSelect.vue'

// ====================== Interfaces & Types ======================
export interface ParentMethods {
    postSelectedRegions: (regions: Region[]) => void
}

// ====================== Props & Injections ======================
const props = defineProps<{
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
}>()

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

const isUnlimited = computed(() => {
    const list = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    return list.length === 0
})

// ====================== Refs & Reactive State ======================
const config = ref<IAicConditionData | null>(null)
const regionCascadeSelectRef = ref<{
    reset: () => void
}>()

const selectedRegions = ref<Region[]>([])

// ====================== Methods ======================
const setUnlimited = () => {
    regionCascadeSelectRef.value?.reset()
}

const handlePostRegions = (regions: Region[]) => {
    selectedRegions.value = regions
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
    }
}

// ====================== Parent Methods ======================
const parentMethods: ParentMethods = {
    postSelectedRegions: (region: Region[]) => handlePostRegions(region),
}

provide<ParentMethods>('parentMethods', parentMethods)

// ====================== Watchers ======================
watch(
    () => isUnlimited.value,
    (value) => {
        if (value) {
            setUnlimited()
        }
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getStaticConfig()
})
</script>

<template>
    <div class="flex top-bottom-center">
        <div class="flex flex-row top-bottom-center">
            <div class="w-112">
                <div class="lh-24 font-16 color-black">{{ props.data.name }}：</div>
            </div>
            <div class="flex flex-row top-bottom-center gap-24 flex-wrap flex-1">
                <CheckboxBtn :onChange="setUnlimited" :checked="isUnlimited" />
                <RegionCascadeSelect
                    ref="regionCascadeSelectRef"
                    :regions="config?.area || []"
                    :store-params="storeParams"
                    :data="data"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
