<template> 
    <div style="background-color: #f7f7f7; ">
        <div style="margin-bottom: 16px; background-color: #fff; padding: 16px">
            <div class=" display-flex top-bottom-center"> 
                <div class="font-16">项目名称： </div>
                <!-- 搜索框 -->
                <el-input
                    placeholder="请输入项目名称"
                    v-model="input"
                    style="width: 412px"
                    @keyup.enter="search"
                    clearable
                >
                </el-input>      
            </div>
            <div class="display-flex justify-flex-end all-padding-10 t-margin-20">
                <el-button class="font-16" @click="cancel">清 空</el-button>
                <el-button class="font-16" type="primary" @click="search" style="background-color: #1966FF;">搜 索</el-button>
            </div>
        </div>
        <div class="t-margin-10"  style="background-color: #fff; padding: 16px; box-sizing: border-box" >
            <div class="display-flex justify-flex-end b-margin-10">
                <el-button type="primary" @click="handleAdd">新增</el-button>
            </div>
            <el-table 
                :data="ProjectTable" 
                v-loading="tableLoading"
                empty-text="暂无数据" 
                style="width: 100%"
                show-overflow-tooltip
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"    
            >
                <el-table-column label="项目名称" prop="name" style="min-width: 300"></el-table-column>
                <el-table-column label="创建时间" prop="createTime" style="min-width: 450px" sortable>
                    <template #default="scope">
                        {{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
                    </template>
                </el-table-column>
                <el-table-column label="项目简称" prop="shortName" style="min-width: 450px"></el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="150px"
                >
                    <template #default="scope">
                        <div>
                            <a class="r-margin-8 pointer color-blue" @click="copy(scope.row)">复制</a>
                            <a class="r-margin-8 pointer color-blue" @click="edit(scope.row)">编辑</a>
                            <a class="pointer color-blue" @click="confirmRemove(scope.row)">删除</a>
                        </div>  
                    </template>
                </el-table-column>
            </el-table>
            <el-affix position="bottom" :offset="0">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
        <el-dialog v-model="addDialogVisible" title="新增" style="width: 420px;">
            <div>
                <div class="display-flex top-bottom-center b-margin-10">
                    <div class="color-red font-24">*</div>
                    <div class="r-margin-4">项目名称</div>
                    <el-input v-model="addProjectName" style="width: 300px;" placeholder="请输入项目名称"></el-input>
                </div>
                <div class="display-flex top-bottom-center l-margin-10 b-margin-10">
                    <div class="r-margin-4">项目简称</div>
                    <el-input v-model="addProjectShorname" style="width: 300px;" placeholder="请输入项目简称"></el-input>
                </div>
            </div>
            <div class="display-flex justify-flex-end ">
                <el-button @click="cancelAdd">取 消</el-button>
                <el-button type="primary" @click="confirmAdd" style="background-color: #1966FF;">确 定</el-button>
            </div>
        </el-dialog>
    </div>

</template>

<script lang="ts" setup> 
import type { CatagoryItem, ISearchListParams, AddProjectParams} from '@/types/aic'
import { ref,reactive,onMounted, getCurrentInstance } from 'vue'
import { ElMessage,ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import aicService from '@/service/aicService'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const router = useRouter()
const input = ref('')
const tableLoading = ref(false)
const ProjectTable = ref<CatagoryItem[]>([])
const addDialogVisible = ref(false)
const addProjectName = ref('')
const addProjectShorname = ref('')
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})
const AddParams = ref<AddProjectParams>({
    name: '',
    shortName: '',
})
const ProjectParams = ref<ISearchListParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
})

const pageChange = (currentPage: number, currentPagesize: number) => {
    console.log('currentPage:', currentPage)
    console.log('currentPagesize:', currentPagesize)
    ProjectParams.value.page = currentPage
    ProjectParams.value.pageSize = currentPagesize
    getProjectInfo(ProjectParams.value)
}

const getProjectInfo = (Params:ISearchListParams) => {
    tableLoading.value = true
    if(Params.name === ''){
        delete Params.name
    }
    aicService.modelGetCategoryList(Params).then(response => {
        ProjectTable.value = response.data
        tableLoading.value = false
        totalNum.value = response.total
    }).catch(error => {
        console.log(error)
        tableLoading.value = false 
    }) 
}
const search = () => {
    ProjectParams.value.name = input.value
    getProjectInfo(ProjectParams.value)
    console.log(input.value)
}

const handleAdd = () => {
    addDialogVisible.value = true
    console.log('新增')
}

const cancel = () => {
    input.value = ''
    ProjectParams.value.name = input.value
    getProjectInfo(ProjectParams.value)
}

const copy = (row: CatagoryItem) => {
    console.log('复制',row)
    ElMessageBox.confirm('确定要复制该项目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        tableLoading.value = true
        aicService.modelCopyCategory(row.id).finally(() => {
            getProjectInfo(ProjectParams.value)
        })
    })
}

const edit = (row: CatagoryItem) => {
    console.log('编辑',row.id)
    router.push({ name: 'search-model-category-detail', params: { id: row.id } })
}

const confirmRemove = ( row:CatagoryItem ) => {
    ElMessageBox.confirm('确定要删除该项目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        deleteRow(row)
    })
}

const deleteRow = async (row:CatagoryItem) => {
    console.log('删除的id',row.id)
    const res = await aicService.modelDeleteTopCategory(row.id)
    if(res.success){
        ElMessage.success('删除成功')
        ProjectParams.value.page = pageInfo.page
        getProjectInfo(ProjectParams.value)
    }else{
        ElMessage.error(`${res.errMsg}`)
    }
}

const cancelAdd = () => {
    addDialogVisible.value = false
}

const confirmAdd = async() => {
    if(addProjectName.value === '' || addProjectName.value === null){
        ElMessage.error('请输入项目名称')
    }else{
        AddParams.value.name = addProjectName.value
        AddParams.value.shortName = addProjectShorname.value
        // addDialogVisible.value = false
        // console.log('确认新增',AddParams.value)
        try{
            const res =await aicService.modelNewCategory(AddParams.value)
            console.log(res)
            if(res.success){
                ElMessage.success('新增成功')
                ProjectParams.value.page = pageInfo.page
                getProjectInfo(ProjectParams.value)
            }else{
                ElMessage.error(`${res.errMsg}`)
            }
        }catch(error){
            console.log(error)
        }finally{
            addDialogVisible.value = false
            addProjectName.value = ''
            addProjectShorname.value = ''
        }
    }
}

onMounted(() => {
    getProjectInfo(ProjectParams.value)
})

</script>


<style lang='scss' scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>