<script lang="tsx" setup>
import { Menu } from './components/help'
</script>

<template>
    <el-popover placement="bottom" popper-style="padding: 0">
        <template #reference>
            <div class="flex top-bottom-center gap-4 pointer">
                <div class="border-radius-16 w-16 h-16 flex-center">
                    <Icon icon="icon-a-lujing1" class="fill-two-grey" />
                </div>
                <div class="font-header-label color-two-grey">帮助</div>
            </div>
        </template>
        <template #default>
            <Menu />
        </template>
    </el-popover>
</template>

<style lang="scss" scoped></style>
