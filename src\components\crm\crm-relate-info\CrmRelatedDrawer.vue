<template>
    <el-drawer v-if="drawerVisible" v-model="drawerVisible" size="80%" :with-header="false" @close="handleClose">
        <div class="w-20 h-20" style="position: absolute; cursor: pointer; right: 18px; top: 20px; z-index: 1;" @click="handleClose">
            <el-icon :size="20" color="var(--two-grey)" ><Close @click="handleClose"/></el-icon>
        </div>
        <el-tabs v-model="activeName">
            <el-tab-pane label="上游企业" name="up">
                <RelateCompanyPage v-if="activeName === 'up'" :from = "'up'" :companyInfo="companyInfo"></RelateCompanyPage>
            </el-tab-pane>
            <el-tab-pane label="下游企业" name="down">
                <RelateCompanyPage v-if="activeName === 'down'" :from = "'down'" :companyInfo="companyInfo"></RelateCompanyPage>
            </el-tab-pane>
        </el-tabs>
    </el-drawer>
</template>

<script lang="ts" setup>
import { ref, watch, defineEmits, defineProps } from 'vue'
import RelateCompanyPage from './RelateCompanyPage.vue'
import type { ILeadData } from '@/types/lead'

const props = defineProps<{
    drawer: boolean,
    companyInfo?: ILeadData
}>()

const drawerVisible = ref(props.drawer)
const activeName = ref('up')
const emit = defineEmits(['update:drawer'])
const handleClose = () => {
    drawerVisible.value = false
    emit('update:drawer', false)
}
watch(
    () => props.drawer,
    (newVal: boolean) => {
        drawerVisible.value = newVal
    }
)
</script>
