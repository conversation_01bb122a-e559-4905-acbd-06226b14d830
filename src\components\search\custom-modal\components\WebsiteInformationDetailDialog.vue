<script lang="ts" setup>
import { ref, inject, watch } from 'vue'
import aicService from '@/service/aicService'
import type { Ref } from 'vue'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IGetWebsiteDetailInfo, IGetGsGetDetailParams, IDomainKeywords } from '@/types/aic'
import { parseTime } from '@/utils/parse-time'
import Icon from '@/components/common/Icon.vue'
const props = defineProps<{
    row: IGetModelCategoryResponse
}>()

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const dialogVisible = ref(false)
const websiteInfoDetail = ref<IGetWebsiteDetailInfo>()
const loading = ref(false)
const pageList = [
    {
        label: '全部',
        value: 0
    },
    {
        label: '第1页',
        value: 1
        
    },
    {
        label: '第2页',
        value: 2
    },
    {
        label: '第3页',
        value: 3
    },
    {
        label: '第4页',
        value: 4
    },
    {
        label: '第5页及以后',
        value: 5
    }
]
const selectPage = ref(0)
const domainKeywordsData = ref({})

const handleOpenDetail = () => {
    dialogVisible.value = true
    aicService
        .getGsGetDetail({
            id: props.row._id,
            moduleName: 'websiteInfo',
            socialCreditCode: socialCreditCode.value,
        })
        .then((res) => {
            console.log('res', res)
            if (res.errCode === 0) {
                websiteInfoDetail.value = res.data as IGetWebsiteDetailInfo
                domainKeywordsData.value = res.data.domainKeywords
            }
        })
        .catch(() => {
            dialogVisible.value = false
        })
}
const openWebSite = (val: string) => {
    // 自动补全协议
    let url = val.trim()
    if (!/^https?:\/\//i.test(url)) {
        url = 'https://' + url
    }

    // 可选：增加 URL 合法性校验
    try {
        new URL(url)
    } catch (e) {
        console.warn('无效的链接地址', e)
        return
    }

    window.open(url, '_blank')
}
const queryParams = ref<IGetGsGetDetailParams>({
    id: props.row._id,
    moduleName: 'websiteInfo',
    page: 1,
    section:'domainKeywords'
})
const getDomainKeywords = () => {
    aicService
        .getGsGetDetail(queryParams.value)
        .then((res) => {
            const { data } = res || {}
            const { domainKeywords } = data as IDomainKeywords || {}
            domainKeywordsData.value = domainKeywords || {}
        })
}
watch(() => selectPage.value, (newVal) => {
    queryParams.value.rankPage = newVal
    getDomainKeywords()
})
</script>
<template>
    <span class="pointer !color-blue" @click="handleOpenDetail()">查看详情</span>
    <el-dialog v-model="dialogVisible" title="网站详情" append-to-body style="height: 600px; overflow-y: auto">
        <div class="font-14 color-two-grey">
            <!-- 网站安全性指标 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >网站安全性指标</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >虚假或欺诈网站监控</el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        websiteInfoDetail?.webSecureIndex?.baiduCheat || '-'}}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >挂马或恶意网站监控
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        websiteInfoDetail?.webSecureIndex?.baiduFalsified || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >违法内容网站监控</el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        websiteInfoDetail?.webSecureIndex?.baiduIllegal || '-' }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >被篡改监控
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        websiteInfoDetail?.webSecureIndex?.baiduTrojan || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >死链数</el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webSecureIndex?.deadLinkCount || 0
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >非法链接数
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webSecureIndex?.illegalLinkCount || 0
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >有无HTTPS证书</el-col>
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ websiteInfoDetail?.webSecureIndex?.hasSsl || '-' }}
                    </el-col>
                </el-row>
            </div>

            <!-- 网站模块 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >网站模块</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >网站功能</el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{  websiteInfoDetail?.webModule?.webFeature || '-'}}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >有无FLASH模块
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.hasFlashModule || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >在线沟通工具</el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.onlineChat || '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >建站方
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >技术语言</el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.devLanguage || '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >是否响应式技术网站
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.isRespTech || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >有无手机端网站</el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.hasMobileSite || '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >平均访问速度
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.websiteSpeed || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >网站结构更新时间</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.lastChangeDate
                            ? parseTime(websiteInfoDetail?.webModule?.lastChangeDate, '{y}-{m}-{d}')
                            : '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >备案年龄
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.webModule?.recordAge || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >W3C标准偏离值
                        <el-tooltip
                            class="box-item"
                            effect="light"
                            content="代表网站规范性，1-5属于正常范围"
                            placement="top"
                        >
                            <Icon icon="icon-a-tishi1" size="16" color="#d9d9d9" class="pointer"></Icon>
                        </el-tooltip>
                    </el-col>
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ '-' }}
                    </el-col>
                </el-row>
            </div>

            <!-- 网站SEO指标 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >网站SEO指标</el-col
                    >
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >百度权重PC端</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.seo?.weightPcBaidu || 0
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >百度总收录量
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.seo?.totalCollectionBaidu || 0
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >360权重PC端</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.seo?.weightPcQihoo || 0
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >360总收录量
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.seo?.totalCollectionQihoo || 0
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >搜狗权重PC端</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.seo?.weightPcSougou || 0
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >搜狗总收录量
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        websiteInfoDetail?.seo?.totalCollectionSougou || 0
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >ALEXA排名</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ websiteInfoDetail?.seo?.RANK || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >标题</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ websiteInfoDetail?.seo?.seoTitle || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >关键词</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ websiteInfoDetail?.seo?.seoKeyWords || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >描述</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ websiteInfoDetail?.seo?.seoDescription || '-' }}
                    </el-col>
                </el-row>
            </div>

            <!-- 网站IP信息 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >网站IP信息</el-col
                    >
                </el-row>
                <el-table border v-loading="loading" :data="websiteInfoDetail?.ipInfo?.items || []">
                    <el-table-column prop="domain" label="网址">
                        <template #default="{ row }">
                            <div class="!color-blue pointer" @click="openWebSite(row.domain)">{{ row.domain }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="ip" label="IP"></el-table-column>
                    <el-table-column prop="serverPhysicNode" label="IP物理位置"></el-table-column>
                    <el-table-column prop="serviceNode" label="IP运营节点"></el-table-column>
                </el-table>
            </div>

            <!-- 域名关键词 -->
            <div class="b-margin-24">
                <el-row justify="space-between">
                    <el-col :span="24" class="all-padding-16 display-flex space-between top-bottom-center" style="border: 1px solid var(--border-color)">
                        <div>域名关键词</div>
                        <div>
                            <el-select v-model="selectPage" size="small">
                                <el-option
                                    v-for="item in pageList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </el-col>
                </el-row>
                <el-table border v-loading="loading" :data="domainKeywordsData?.items">
                    <el-table-column prop="keyword" label="关键词"></el-table-column>
                    <el-table-column prop="rank" label="排名"></el-table-column>
                    <el-table-column prop="platform" label="平台"></el-table-column>
                    <el-table-column prop="type" label="类型"></el-table-column>
                </el-table>
            </div>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
