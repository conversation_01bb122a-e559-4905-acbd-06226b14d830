<script lang="ts" setup>
// import systemService from '@/service/systemService'
import type { IAutoDialerFilter, IAutoDialerRequest } from '@/types/autoDialer'
// import type { IRoleItem } from '@/types/role'
import type { FormInstance } from 'element-plus'
import { reactive, ref, watch } from 'vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    getData: (params: IAutoDialerRequest) => void
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const options = [
    { label: '未开始', value: 1 },
    { label: '进行中', value: 2 },
    { label: '已完成', value: 3 },
    { label: '已终止', value: 4 },
    { label: '排队中', value: 5 },
    { label: '手动暂停', value: 6 },
    { label: '自动暂停', value: 7 },
    { label: '已过期', value: 8 },
]
const formRef = ref<FormInstance>()
const date = ref('')

const queryForm = reactive<IAutoDialerFilter>({
    taskName: '',
    status: '',
    date: [],
})

// ====================== Methods ======================
const onSubmit = () => {
    console.log('queryForm', queryForm)

    const params: IAutoDialerRequest = {
        taskName: queryForm.taskName,
        status: queryForm.status,
    }

    if (queryForm.date && Array.isArray(queryForm.date) && queryForm.date.length === 2) {
        params.startTime = queryForm.date[0]
        params.endTime = queryForm.date[1]
    }

    props.getData(params)
}

const reset = (formEl?: FormInstance) => {
    formEl?.resetFields()
    onSubmit()
}

watch(
    () => date.value,
    (val) => {
        console.log('queryForm.taskName', val)
    }
)

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <el-form ref="formRef" :inline="true" :model="queryForm" :label-width="112" label-suffix=":">
        <div class="flex flex-row filter flex-wrap">
            <el-form-item label="任务名称" label-position="left" prop="taskName">
                <el-input v-model="queryForm.taskName" placeholder="请输入任务名称" clearable style="width: 400px" />
            </el-form-item>
            <el-form-item label="创建时间" label-position="left" prop="date">
                <el-date-picker
                    v-model="queryForm.date"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="common-width"
                    style="width: 400px"
                />
            </el-form-item>
            <el-form-item label="任务状态" label-position="left" prop="status">
                <el-select
                    v-model="queryForm.status"
                    clearable
                    placeholder="请选择任务状态"
                    style="width: 400px"
                    @change="onSubmit"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                >
                    <el-option
                        v-for="option in options"
                        :label="option.label"
                        :value="option.value"
                        :key="option.value"
                    />
                </el-select>
            </el-form-item>
        </div>
        <div class="flex flex-row gap-16 justify-end filter">
            <el-form-item>
                <el-button @click="reset(formRef)">清空</el-button>
                <el-button type="primary" @click="onSubmit">搜索</el-button>
            </el-form-item>
        </div>
    </el-form>
</template>

<style lang="scss" scoped></style>
