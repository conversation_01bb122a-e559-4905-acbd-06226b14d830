<script lang="ts" setup>
import { computed } from 'vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    label: string
    required?: boolean
    size?: 'small'
    position?: 'top'
}>()
// ====================== Store & Computed Values ======================
const requiredTxt = computed(() => {
    return props.required ? ' *' : ''
})

// ====================== Refs & Reactive State ======================
// ====================== Methods ======================

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div
        class="flex"
        :class="{
            'flex-column': position === 'top',
            'gap-40': position !== 'top',
            'flex-row': position !== 'top',
        }"
    >
        <div
            class="flex"
            :class="{
                'w-120': !size,
                'maxw-120': !size,
                'flex-1': !size,
            }"
        >
            <div
                class="flex flex-row"
                :class="{
                    'lh-44': position !== 'top',
                    'h-44': position !== 'top',
                }"
            >
                <div class="w-8 color-red">
                    {{ requiredTxt }}
                </div>
                <div>{{ label }}</div>
            </div>
        </div>
        <slot></slot>
    </div>
</template>

<style lang="scss" scoped>
.el-checkbox.el-checkbox--large {
    height: auto;
}
</style>
