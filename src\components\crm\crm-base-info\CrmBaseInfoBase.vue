<template>
    <el-descriptions class="margin-top" :column="2" border :label-class-name="'base-info-label'">
        <el-descriptions-item label-class-name="base-info-label" :span="2">
            <template #label>
                <div class="cell-item">公司名称</div>
            </template>
            {{ crmDetail.companyName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label" :span="2">
            <template #label>
                <div class="cell-item">公司地址</div>
            </template>
            {{ crmDetail.customFields?.address || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">公司税号</div>
            </template>
            {{ crmDetail.socialCreditCode }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">组织</div>
            </template>
            {{ crmDetail.departmentName || '-' }}
        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang='ts' setup>
import { onMounted, inject } from 'vue'
import type { Ref } from 'vue'

import type { ILeadData } from '@/types/lead'

const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

onMounted(() => { })
</script>

<style lang='scss' scoped>
:deep(.base-info-label) {
    background: var(--table-header-bg-) !important;
}
</style>