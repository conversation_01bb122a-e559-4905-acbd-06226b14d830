.password-form {
    flex: 1;
    padding: 80px 50px;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
}

.title {
    font-size: var(--text-3xl);
    font-weight: var(--weight-700);
    color: var(--main-blue-);
    text-align: center;
}

.input {
    height: 52px;
}

.input :deep(.el-input__inner) {
    font-size: 18px;
    padding: 24px 12px;
}

.help {
    display: flex;
    justify-content: flex-end;
    font-size: var(--text-base);
    font-weight: var(--weight-400);
    line-height: var(--text-2xl);
    color: var(--four-grey);
    text-align: left;
    vertical-align: top;
    margin-bottom: 34px;
    z-index: 2;
}
.submit-btn {
    width: 100%;
    height: 54px;
    opacity: 1;
    border-radius: var(--text-3xl) !important;
    background: var(--main-blue-);
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    font-size: var(--text-xl);
    font-weight: var(--weight-500);
    color: var(--main-white);
    text-align: left;
    vertical-align: top;
    border: none;
}
.tips {
    flex: 9;
    font-size: var(--text-base);
    font-weight: var(--weight-400);
    letter-spacing: 0px;
    color: var(--four-grey);
    text-align: left;
    vertical-align: top;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    margin-top: 120px;
    gap: 8px;
}

.captcha-box {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    bottom: 0;
    right: 0;
    background-color: var(--main-white);
}
.captcha {
    position: absolute;
    width: 100%;
    display: flex;
    justify-content: center;
}

.form {
    position: relative;
    display: flex;
    flex-direction: column;
}

.login-btn {
    width: 100%;
    height: 54px;
    opacity: 1;
    border-radius: var(--text-3xl) !important;
    font-size: var(--text-xl);
    font-weight: var(--weight-400);
    line-height: var(--text-4xl);
    color: var(--main-white);
    vertical-align: top;
    color: var(--two-grey);
    margin-top: 16px;
}

.code-btn {
    background-color: var(--main-blue-);
    border: none;
    width: 100%;
    height: 52px;
    font-size: 18px;
}

.step,
.steps {
    display: flex;
    flex-direction: row;
}

.steps {
    justify-content: space-around;
    align-items: center;
    padding: 39px 0px;
    margin-bottom: 28px;
}

.step {
    justify-content: center;
    align-items: center;
    gap: 8px;
    font-size: var(--text-base);
    line-height: var(--text-base);
}

.icon {
    width: 30px;
    height: 30px;

    border-radius: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--main-white);
}

// .label {
//     font-size: var(--text-base);
//     line-height: var(--text-base);
// }

.step-split {
    flex: 1;
    background-color: var(--border-color);
    height: 2px;
    margin: 0px 16px;
    max-width: 60px;
}

.step-bg {
    background-color: var(--second-blue);
    color: var(--main-blue-);
    font-weight: 600;
}

.current-step-bg {
    background-color: var(--main-blue-);
    font-weight: var(--weight-500);
}

.password-form-item {
    margin-bottom: 34px;
}
.password-form-item:nth-child(2) {
    margin-bottom: 12px;
}

.phone-bond-step-label {
    font-size: 16px;
}

// @media screen and (max-width: 600px) {
//     .password-form {
//         padding: 2rem 1rem;
//     }

//     .title {
//         margin-bottom: 2rem;
//     }

//     .tips {
//         margin-top: 2rem;
//     }

//     .steps {
//         padding: 0px 40px;
//     }
// }

/* 自适应宽度 */
@media screen and (max-width: 1600px) {
    .password-form {
        padding: 66px 40px;
    }

    .steps {
        padding: 32px 40px;
    }

    .title {
        font-size: 22px; // 22
    }

    .input {
        height: 44px;
    }

    .input :deep(.el-input__inner) {
        font-size: 16px;
        padding: 20px 10px;
    }

    .password-form-item {
        margin-bottom: 28px;
    }

    .password-form-item:nth-child(2) {
        margin-bottom: 10px;
    }

    .help {
        font-size: 14px;
        line-height: 14px;
        margin-bottom: 26px;
    }

    .submit-btn,
    .login-btn {
        height: 44px;
        font-size: 16px;
    }

    .login-btn {
        margin-top: 14px;
    }

    .tips {
        font-size: 14px;
        margin-top: 116px;
        gap: 6px;
    }

    .code-btn {
        height: 44px;
        font-size: 16px;
    }
}

@media screen and (max-width: 1200px) {
    .password-form {
        padding: 50px 30px;
    }

    .steps {
        padding: 24px 0px;
    }

    .title {
        font-size: 16px; // 16
    }

    .input {
        height: 34px;
    }

    .input :deep(.el-input__inner) {
        font-size: 12px;
        padding: 16px 8px;
    }

    .password-form-item {
        margin-bottom: 20px;
    }

    .password-form-item:nth-child(2) {
        margin-bottom: 9px;
    }

    .help {
        font-size: 10px;
        line-height: 10px;
        margin-bottom: 20px;
    }

    .submit-btn,
    .login-btn {
        height: 34px;
        font-size: 12px;
    }

    .login-btn {
        margin-top: 12px;
    }

    .tips {
        font-size: 10px;
        margin-top: 88px;
        gap: 5px;
    }

    .code-btn {
        height: 34px;
        font-size: 12px;
    }

    .phone-bond-step-label {
        font-size: 14px;
    }

    .icon {
        width: 26px;
        height: 26px;
    }

    .step-bg,
    .current-step-bg {
        font-size: 16px;
    }
}

@media screen and (max-width: 992px) {
    .password-form {
        padding: 40px 24px;
    }

    .steps {
        padding: 20px 0px;
    }

    .title {
        font-size: 14px; // 14
    }

    .input {
        height: 29px;
    }

    .input :deep(.el-input__inner) {
        font-size: 10px;
        padding: 14px 7px;
    }

    .password-form-item {
        margin-bottom: 16px;
    }

    .password-form-item:nth-child(2) {
        margin-bottom: 8px;
    }

    .help {
        font-size: 8px;
        line-height: 8px;
        margin-bottom: 16px;
    }

    .submit-btn,
    .login-btn {
        height: 28px;
        font-size: 10px;
    }

    .login-btn {
        margin-top: 10px;
    }

    .tips {
        font-size: 8px;
        margin-top: 72px;
        gap: 4px;
    }

    .code-btn {
        height: 29px;
        font-size: 10px;
    }

    .phone-bond-step-label {
        font-size: 14px;
    }

    .icon {
        width: 14px;
        height: 14px;
    }

    .step-bg,
    .current-step-bg {
        font-size: 10px;
    }

    .step-split {
        height: 1px;
    }
}

@media screen and (max-width: 768px) {
    .password-form {
        padding: 30px 18px;
    }

    .steps {
        padding: 15px 0px;
    }

    .title {
        font-size: 10px; // 10
    }

    .input {
        height: 24px;
    }

    .input :deep(.el-input__inner) {
        font-size: 8px;
        padding: 10px 6px;
    }

    .password-form-item {
        margin-bottom: 12px;
    }

    .password-form-item:nth-child(2) {
        margin-bottom: 8px;
    }

    .help {
        font-size: 6px;
        line-height: 6px;
        margin-bottom: 12px;
    }

    .submit-btn,
    .login-btn {
        height: 22px;
        font-size: 8px;
    }

    .login-btn {
        margin-top: 10px;
    }

    .tips {
        font-size: 6px;
        margin-top: 56px;
        gap: 3px;
    }

    .code-btn {
        height: 24px;
        font-size: 6px;
    }

    .phone-bond-step-label {
        font-size: 10px;
    }

    .icon {
        width: 12px;
        height: 12px;
    }

    .step-bg,
    .current-step-bg {
        font-size: 10px;
    }

    .step-split {
        height: 1px;
    }
}

@media screen and (max-width: 576px) {
    .password-form {
        padding: 22px 16px;
    }

    .steps {
        padding: 11px 0px;
    }

    .title {
        font-size: 8px; // 8
    }

    .input {
        height: 18px;
    }

    .input :deep(.el-input__inner) {
        font-size: 6px;
        padding: 8px 4px;
    }

    .password-form-item {
        margin-bottom: 9px;
    }

    .password-form-item:nth-child(2) {
        margin-bottom: 6px;
    }

    .help {
        font-size: 4px;
        line-height: 4px;
        margin-bottom: 9px;
    }

    .submit-btn,
    .login-btn {
        height: 16px;
        font-size: 6px;
    }

    .login-btn {
        margin-top: 8px;
    }

    .tips {
        font-size: 4px;
        margin-top: 49px;
        gap: 3px;
    }

    .code-btn {
        height: 18px;
        font-size: 6px;
    }

    .phone-bond-step-label {
        font-size: 6px;
    }

    .icon {
        width: 8px;
        height: 8px;
    }

    .step-bg,
    .current-step-bg {
        font-size: 6px;
    }

    .step-split {
        height: 1px;
    }
}
