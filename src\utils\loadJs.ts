export const loadScript = (src: string, callback?: () => void) => {
    if (document.querySelector(`script[src="${src}"]`)) {
        console.log('脚本已加载')
        callback?.()
        return
    }

    const script = document.createElement('script')
    script.src = src
    script.async = true
    script.onload = () => {
        console.log('脚本加载完成:', src)
        callback?.()
    }
    script.onerror = () => {
        console.error('脚本加载失败:', src)
    }

    document.body.appendChild(script)
}
