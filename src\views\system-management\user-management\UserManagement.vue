<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7; ">
        <div ref="searchContentRef" style="margin-bottom: 16px; background-color: #fff; padding: 16px">
            <searchBox
                :searchOptionKey="'USER_MANAGEMENT_SEARCH_OPTIONS'"
                @updateSearchParams="updateSearchParams"
                :customConfig="userManagementOptionConifg"
            >
            </searchBox>
        </div>
        <div style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div ref="actionBarContentRef" class="display-flex top-bottom-center b-margin-16">
                <div class="color-three-grey font-14">已选{{ selectedLength }}</div>
                <div class="l-margin-10 font-16">
                    <el-button @click="disableSelected"> 批量停用 </el-button>
                </div>
                <div class="l-margin-10 font-16" >
                    <el-dropdown>
                        <el-button @click="exportUser" :loading="exporting">
                            导出
                        </el-button>
                    </el-dropdown>
                </div>
            </div>
            <el-table
                :data="userList"
                style="width: 100%"
                :height="tableHeight+'px'"
                empty-text="暂无数据"
                v-loading="tableLoading"
                @selection-change="handleSelectionChange"
                show-overflow-tooltip
                row-key="id"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column type="selection" width="55" reserve-selection fixed="left" />
                <el-table-column label="登录账号" prop="username" min-width="100" />
                <el-table-column label="联系人姓名" prop="nickname" min-width="100" />
                <el-table-column label="手机号码" prop="mobile" width="120" />
                
                <el-table-column label="状态" prop="status" width="90">
                    <template #default="scope">
                        <el-dropdown>
                            <el-tag class="pointer" :type="scope.row.status == 1 ? 'info' : 'success'">
                                <div class="flex flex-row gap-4 top-bottom-center">
                                    {{ scope.row.status === 1 ? '停用' : '启用' }} <el-icon><ArrowDown /></el-icon>
                                </div>
                            </el-tag>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="changeStatus(0, scope.row)" v-if="scope.row.status === 1">
                                        启用
                                    </el-dropdown-item>
                                    <el-dropdown-item @click="changeStatus(1, scope.row)" v-if="scope.row.status === 0">
                                        停用
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
                <el-table-column label="用户角色" prop="roleName" min-width="150" />
                <el-table-column label="所属组织" prop="orgNames" min-width="200"></el-table-column>
                <el-table-column label="所属租户" prop="tenantName" min-width="150"></el-table-column>
                <!-- <el-table-column label="最后登录时间" prop="lastLoginTime" min-width="150"></el-table-column> -->
            </el-table>
            <!-- 分页器 -->
            <el-affix v-show="!tableLoading" position="bottom" :offset="16">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
    </div>
</template>

<script lang='ts' setup>
import searchBox from '@/components/common/SearchBox.vue'
import type { IPageUserItem, IUserPageListRequest, SearchUserResponseItem } from '@/types/user'
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import systemService from '@/service/systemService'
import { downloadFile } from '@/utils/download'


const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
const tableHeight = ref(500)
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - actionBarContentRef.value.clientHeight - 32 - 16 - 16 - 16 - 16 - 16
    }
}
const exporting = ref(false)
type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const userManagementOptionConifg = ref<CustomConfig>({})

const getSearchOptions = async () => {
    const [ roleRes, orgRes, tenantRes ] = await Promise.all([
        systemService.roleListByName(),
        systemService.orgGetOrgByScopeData('clue'),
        systemService.tenantList()
    ])
    const roleResList = roleRes.data.map((item) => ({ value: item.id, label: item.roleName }))
    const orgResList = orgRes.data?.map((item) => ({ value: item.id, label: item.name }))
    const tenantResList = tenantRes.map((item) => ({ value: item.id, label: item.name }))
    userManagementOptionConifg.value = {
        roleId: roleResList,
        orgId: orgResList,
        tenantId: tenantResList
    }
}

let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    getuserList()
}

let queryParams = reactive<IUserPageListRequest>({
    page: 1,
    pageSize: 20,
})

const getuserList = () => {
    tableLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    systemService.userPage(queryParams).then((res) => {
        // console.log('userpage',res.data)
        userList.value = res.data
        pageInfo.total = res.total
    }).finally(() => {
        tableLoading.value = false
    })
}

const disableSelected = () => {
    if (selectedData.value.length === 0) {
        ElMessage.warning('请选择要操作的用户')
        return
    }
    systemService
        .userDisable({
            ids:checkIds.value,
        })
        .then(() => {
            getuserList()
        })
}

const changeStatus = (status: number, user: IPageUserItem) => {
    systemService
        .userEdit({
            id: user.id,
            nickname: user.nickname,
            orgId: user.orgId,
            role: user.role,
            status: status.toString(),
            username: user.username,
        })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode !== 0) {
                ElMessage.error(errMsg || '操作失败')
            } else {
                // 查询列表
                getuserList()
            }
        })
        .catch((err) => {
            ElMessage.error(err || '操作失败，请稍后再试')
        })
}

const exportUser = () => {
    if (pageInfo.total === 0) {
        ElMessage.warning('没有可导出的用户')
        return
    }

    if (exporting.value) return

    exporting.value = true
    systemService
        .userExportUser({
            mobile: queryParams?.mobile || '',
            orgId: queryParams?.orgId || '',
            roleId: queryParams?.roleId || '',
            status: queryParams?.status || '0',
            username: queryParams?.username || '',
        })
        .then((res) => {
            exporting.value = false
            downloadFile(res)
        })
        .catch(() => {
            ElMessage.warning('导出失败，请稍后再试')
        })
}

const selectedData = ref<SearchUserResponseItem[]>([])
const checkIds = ref<string[]>([])
const handleSelectionChange = (val: SearchUserResponseItem[]) => {
    if (val && val.length < 1001) {
        selectedData.value = val
        checkIds.value = val.map((i) => {
            return i.id
        })
    } else {
        ElMessage.warning('最多只能选择1000条数据')
    }
}

const selectedLength = computed(() => selectedData.value.length)
const tableLoading = ref(false)
const userList = ref<IPageUserItem[]>([])

const updateSearchParams = (params:IUserPageListRequest) => {
    queryParams = params
    // queryParams.page = pageInfo.page
    // queryParams.pageSize = pageInfo.pageSize
    getuserList()
}


onMounted(() => {
    getSearchOptions()
    getuserList()
    getTableHeight()
})

</script>

<style lang='scss' scoped>

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
    margin-top: 16px;
}


</style>