<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, type RouteRecordRaw } from 'vue-router'
import userRoutes from '@/router/routes/userRoutes'
import SettingMenus from './menu/SettingMenus.vue'

const route = useRoute()

const menus = ref<RouteRecordRaw[]>([])

const props = defineProps<{
    isCollapse: boolean
    color?: string
    size?: string
}>()

onMounted(() => {
    if (userRoutes.length > 0) {
        const { children } = userRoutes[0]
        if (children) {
            menus.value = children
        }
    } else {
        menus.value = []
    }
})

const openList = computed(() => {
    const { matched } = route || {}
    const result = matched.map((item) => item.name)
    return result
})
</script>

<template>
    <div class="flex-1 oh">
        <el-scrollbar view-class="height-100 flex flex-column">
            <el-menu
                class="el-menu-vertical flex-1"
                :collapse="props.isCollapse"
                :collapse-transition="false"
                :default-openeds="openList"
                :default-active="route.name"
                :unique-opened="true"
            >
                <SettingMenus :menu="menu" v-for="menu in menus" :key="menu.name" :collapse="props.isCollapse" />
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<style lang="scss" scoped>
.el-menu-vertical {
    border: none;
    padding-top: 16px;
}
.el-menu-vertical:not(.el-menu--collapse) {
    width: 200px;
}
</style>
