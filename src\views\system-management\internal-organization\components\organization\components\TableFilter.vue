<script setup lang="ts">
import systemService from '@/service/systemService'
import type { IOrgTreeItem } from '@/types/org'
import type { IRoleItem, IRoleListRequest } from '@/types/role'
import type { IUserListRequestParams } from '@/types/user'
import { flatten } from '@/utils/flatten'
import { ElSelect, type FormInstance } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'

const props = defineProps<{
    orgList: IOrgTreeItem[]
    getData: (params: IUserListRequestParams) => void
    currentOrg: IOrgTreeItem | null
    setCurrentOrg: (v: IOrgTreeItem | null) => void
}>()

const formRef = ref<FormInstance>()
const queryingRole = ref(false)
const roleList = ref<IRoleItem[]>([])
const store = useStore()

const queryForm = reactive<IUserListRequestParams>({
    username: '',
    mobile: '',
    roleId: '',
    status: '',
    orgId: '',
})

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

const onSubmit = () => {
    props.getData(queryForm)
}

const reset = (formEl?: FormInstance) => {
    if (isPlatManager.value) {
        props.setCurrentOrg(null)
        formEl?.resetFields()
    } else {
        formEl?.resetFields()
        onSubmit()
    }
}

const getRoleList = () => {
    queryingRole.value = true
    const params: IRoleListRequest = {}

    if (isPlatManager.value) {
        const { tenantId } = props.currentOrg || {}
        params.tenantId = tenantId
    }
    systemService
        .roleListByName(params)
        .then((res) => {
            queryingRole.value = false
            const { errCode, data } = res
            if (errCode === 0) {
                roleList.value = data
            } else {
                roleList.value = []
            }
        })
        .catch(() => {
            queryingRole.value = false
            roleList.value = []
        })
}

const onRoleFocus = () => {
    getRoleList()
}

const onOrgChange = (v: string) => {
    const orgs = flatten(props.orgList)
    const target = orgs.find((item) => item.id === v)
    if (target) {
        props.setCurrentOrg(target)
    } else {
        props.setCurrentOrg(null)
    }
}

watch(
    () => props.currentOrg,
    () => {
        queryForm.orgId = props.currentOrg?.id || ''
        onSubmit()
    }
)
</script>

<template>
    <el-form ref="formRef" :inline="true" :model="queryForm" :label-width="90" label-suffix=":">
        <div>
            <el-form-item label="登录账号" label-position="left" prop="username">
                <el-input v-model="queryForm.username" placeholder="请输入登录账号" style="width: 302px" clearable />
            </el-form-item>
            <el-form-item label="手机号" label-position="left" prop="mobile">
                <el-input v-model="queryForm.mobile" placeholder="请输入手机号" style="width: 302px" clearable />
            </el-form-item>
            <el-form-item label="用户状态" label-position="left" prop="status">
                <el-select
                    v-model="queryForm.status"
                    placeholder="全部用户状态"
                    style="width: 302px"
                    @change="onSubmit"
                    :filterable="false"
                    clearable
                >
                    <el-option label="启用" value="0" />
                    <el-option label="停用" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="用户角色" label-position="left" prop="roleId">
                <el-select
                    v-model="queryForm.roleId"
                    placeholder="请选择用户角色"
                    style="width: 302px"
                    @change="onSubmit"
                    :loading="queryingRole"
                    @focus="onRoleFocus"
                    clearable
                >
                    <el-option :label="role.roleName" :value="role.roleId" :key="role.id" v-for="role in roleList" />
                </el-select>
            </el-form-item>
            <el-form-item label="所属组织" label-position="left" prop="orgId">
                <el-select
                    v-model="queryForm.orgId"
                    :clearable="true"
                    placeholder="请选择所属组织"
                    style="width: 302px"
                    @change="onOrgChange"
                >
                    <el-option :label="org.name" :value="org.id" :key="org.id" v-for="org in flatten(orgList)" />
                </el-select>
            </el-form-item>
        </div>
        <div class="flex flex-row gap-16 justify-end">
            <el-form-item style="margin-right: 0px">
                <el-button @click="reset(formRef)">清空</el-button>
                <el-button type="primary" @click="onSubmit">搜索</el-button>
            </el-form-item>
        </div>
    </el-form>
</template>

<style scoped></style>
