import { type RouteRecordRaw } from 'vue-router'
import Profile from '@/views/usercenter/profile/Profile.vue'
import SettingLayout from '@/layouts/SettingLayout.vue'
import Notification from '@/views/usercenter/message/notification/Notification.vue'

const userRoutes: Array<RouteRecordRaw> = [
    {
        path: '/usercenter',
        component: SettingLayout,
        redirect: '/profile',
        children: [
            {
                path: 'profile',
                name: 'profile',
                component: Profile,
                meta: { requiresAuth: true, title: '用户中心', icon: 'icon-a-yonghuzhongxin' },
            },
            {
                path: 'message',
                name: 'message',
                meta: { requiresAuth: true, title: '消息管理', icon: 'icon-a-xiaoxitongzhi' },
                children: [
                    {
                        path: 'notification',
                        name: 'notification',
                        component: Notification,
                        meta: { requiresAuth: true, title: '消息通知' },
                    },
                ],
            },
        ],
    },
]

export default userRoutes
