<script lang="ts" setup>
import { computed } from 'vue'
import { useRouter, type RouteRecordNameGeneric, type RouteRecordRaw } from 'vue-router'
const router = useRouter()
const props = defineProps<{
    menu: RouteRecordRaw
    collapse: boolean
}>()

const open = (menu: RouteRecordRaw) => {
    if (!menu.name || typeof menu.name !== 'string') return
    router.push({
        name: menu.name,
    })
}

const currentRoute = computed(() => {
    return router.currentRoute.value.name
})

const isGroup = computed(() => {
    const { children } = props.menu
    return !!children
})

const isSubmenu = (menu: RouteRecordRaw) => {
    return !menu.meta?.icon
}

const isCurrentRoute = (name: RouteRecordNameGeneric) => {
    return currentRoute.value === name
}

const matchedRoute = (name: RouteRecordNameGeneric) => {
    const arr = router.currentRoute.value.matched

    console.log('arrrr')
    console.log(arr)

    if (name === 'index') {
        return arr[1].name === name
    }

    if (Array.isArray(arr) && arr.length >= 2) {
        return arr[1].name === name
    }
    return false
}
</script>

<template>
    <el-menu-item :index="props.menu.name" v-if="!isGroup" @click="open(props.menu)" class="cust-menu cust-menu-item"
                  :class="{
                      'cust-menu-item-sub': isSubmenu(props.menu),
                  }">
        <div style="width: 100%" :class="{
            'lr-padding-8': !isSubmenu(props.menu),
        }">
            <div class="flex h-40 top-bottom-center border-radius-4" :class="{
                'back-color-menu-bg': isCurrentRoute(props.menu.name),
                'back-color-main--hover': !isCurrentRoute(props.menu.name),
                'l-padding-12': !isSubmenu(props.menu),
                'l-padding-41': isSubmenu(props.menu),
            }">
                <el-icon v-if="props.menu.meta?.icon">
                    <Icon :icon="matchedRoute(props.menu.name) ? props.menu.meta.icon + '_bod' : props.menu.meta.icon"
                          size="18" />
                </el-icon>
                <div v-if="!collapse || isSubmenu(props.menu)">{{ props.menu.meta?.title }}</div>
            </div>
        </div>
    </el-menu-item>
    <el-sub-menu :index="props.menu.name" v-if="isGroup" class="cust-sub-menu" popper-class="menu-side-cust-popper">
        <template #title>
            <el-icon>
                <Icon :icon="matchedRoute(props.menu.name) ? props.menu.meta?.icon + '_bod' : props.menu.meta?.icon"
                      size="18" />
            </el-icon>
            <span>{{ props.menu.meta?.title }}</span>
        </template>
        <SettingMenus :menu="menu" v-for="menu in props.menu.children" :key="menu.name" :collapse="collapse" />
    </el-sub-menu>
</template>

<style lang="scss" scoped>
.el-menu-vertical {
    border: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
    width: 200px;
}

.cust-menu-item {
    padding-left: 0px !important;
    padding-right: 0px !important;
    height: 40px;
    margin-bottom: 8px;
}

.el-menu-item:hover {
    --el-menu-hover-bg-color: '';
}

.cust-menu .el-sub-menu,
.is-active :deep(.el-sub-menu__title) {
    color: var(--main-blue-);
}

.cust-sub-menu {
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 8px;
}

.cust-sub-menu :deep(.el-sub-menu__title) {
    padding-left: 12px !important;
    height: 40px;
    border-radius: 4px;
}

.cust-sub-menu :deep(.el-sub-menu__title):hover {
    background-color: var(--main-bg);
}

.cust-sub-menu :deep(ul) {
    padding-top: 8px;
}
</style>
