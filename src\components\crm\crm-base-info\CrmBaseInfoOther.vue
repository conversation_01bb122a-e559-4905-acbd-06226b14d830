<template>
    <el-descriptions class="margin-top" :column="2" border :label-class-name="'base-info-label'">
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">跟进状态</div>
            </template>
            {{ crmDetail.statusStr || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">线索来源</div>
            </template>
            {{ crmDetail.sourceStr || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label" :span="2">
            <template #label>
                <div class="cell-item">下次跟进时间</div>
            </template>
            {{ crmDetail.nextFollowDate ? moment(crmDetail.nextFollowDate).format("YYYY-MM-DD HH:mm:ss") : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label" :span="2">
            <template #label>
                <div class="cell-item">备注</div>
            </template>
            {{ crmDetail.note || '-' }}
        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang='ts' setup>
import { onMounted, inject, getCurrentInstance } from 'vue'
import type { Ref } from 'vue'

import type { ILeadData } from '@/types/lead'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

onMounted(() => { })
</script>

<style lang='scss' scoped>
:deep(.base-info-label) {
    background: var(--table-header-bg-) !important;
}
</style>