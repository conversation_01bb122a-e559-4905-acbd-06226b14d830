<script lang="ts" setup>
import aicService from '@/service/aicService'
import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import type { RootState } from '@/types/store'
import { computed, inject, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { Cascader, CheckboxBtn } from '../ui'
import type { IPushToGlobal } from '@/types/company'
import { cascaderFormat, type CascadeData } from '@/utils/cascader-format'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
}>()

const pushToGlobal = inject<(v: IPushToGlobal) => void>('pushToGlobal', () => {})

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const isUnlimited = computed(() => {
    const list = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    return list.length === 0
})

const isHaschanged = computed(() => {
    const target = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    const checked = formattedSelection(selectedValue.value)
    return target.length < checked.length
})

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

// ====================== Refs & Reactive State ======================
const selectedValue = ref<CascadeData>([])
const config = ref<IAicConditionData | null>(null)

// ====================== Methods ======================
const pushParams = (value: string[]) => {
    const tempArray: INormalFilterParams[] = []
    value.forEach((item: string) => {
        const arr = item.split(',')
        if (arr && arr.length === 3) {
            const params: INormalFilterParams = {
                label: arr[1],
                value: item,
                category: props.data.name,
                categoryKey: props.data.key,
                type: props.data.dataType,
            }

            tempArray.push(params)
        }
    })

    if (!pushToGlobal) return
    pushToGlobal(tempArray)
}

const onCascaderChange = (value: CascadeData) => {
    selectedValue.value = value
    const data = formattedSelection(value)
    if (data.length === 0) {
        resetParams()
    } else {
        pushParams(data)
    }
}

// 计算格式化的数据
const formattedSelection = (value: CascadeData) => {
    const result = cascaderFormat(value, getConfig(props.data))
    return result
}

const resetParams = () => {
    if (!pushToGlobal) return
    pushToGlobal({ categoryKey: props.data.key })
}

const onUnlimitedChange = () => {
    selectedValue.value = []
    resetParams()
}

const uncheck = () => {
    const targets = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    const temp = []

    for (let index = 0; index < targets.length; index++) {
        const target = targets[index]
        const arr = target.value.split(',')
        const key = arr[0]
        const level = arr[2]

        for (let index = 0; index < selectedValue.value.length; index++) {
            const selected = selectedValue.value[index]
            if (selected[Number(level) - 1] === key) {
                temp.push(selected)
            }
        }
    }

    selectedValue.value = temp
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
    }
}

const getConfig = (item: IAicNormalSearchRules) => {
    if (!config.value) return []
    const value = config.value
    const found = Object.entries(value).find(([k]) => k === item.key)
    const result = found ? (found[1] as IAicConditionData[keyof IAicConditionData]) : []
    return result
}

// ====================== Watchers ======================
watch(
    () => isHaschanged.value,
    (value) => {
        if (value) {
            uncheck()
        }
    }
)

watch(
    () => isUnlimited.value,
    (value) => {
        if (value) {
            selectedValue.value = []
        }
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getStaticConfig()
})
</script>

<template>
    <div class="flex top-bottom-center">
        <div class="flex flex-row top-bottom-center">
            <div class="w-112">
                <div class="lh-24 font-16 color-black">{{ props.data.name }}：</div>
            </div>
            <div class="flex flex-row top-bottom-center gap-24 flex-wrap flex-1">
                <CheckboxBtn :onChange="onUnlimitedChange" :checked="isUnlimited" />
                <Cascader :config="getConfig(data)" :onChange="onCascaderChange" :value="selectedValue" />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
