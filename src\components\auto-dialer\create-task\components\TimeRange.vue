<script lang="ts" setup>
import { useGridSelect } from '@/hooks/useGridSelect'
import { indexToSlot, numbersToTimeRanges, timeRangesToNumbers } from '@/utils/format'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const model = defineModel<string>()
defineProps<{
    disabled: boolean
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const groups = ref<number[]>([])
const { onMouseDown, onMouseEnter, onMouseUp } = useGridSelect(groups)
const invalidRange = [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 25, 26, 27, 28, 41, 42, 43, 44, 45, 46, 47, 48,
]

// ====================== Methods ======================
const onChooseDone = () => {
    onMouseUp()
    groups.value = groups.value.filter((e) => !invalidRange.includes(e))
    const value = numbersToTimeRanges(groups.value)
    // emit('update:modelValue', value)
    model.value = value
}

const convert = (val?: string) => {
    if (!val) return

    const result = timeRangesToNumbers(val)
    if (!result) return

    groups.value = result
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================

onMounted(() => {
    document.addEventListener('mouseup', onChooseDone)
    convert(model.value)
})

onBeforeUnmount(() => {
    document.removeEventListener('mouseup', onChooseDone)
})

watch(
    () => model.value,
    (val) => {
        convert(val)
    }
)
</script>

<template>
    <div class="el-border border-radius-4 oh back-color-white time-range">
        <table>
            <tbody>
                <tr class="h-30 color-black font-14">
                    <td colspan="24" class="text-center el-border noselect">00:00 - 11:59</td>
                    <td colspan="24" class="text-center el-border noselect">12:00 - 23:59</td>
                </tr>

                <tr class="h-30 color-two-grey">
                    <td colspan="2" v-for="i in 24" :key="i" class="font-12 text-center el-border noselect">
                        {{ i - 1 }}
                    </td>
                </tr>
                <tr class="h-30">
                    <el-tooltip
                        class="box-item"
                        effect="dark"
                        :content="indexToSlot(i - 1)"
                        placement="bottom"
                        v-for="i in 48"
                        :key="i"
                        :show-after="200"
                    >
                        <td
                            :class="{
                                'back-color-blue': groups.includes(i),
                                'back-color-light-blue--hover':
                                    !groups.includes(i) && !invalidRange.includes(i) && !disabled,
                                'back-color-border': invalidRange.includes(i),
                                'back-color-active': !groups.includes(i) && !invalidRange.includes(i),
                                pointer: !invalidRange.includes(i),
                                'not-allow': invalidRange.includes(i) || disabled,
                            }"
                            class="el-border noselect"
                            @mousedown="disabled ? '' : onMouseDown(i)"
                            @mouseenter="disabled ? '' : onMouseEnter(i)"
                        ></td>
                    </el-tooltip>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<style lang="scss" scoped>
.time-range table {
    table-layout: fixed;
    border-collapse: collapse;
    width: 100%;
    overflow: hidden;
    /* 消除掉外边框 */
    border-style: hidden;
}

.time-range table td {
    padding: 4px 0;
    box-sizing: border-box;
    word-break: break-all; /* 防长单词撑宽，可按需移除 */
}
</style>
