<template>
    <el-dialog
        title="绑定企业"
        v-model="dialogVisible"
        @close="handleClose"
    >
        <div>
            <div class="display-flex flex-end b-margin-16">
                <el-button type="primary" @click="handleAdd">新增</el-button>
            </div>
            <BindCompanyTable :tableDataList="tableDataList" :tableLoading="tableLoading" :tableHeight="tableHeight" :refresh="search" :isDialog="isDialog"/>
            <div class="pagination-bar">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.size"
                    :total="pageInfo.total"
                    :page-sizes="[20, 40, 60, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @change="pageChange"
                />
            </div>
        </div>
    </el-dialog>
    <el-dialog
        title="新增"
        v-model="addDialogVisible"
        width="500px"
    >
        <div class="display-flex flex-column gap-8">
            <div class="display-flex flex-column gap-8">
                <div class="display-flex gap-4">
                    <span class="color-red">*</span>
                    <span class="">企业名称</span>
                </div>
                <el-select 
                    v-model="nsrsbh"
                    filterable
                    remote
                    reserve-keyword
                    :loading="loading"
                    :remote-method="remoteMethod"
                    placeholder="请输入企业名称"
                >
                    <el-option
                        v-for="item in dataList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
            <div class="display-flex flex-column gap-8">
                <div class="display-flex gap-4">
                    <span class="color-red">*</span>
                    <span class="">企业税号</span>
                </div>
                <el-input v-model="nsrsbh" disabled></el-input>
            </div>
            <div class="t-margin-16 display-flex flex-end">
                <el-button class="l-margin-12 all-padding-16" @click="handleAddClose">取消</el-button>
                <el-button 
                    :loading="confirmLoading" 
                    type="primary" 
                    class="all-padding-16" 
                    @click="submitBondCompany"  
                >提交</el-button
                >
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, defineProps, reactive } from 'vue'
import tamService from '@/service/tamService'
import BindCompanyTable from './BindCompanyTable.vue'
import type { IBindCompanyTableDataItem,IBindCompanyTableParams, IWorkNumberListTableDataItem, IBindCompanyParams } from '@/types/worknumber'
import { ElMessage } from 'element-plus'
import crmService from '@/service/crmService'

const isDialog = ref(true)
const props = defineProps<{
    visible: boolean
    workNumberInfo:IWorkNumberListTableDataItem | undefined
    channelId:string
}>()

const dialogVisible = ref(false)
watch(() => props.visible, (val) => {
    if (val) {
        dialogVisible.value = true
        search()
    }
},{immediate: true })
const emit = defineEmits(['update:visible'])
const handleClose = () => {
    emit('update:visible', false)
}
const addDialogVisible = ref(false)
const handleAdd = () => {
    addDialogVisible.value = true
}
const handleAddClose = () => {
    addDialogVisible.value = false
}
const confirmLoading = ref(false)
const nsrsbh = ref('')

interface ListItem {
  value: string
  label: string
}
const dataList = ref<ListItem[]>([])
const loading = ref(false)
const remoteMethod =async (query: string) => {
    if (query) {
        loading.value = true
        const list = await crmService.getCrmList({page: 1, pageSize: 10, companyName: query})
        if(list.data && list.data.length > 0){
            dataList.value = list.data.map((item) => {
                return {
                    value: item.socialCreditCode,
                    label: item.companyName,
                }
            })
        }
        loading.value = false
    } else {
        dataList.value = []
    }
}
const companyName = ref('')
watch(nsrsbh, (newValue) => {
    const selectedItem = dataList.value.find(item => item.value === newValue)
    if (selectedItem) {
        companyName.value = selectedItem.label
    }
})
const submitBondCompany =async () => {
    console.log('props.channelId',props.channelId)
    console.log('props.workNumberInfo',props.workNumberInfo)
    if(!companyName.value) {
        ElMessage.error('请输入企业名称')
        return
    }
    const bsyxx = await tamService.getTaxOfficerInfo({telX:props.workNumberInfo!.telX,channelId:props.channelId})
    confirmLoading.value = true
    const params: IBindCompanyParams = {
        telX: props.workNumberInfo?.telX,
        telB: props.workNumberInfo?.telB,
        entName: companyName.value,
        socialCreditCode: nsrsbh.value,   
    }
    if(bsyxx.data.bsysfzhm){
        params.bsySfz = bsyxx.data.bsysfzhm
    }
    if(bsyxx.data.bsyxm){
        params.bsyName = bsyxx.data.bsyxm
    }
    tamService.bindEnterprise(params).then((res) => {
        console.log(res)
        if(res.success){
            ElMessage.success('绑定成功')
            addDialogVisible.value = false
            search()
        }else{
            ElMessage.error(res.errMsg)
        }
    }).finally(() => {
        confirmLoading.value = false
    })
}

const pageInfo = reactive({
    page: 1,
    size: 20,
    total: 0,
})
const queryParams = ref<IBindCompanyTableParams>({
    page: 1,
    size: 20,
})
const pageChange = (currentPage: number, size: number) => {
    queryParams.value.page = currentPage
    queryParams.value.size = size
    search()
}
const tableDataList = ref<IBindCompanyTableDataItem[]>([])
const tableLoading = ref(false)
const search = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.size = pageInfo.size
    tamService.bsyBindEntList(queryParams.value).then((res) => {
        tableDataList.value = res.records
        pageInfo.total = res.total
    }).finally(() => {
        tableLoading.value = false
    })
}
const tableHeight = ref(400)
onMounted(() => {
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>
