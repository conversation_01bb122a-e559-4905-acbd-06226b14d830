<template>
    <el-form :inline="true" :model="formXTPT" >
        <el-form-item class="full-width">
            <div class="flex-column">
                <span>域名</span>
                <div class="display-flex">
                    <div class="mb-2 ml-4">
                        <el-radio-group v-model="domainProtocol" style="min-width: 150px;">
                            <el-radio value="1" size="large">http</el-radio>
                            <el-radio value="2" size="large">https</el-radio>
                        </el-radio-group>
                    </div>
                    <el-input v-model="formXTPT.domain" placeholder="请输入域名" style="width: 500px">
                    </el-input>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>SSL证书
                    <span class="color-three-grey font-14">(.key)</span>
                </span>
                <el-upload
                    v-if="!domainKey"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept=".key"
                    :show-file-list="false"
                    :on-success="handleKeySuccess"
                    :on-progress="handleKeyProgress"
                    :on-error="onError"
                    :on-remove="handleKeyRemove"
                    :before-upload="beforeFlieUpload"
                >
                    <el-icon v-if="domainKeyLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="width-100 display-flex top-bottom-center space-between ">
                    <span class="text-ellipsis text-nowrap" style="width: 200px;" :title="domainKeyOriginName">{{ domainKeyOriginName }}</span>
                    <el-icon class="pointer" @click="deleteDomainKey"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>SSL证书
                    <span class="color-three-grey font-14">(.pem)</span>
                </span>
                <el-upload
                    v-if="!domainPem"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept=".pem"
                    :show-file-list="false"
                    :on-success="handlePemSuccess"
                    :on-progress="handlePemProgress"
                    :on-error="onError"
                    :on-remove="handlePemRemove"
                    :before-upload="beforeFlieUpload"
                >
                    <el-icon v-if="domainPemLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="width-100 display-flex top-bottom-center space-between ">
                    <span class="text-ellipsis text-nowrap" style="width: 200px;" :title="domainPemOriginName">{{ domainPemOriginName }}</span>
                    <el-icon class="pointer" @click="deleteDomainPem"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <div class="flex top-bottom-center gap-8 tb-margin-16">
            <div style="width: 4px;height: 14px; background-color: #1966FF; border-radius: 4px;" ></div>
            <span class="color-black font-16">登录页</span>
        </div>
        <el-form-item>
            <div class="flex-column">
                <span>
                    网页页签标题
                    <span class="color-three-grey font-14">(默认值为数字化产业融合协同平台)</span>
                </span>
                <el-input
                    v-model="formXTPT.webPageTabTitle"
                    placeholder="请输入网页页签标题"
                ></el-input>
            </div>
        </el-form-item>
        <el-form-item >
            <div class="flex-column">
                <span>左上角logo</span>
                <el-upload
                    v-if="!loginLogoUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleLogoProgress"
                    :on-success="handleLogoSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="loginLogoLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="loginLogoUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[loginLogoUrl] />
                    <el-icon class="close-icon" @click="deleteLoginLogo"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>
                    左侧顶部文字
                    <span class="color-three-grey font-14">(默认值为臻企云·数字化产业融合协同平台)</span>
                </span>
                <el-input
                    v-model="formXTPT.companyName"
                    placeholder="请输入顶部文字"
                ></el-input>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>登录页图片</span>
                <el-upload
                    v-if="!logoImgUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleLoginProgress"
                    :on-success="handleLoginSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="logoImgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="logoImgUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[logoImgUrl] />
                    <el-icon class="close-icon" @click="deleteLogoImg"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span >
                    底部时间
                    <span class="color-three-grey font-14">(默认值为2016-2025)</span>
                </span>
                <el-date-picker
                    v-model="cDateValue"
                    type="yearrange"
                    range-separator="To"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="width: 100%;"
                />
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>
                    底部公司名
                    <span class="color-three-grey font-14">(默认值为数族科技（南京）股份有限公司)</span>
                </span>
                <el-input
                    v-model="formXTPT.cName"
                    placeholder="请输入公司名"
                ></el-input>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>底部的备案号</span>
                <el-input
                    v-model="formXTPT.tcp"
                    placeholder="请输入备案号"
                ></el-input>
            </div>
        </el-form-item>
        <div class="flex top-bottom-center gap-8 b-margin-16">
            <div style="width: 4px;height: 14px; background-color: #1966FF; border-radius: 4px;" ></div>
            <span class="color-black font-16">首页</span>
        </div>

        <el-form-item>
            <div class="flex-column">
                <span>首页上方的logo</span>
                <el-upload
                    v-if="!logoBkUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleHomeLogoProgress"
                    :on-success="handleHomeLogoSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="logoBkLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="logoBkUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[logoBkUrl] />
                    <el-icon class="close-icon" @click="deleteLogoBk"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>帮助手册地址</span>
                <el-input
                    v-model="formXTPT.helpManualAddress"
                    placeholder="请输入帮助手册地址"
                ></el-input>
            </div>
        </el-form-item>
    </el-form>
</template>

<script lang='ts' setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import type { UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import type { XTPT } from '@/types/OEM'
import fileService from '@/service/fileService'
import type { FileIns, FileUploadResponse } from '@/types/lead'

const props = defineProps<{
    modelValue: XTPT
    editData:XTPT
}>()
const emit = defineEmits<{
    (e: 'update:modelValue', value: XTPT): void
}>()
const formXTPT = ref<XTPT>(props.modelValue)
watch(formXTPT, (newValue) => {
    emit('update:modelValue', newValue)
})
watch(() => props.editData, (newValue) => {
    nextTick(() => {
        console.log('props.editData', newValue)
        formXTPT.value.domain = newValue.domain || ''
        domainProtocol.value = newValue.domainProtocol === 'https' ? '2' : '1'
        console.log('domainProtocol.value',domainProtocol.value)
        domainKey.value = newValue.domainKey ? fileService.getFileUrl(newValue.domainKey) : ''
        domainKeyOriginName.value = newValue.domainKey ? newValue.domainKey?.split('/')[4] : ''
        domainPem.value = newValue.domainPem ? fileService.getFileUrl(newValue.domainPem) : ''
        domainPemOriginName.value = newValue.domainPem ? newValue.domainPem?.split('/')[4] : ''
        formXTPT.value.cName = newValue.cName || ''
        formXTPT.value.companyName = newValue.companyName || ''
        loginLogoUrl.value = newValue.loginLogo ? fileService.getFileUrl(newValue.loginLogo) : ''        
        logoBkUrl.value = newValue.logoBk ? fileService.getFileUrl(newValue.logoBk) : ''
        logoImgUrl.value = newValue.logoImg ? fileService.getFileUrl(newValue.logoImg) : '' 
        cDateValue.value = newValue.cDate ? newValue.cDate.split('-').map(dateStr => new Date(dateStr)) : []
        formXTPT.value.webPageTabTitle = newValue.webPageTabTitle || ''
        formXTPT.value.tcp = newValue.tcp || ''
        formXTPT.value.helpManualAddress = newValue.helpManualAddress || ''
    })
}, {immediate: true, deep: true })
// 上传文件接口
const uploadFile = `/api/zhenqi-crm/file/upload-temp`
// 请求头
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}
// ======协同平台======

//域名
const domainKeyOriginName = ref('')
const domainPemOriginName = ref('')
const domainKey = ref('')
const domainPem = ref('')
const domainKeyLoading = ref(false)
const domainPemLoading = ref(false)
const domainProtocol = ref('')
const deleteDomainKey = () => {
    domainKeyOriginName.value = ''
    domainKey.value = ''
}

const deleteDomainPem = () => {
    domainPemOriginName.value = ''
    domainPem.value = ''
}

const handleKeyProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    domainKeyLoading.value = true
}
const handlePemProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    domainPemLoading.value = true
}

const handleKeySuccess = (file: FileUploadResponse) => {
    console.log('handleKeySuccess 事件触发：', file)
    domainKey.value = file.data.link
    domainKeyOriginName.value = file.data.originalName
    domainKeyLoading.value = false
    console.log('KeyFile',domainKey.value)
}

const handleKeyRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
    console.log(file, uploadFiles)
    domainKey.value = ''

}
const handlePemSuccess = (file: FileUploadResponse) => {
    console.log('handleKeySuccess 事件触发：', file)
    domainPem.value = file.data.link
    domainPemOriginName.value = file.data.originalName
    domainPemLoading.value = false
    console.log('KeyFile',domainPem.value)
}

const handlePemRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
    console.log(file, uploadFiles)
    domainPem.value = ''
}

const onError = ( file: FileIns) => {
    console.log('onError 事件触发：', file)
    ElMessage.error('上传失败')
}
const beforeFlieUpload: UploadProps['beforeUpload'] = () => {
    if(formXTPT.value.domain === ''){
        ElMessage.error('请先填写域名')
        return false
    }
    return true
}

// cDate转化
const cDateValue = ref([] as Date[])
const cDate = computed(() => {
    if(cDateValue.value && cDateValue.value.length === 2) {
        return transformDate(cDateValue.value)
    }else{
        return ''
    }
})
const transformDate = (data : Date[]) => {
    const startYear = data[0].getFullYear()
    const endYear = data[1].getFullYear()
    return `${startYear}-${endYear}`
}

// 左上角logo上传相关
const loginLogoUrl = ref('')
const loginLogoLoading = ref(false)
const handleLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    loginLogoLoading.value = true
}
const handleLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    loginLogoLoading.value = false
    loginLogoUrl.value = response.data.link
}

// 登录页图片上传相关
const logoImgUrl = ref('')
const logoImgLoading = ref(false)
const handleLoginProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoImgLoading.value = true
}
const handleLoginSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, uploadFile)
    logoImgLoading.value = false
    logoImgUrl.value = response.data.link
}

// 首页上方的logo上传相关
const logoBkUrl = ref('')
const logoBkLoading = ref(false)
const handleHomeLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoBkLoading.value = true
}
const handleHomeLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response)
    logoBkLoading.value = false
    logoBkUrl.value = response.data.link
}
const beforePicUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (rawFile.size / 1024 / 1024 > 5) {
        ElMessage.error('文件大小不能超过5mb!')
        return false
    }
    return true
}

const deleteLogoBk = () => {
    logoBkUrl.value = ''
}
const deleteLoginLogo = () => {
    loginLogoUrl.value = ''
}
const deleteLogoImg = () => {
    logoImgUrl.value = ''
}

// 协同平台相关
watch([cDate, loginLogoUrl, logoImgUrl, logoBkUrl, domainProtocol, domainKey, domainPem], () => {
    formXTPT.value.cDate = cDate.value
    formXTPT.value.loginLogo = loginLogoUrl.value 
    formXTPT.value.logoImg = logoImgUrl.value 
    formXTPT.value.logoBk = logoBkUrl.value 
    formXTPT.value.domainProtocol = domainProtocol.value === '1' ? 'http' : 'https'
    formXTPT.value.domainKey = domainKey.value 
    formXTPT.value.domainPem = domainPem.value 
    console.log('formXTPT.value',formXTPT.value)
}, { immediate: true, deep: true })

onMounted(() => {
})
</script>

<style lang='scss' scoped>
.close-icon {
  position: absolute;
  right: 0;
  top: 0;
  color: red;
  cursor: pointer;
  display: none;
}


.image-container {
    position: relative;
    width: 50px;
    height: 50px;
}

.image-container:hover .close-icon {
    display: block; 
}
:deep(.el-form-item){
    width: 45% !important;
    height: 60px !important;
}
:deep(.full-width){
    width: 100% !important;
}
:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}

:deep(.upload-demo){
    display: flex;
    width: 300px;
}
:deep(.el-upload-list__item){
    width: 180px;
}

:deep(.inline-upload) {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 20px;
}
</style>