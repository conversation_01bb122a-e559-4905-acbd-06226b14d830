<script lang="ts" setup>
import type { IAicNormalSearchRuleEnums, IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import type { IPushToGlobal } from '@/types/company'
import { computed, ref, watch, onBeforeMount, inject } from 'vue'

// ====================== Interfaces & Types ======================
interface ISelectFilter extends IAicNormalSearchRuleEnums {
    checked?: boolean
}

// ====================== Props & Injections ======================
const props = defineProps<{
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
}>()

const pushToGlobal = inject<(v: IPushToGlobal) => void>('pushToGlobal', () => {})

// ====================== Store & Computed Values ======================
const isUnlimited = computed(() => {
    const list = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    return list.length === 0
})

const isHaschanged = computed(() => {
    const target = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    const checked = list.value.filter((e) => e.checked)
    return target.length < checked.length
})

// ====================== Refs & Reactive State ======================
const unlimited = ref(true)
const list = ref<ISelectFilter[]>([])

// ====================== Methods ======================
const onChange = (value: string) => {
    if (value === 'unlimited') {
        unlimited.value = true
        list.value.forEach((item) => {
            item.checked = false
        })
        resetParams()
    } else {
        unlimited.value = false
        const target = list.value.findIndex((item) => item.tagValue === value)
        const isChecked = !list.value[target].checked
        list.value[target].checked = isChecked

        pushParams(list.value[target])
    }
}

const pushParams = (item: ISelectFilter) => {
    const params: INormalFilterParams = {
        label: item.name,
        value: item.tagValue,
        category: props.data.name,
        categoryKey: props.data.key,
        type: props.data.dataType,
        checked: item.checked,
    }

    if (!pushToGlobal) return
    pushToGlobal(params)
}

const resetParams = () => {
    if (!pushToGlobal) return
    pushToGlobal({ categoryKey: props.data.key })
}

const uncheck = () => {
    const target = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    const targetIds = target.map((e) => e.value)
    list.value.forEach((item) => {
        if (!targetIds.includes(item.tagValue)) {
            item.checked = false
        }
    })
}

// ====================== Watchers ======================
watch(
    () => isUnlimited.value,
    (value) => {
        if (value) {
            list.value.forEach((item) => {
                item.checked = false
            })
        }
    }
)

watch(
    () => isHaschanged.value,
    (value) => {
        if (value) {
            uncheck()
        }
    }
)

// ====================== Lifecycle Hooks ======================
onBeforeMount(() => {
    const { enums } = props.data || {}
    list.value = enums || []
})
</script>

<template>
    <div class="flex top-bottom-center">
        <div class="flex flex-row top-bottom-center">
            <div class="w-112">
                <div class="lh-24 font-16 color-black">{{ data.name }}：</div>
            </div>
            <div class="flex flex-row top-bottom-center gap-24 flex-wrap flex-1">
                <CheckboxBtn :onChange="onChange" :checked="isUnlimited" />
                <Checkbox
                    v-for="item in list"
                    :key="item.tagValue"
                    :label="item.name"
                    :value="item.tagValue"
                    :checked="!!item.checked"
                    :onChange="onChange"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
// Component styles
</style>
