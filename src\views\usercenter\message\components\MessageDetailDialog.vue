<script lang='ts' setup>
import { watch, ref } from 'vue'
import type { ISystemMessageItem } from '@/types/message'
const props = defineProps<{
    visible: boolean
    detailInfo: Partial<ISystemMessageItem> 
}>()
const dialogVisible = ref(false)
watch(() => props.visible, (newVal) => {
    dialogVisible.value=newVal
})
const emit = defineEmits(['update:visible'])
const handleClose = () => {
    emit('update:visible', false)
}
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        :title="props.detailInfo.title"
        width="500"
        show-close
        close-on-press-escape
        destroy-on-close
        style="padding: 16px 24px;min-height: 230px;"
        @close="handleClose()"
    >
        <template #header>
            <div class="font-16 font-bold">{{props.detailInfo.title}}</div>
            <div class="color-three-grey font-12 t-margin-4">{{ detailInfo.createTime }}</div>
        </template>
        <div class="font-14 color-two-grey" style="line-height: 24px;" v-html="detailInfo.content"></div>
        <!-- <div class="display-flex" v-if="detailInfo.linkNoticeId">
            <Icon class="r-margin-5" icon="icon-icon_clue" :size="16" color="var(--main-blue-)" />
            <a class="a-link" :href="detailInfo.linkNoticeId" target="_blank">{{ detailInfo.linkNoticeId }}</a>
        </div> -->
    </el-dialog>
</template>
<style scoped lang='scss'>
    .a-link {
        display: block;
        cursor: pointer;
        color: var(--main-blue-);
        font-size: 14px;
        text-decoration: none;
    }
</style>
