<template>
    <div ef="mainContentRef" style="background-color: #f7f7f7">
        <div ref="searchContentRef" style="background-color: #fff; padding: 16px">
            <!-- 搜索公共组件 -->
            <searchBox
                :searchOptionKey="searchOptionsKey"
                @updateSearchParams="updateSearchParams"
                :defaultValue="defaultQueryParams"
                :customConfig="searchConfig"
            ></searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="flex flex-row b-margin-16">
                <el-dropdown placement="bottom-start">
                    <el-button :loading="exporting" :disabled="exporting">
                        导出
                        <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="exportRecord()">导出全部</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <el-table
                ref="tableList"
                :data="tableData"
                :style="{ 'min-height': tableHeight + 'px' }"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh">
                        <img class="w-260 h-260" src="@/assets/images/customer-public-no-data.png" alt="暂无数据" />
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="权益名称" min-width="150">
                    <template #default="scope">
                        {{ scope.row.serviceName }}
                    </template>
                </el-table-column>
                <el-table-column label="企业名称" prop="companyName" min-width="180"></el-table-column>
                <el-table-column label="企业税号" prop="socialCreditCode" min-width="150"></el-table-column>
                <el-table-column label="类型" prop="usageType" min-width="50">
                    <template #default="scope">
                        {{ usageType(scope.row.usageType) }}
                    </template>
                </el-table-column>
                <el-table-column label="数量" prop="consumeAmount" min-width="“50”" />
                <el-table-column label="操作员" prop="operatorName" min-width="“100”" />
                <el-table-column v-if="isPlatManager" label="所属租户" prop="tenantName" />
                <el-table-column label="消费时间" prop="usageTime" min-width="“200”">
                    <template #default="scope">
                        {{ scope.row.usageTime ? moment(scope.row.usageTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </template>
                </el-table-column>
            </el-table>
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive, getCurrentInstance } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import searchBox from '@/components/common/SearchBox.vue'
import type { RootState } from '@/types/store'
import orderService from '@/service/orderService'
import systemService from '@/service/systemService'
import type { IOrderUsageRecordParams, IOrderUsageRecordResponseItem } from '@/types/order'
import { ElMessage } from 'element-plus'
import { downloadFile } from '@/utils/download'

const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)

// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - 32 - 16 - 16 - 16 - 16 - 16
    }
}
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}

const usageType = (type: number) => {
    switch (type) {
        case 0:
            return '回退'
        case 1:
            return '扣减'
        case 2:
            return '预扣减'
        default:
            return '其他'
    }
}
const searchConfig = ref<CustomConfig>()
const searchOptionsKey = ref('BENEFIT_RECORD_SEARCH_OPTIONS')
const route = useRoute()
const tableLoading = ref<boolean>(false)
const exporting = ref(false)
const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

const exportRecord = () => {
    let params = {} as IOrderUsageRecordParams
    orderService
        .orderUsageExport(params)
        .then((res) => {
            if (res.data.type === 'application/vnd.ms-excel') {
                exporting.value = true
                downloadFile(res)
                exporting.value = false
            } else {
                ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
            }
        })
        .catch(() => {
            exporting.value = false
            ElMessage.warning('导出失败，请稍后再试')
        })
}

const queryParams = ref<IOrderUsageRecordParams>({
    page: pageInfo.page,
    pageSize: pageInfo.pageSize,
})

const tableData = ref<IOrderUsageRecordResponseItem[]>([])

const getBenefitRecord = (Params: IOrderUsageRecordParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    orderService
        .orderServiceUsagePage(Params)
        .then((res) => {
            console.log(res)
            if (res.success) {
                pageInfo.total = res.total
                tableData.value = res.data
            } else {
                ElMessage.error('系统错误')
            }
        })
        .then(() => {
            tableLoading.value = false
        })
}

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getBenefitRecord(queryParams.value)
}

const updateSearchParams = (params: IOrderUsageRecordParams) => {
    queryParams.value = params
    getBenefitRecord(queryParams.value)
}

let defaultQueryParams = reactive<Record<string, boolean | string | number[] | string[]>>({})
onMounted(() => {
    console.log(userInfo.value)

    if (isPlatManager.value) {
        searchOptionsKey.value = 'BENEFIT_RECORD_SEARCH_OPTIONS_FORADMIN'
        systemService.tenantList().then((response) => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantId: response.map((item) => ({
                    label: item.name,
                    value: item.id,
                })),
            }
        })
    }

    systemService.userGetUserByScopeData('collect').then((response) => {
        searchConfig.value = {
            ...searchConfig.value,
            operatorId: response.data.map((item) => ({
                value: item.id,
                label: item.nickname,
            })),
        }
    })

    if (route.query && JSON.stringify(route.query) !== '{}') {
        console.log('route.query', route.query)
        for (const key in route.query) {
            defaultQueryParams = {
                ...defaultQueryParams,
                [key]: route.query[key] as boolean | string | number[] | string[],
            }
        }
    } else {
        getBenefitRecord(queryParams.value)
    }
    getTableHeight()
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>
