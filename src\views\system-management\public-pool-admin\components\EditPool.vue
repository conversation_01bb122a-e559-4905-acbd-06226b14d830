<template>
    <div>
        <div class="display-flex align-center">
            <div class="color-red font-24">*</div>
            <div class="l-margin-4 font-16">客户公海名称</div>
        </div>
        <div class="display-flex t-margin-8 justify-center b-margin-8">
            <el-input v-model="formData.name" autocomplete="off" style="width: 452px" placeholder="请输入客户公海名称"></el-input>
        </div>
        <div class="display-flex align-center">
            <div class="color-red font-24">*</div>
            <div class="l-margin-4 font-16">客户公海成员</div>
        </div>
        <div >
            <el-form-item class="display-flex t-margin-8 align-center">
                <el-select 
                    multiple 
                    v-model="selectedUsers" 
                    autocomplete="off" 
                    style="width: 452px;" 
                    placeholder="请选择客户公海成员">
                    <el-option  
                        v-for="item in userList" 
                        :key="item.id" 
                        :label="item.nickname" 
                        :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </div>
        <div class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" style="background-color: #1966FF;">确 定</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref,toRefs,reactive,watch, onMounted } from 'vue'
import systemService from '@/service/systemService'
import type { IUserListResponse } from '@/types/user'
import type { IUserListItem } from '@/types/org'

const selectedUsers = ref<IUserListResponse[]>([])
const userList = ref<IUserListItem[]>([])
const props = defineProps({
    addData: Object
})

const { addData } = toRefs(props)

watch(() => addData?.value, (newAddData) => {
    console.log('addData changed:', newAddData)
    if (newAddData) {
        formData.poolId = newAddData.id || ''
        formData.name = newAddData.name || ''
        formData.users = newAddData.users || []
    }
    selectedUsers.value = formData.users.map((user : IUserListResponse) => user.id)
}, { deep: true })

const getUser = async () => {
    let res = await systemService.userList({tenantId:''})
    userList.value = res
}

const initialState = {
    poolId: addData?.value?.id || '',
    name: addData?.value?.name || '',
    users: addData?.value?.users || [],
}

const formData = reactive({ ...initialState })

const emit = defineEmits(['cancel', 'confirm'])

const cancel = () => {
    emit('cancel')
}

const confirm = () => {
    emit('confirm', { 
        poolId: formData.poolId, 
        name: formData.name, 
        users: selectedUsers.value
    })
}

onMounted(async () => {
    selectedUsers.value = formData.users.map((user : IUserListResponse) => user.id)
    const res = await getUser()
    console.log('userList:', res)
})


</script>

<style scoped>
.align-center{
    align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  margin-top: 20px;
}
</style>
