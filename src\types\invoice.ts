import type { IPaginationResponse } from './axios'

export interface ICollectInvoiceItem {
    uuid: number
    requestId: string
    invoiceCode: string
    invoiceNumber: string
    digitalNumber: string
    invoiceTypes: string
    distributorName: string
    distributorTaxNumber: string
    purchaseName: string
    purchaseTaxNumber: string
    totalAmount: string
    taxAmount: string
    invoiceDate: string
    invoiceStatus: string
    invoiceRiskLevel: string
    invoiceTerminal: string
    drawer: string
    invoiceStatusCode: string
    scope: string
    socialCreditCode: string
    invoiceYear: string
    invoiceMonth: string
    type: string
    invoiceStatusName: string
    invoiceTypesName: string
    pdfUrl: string
    ofdUrl: string
    xmlUrl: string
    createTime: string
    num: number
}

export interface ICollectInvoiceFilter {
    invoiceDateList?: string[]
    scope?: string
    purchaseName?: string
    purchaseTaxNumber?: string
    distributorName?: string
    distributorTaxNumber?: string
    invoiceNumber?: string
}

export interface ICollectInvoiceListResponse extends IPaginationResponse {
    data: ICollectInvoiceItem[]
}

export interface ICollectInvoiceParams {
    requestId: string
    page: number
    pageSize: number
}

export interface ICollectDownloadInvoiceParams {
    invoiceIds: string[]
    requestId: string
}
