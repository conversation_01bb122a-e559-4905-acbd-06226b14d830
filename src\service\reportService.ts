import http from '@/axios'
import type { SearchCollectLogResponse, CollectLogParams , ExportParams , DownlodaReportParams , DownloadReportResponse } from '@/types/report'

export default {
    collectPage(data: CollectLogParams): Promise<SearchCollectLogResponse> {
        return http.get(`/api/zhenqi-report/collect/page`, {
            params: data,
            hideError: true, 
        })
    },

    collectExport(data: ExportParams): Promise<Blob> {
        return http.post('/api/zhenqi-report/collect/export', data, {
            responseType: 'blob',
            hideError: true,
        })
    },

    collectGetReportUrl(data:DownlodaReportParams): Promise<DownloadReportResponse> {
        const queryString = new URLSearchParams(data as unknown as Record<string, string>).toString()
        return http.get(`/api/zhenqi-report/report/get-report-url?${queryString}`,{
            hideError: true,
        })
    }
}