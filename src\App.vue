<template>
    <div id="app">
        <!-- 全局头部 -->
        <!-- 路由视图 -->
        <router-view v-if="isRouterAlive && !isMaintenance" />
        <div v-else>系统正在维护</div>
        <!-- 全局页脚 -->
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, provide, ref } from 'vue'
import { loadIconfont } from './utils/loadIconfont'
import { loadStatus } from './utils/loadstatus'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const store = useStore<RootState>()
const logout = async () => {
    store.dispatch('auth/logout')
}
const isRouterAlive = ref(true)
const isMaintenance = ref(false)
const shouldReload = ref(false)

const reload = () => {
    console.log('重载页面')
    isRouterAlive.value = false //先关闭，
    nextTick(() => {
        isRouterAlive.value = true
    })
}

const getStatus = async () => {
    const status = await loadStatus()
    const { maintenance } = status || {}
    isMaintenance.value = maintenance
}

provide('reload', reload)

onMounted(() => {
    const refreshToken = localStorage.getItem('refresh_token')
    window.addEventListener('storage', (e) => onStorageRefresh(e, refreshToken))
    window.addEventListener('visibilitychange', onVisibilitychange)
    getStatus()
    // 载入icon资源
    loadIconfont()
})

const onStorageRefresh = (e: StorageEvent, refreshToken: string | null) => {
    if (e.key === 'refresh_token') {
        if (e.newValue === null) {
            logout()
        }
        if (e.newValue !== null && e.newValue !== refreshToken) {
            console.log('需要reload')
            shouldReload.value = true
        }
    }
}

const onVisibilitychange = () => {
    // 标签切换后，再重置，解决绑定手机号后不绑定导致的问题
    if (shouldReload.value) {
        shouldReload.value = false
        reload()
    }
}

onUnmounted(() => {
    window.removeEventListener('storage', (e) => onStorageRefresh(e, ''))
    window.removeEventListener('visibilitychange', onVisibilitychange)
})
</script>
<style>
/* 全局样式 */
#app {
    font-family:
        Inter, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial,
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
    margin: 0;
    padding: 0;
    height: 100vh;
}

/* 其他全局样式 */
</style>
