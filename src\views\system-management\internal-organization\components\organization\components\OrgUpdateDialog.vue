<script setup lang="ts">
import systemService from '@/service/systemService'
import type { IOrgTreeItem } from '@/types/org'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
const dialogVisible = ref(false)
const formRef = ref<FormInstance | null>(null)
const loading = ref(false)
// const store = useStore<RootState>()
// const queryingManager = ref(false)

// const isPlatManager = computed(() => {
//     const { account } = store.state.user
//     const { user } = account || {}
//     const { tenantId } = user || {}
//     return tenantId === ''
// })

const props = defineProps<{
    visible: boolean
    onClose: (refresh?: boolean) => void
    org: IOrgTreeItem | null
    orgList: IOrgTreeItem[]
    type: string
}>()

const origin = {
    parentId: '',
    name: '',
    // managers: [],
}

const form = ref(JSON.parse(JSON.stringify(origin)))

// const userListRef = ref<IUserListItem[]>([])

watch(
    () => props.visible,
    (val) => {
        dialogVisible.value = val
        // if (val) {
        //     getManagers()
        // }
    }
)

watch(
    () => props.org,
    (val) => {
        handleOrgInfo(val)
    }
)

const handleOrgInfo = (org: IOrgTreeItem | null) => {
    const { name, id, parentId } = org || {}

    form.value.parentId = id || ''

    if (props.type === 'edit') {
        form.value.name = name || ''
        // form.value.managers = managers || []
        form.value.parentId = parentId || ''
    }

    if (formRef.value) {
        formRef.value.clearValidate()
    }
}

const handleClose = (refresh?: boolean) => {
    console.log('handleClose', refresh)
    dialogVisible.value = false
    props.onClose(refresh)
}

const resetForm = () => {
    if (!formRef.value) return
    formRef.value.resetFields()
    form.value = JSON.parse(JSON.stringify(origin))
}

// const getManagers = () => {
//     queryingManager.value = true
//     let params: IUserListRequest = {}
//     if (isPlatManager.value && props.org) {
//         const { tenantId } = props.org
//         params['tenantId'] = tenantId
//     }
//     try {
//         systemService
//             .userList(params)
//             .then((res) => {
//                 queryingManager.value = false
//                 userListRef.value = res
//             })
//             .finally(() => {
//                 queryingManager.value = false
//             })
//     } catch (error) {
//         console.log(error)
//         queryingManager.value = false
//     }
// }

const addOrg = async (formEle: FormInstance | null) => {
    if (loading.value) return
    if (!formEle) return
    loading.value = true
    await formEle.validate((valid) => {
        if (valid) {
            systemService
                .orgAdd(form.value)
                .then((res) => {
                    const { errCode, errMsg } = res
                    if (errCode !== 0) {
                        ElMessage.error(errMsg || '添加失败')
                    } else {
                        ElMessage.success('添加成功')
                        handleClose(true)
                    }
                })
                .catch(() => {
                    ElMessage.error('添加失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

const editOrg = async (formEle: FormInstance | null) => {
    const { org } = props
    if (loading.value) return
    if (!formEle) return
    if (!org) return
    if (!org.id) return

    loading.value = true
    await formEle.validate((valid) => {
        if (valid) {
            systemService
                .orgEdit({ ...form.value, orgId: org.id })
                .then((res) => {
                    const { errCode, errMsg } = res
                    if (errCode !== 0) {
                        ElMessage.error(errMsg || '操作失败')
                    } else {
                        ElMessage.success(props.type === 'add' ? '添加成功' : '编辑成功')
                        handleClose(true)
                    }
                })
                .catch(() => {
                    ElMessage.error('操作失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

const save = (formEle: FormInstance | null) => {
    if (props.type === 'add') {
        addOrg(formEle)
    }
    if (props.type === 'edit') {
        editOrg(formEle)
    }
}

const handleClosed = () => {
    resetForm()
}

const rules = reactive<FormRules<typeof form>>({
    name: [{ required: true, message: '请输入组织名称', trigger: 'change' }],
    // managers: [{ required: true, message: '请选择组织管理者', trigger: 'change' }],
    parentId: [{ required: true, message: '请选择上级部分', trigger: 'change' }],
})

const dialogTitle = computed(() => {
    return props.type === 'add' ? '新增组织' : '编辑组织'
})

// const onManagerFocus = () => {
//     getManagers()
// }
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="400"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        @close="handleClose"
        @closed="handleClosed"
    >
        <div class="org-update-dialog">
            <el-form ref="formRef" :rules="rules" :model="form" :hide-required-asterisk="false">
                <el-form-item label="组织名称" label-position="top" prop="name">
                    <el-input v-model="form.name" clearable />
                </el-form-item>
                <!-- <el-form-item label="组织管理者" label-position="top" prop="managers">
                    <el-select
                        v-model="form.managers"
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                        placeholder="请选择管理者"
                        clearable
                        :loading="queryingManager"
                        @focus="onManagerFocus"
                    >
                        <el-option v-for="user in userListRef" :key="user.id" :label="user.nickname" :value="user.id" />
                    </el-select>
                </el-form-item> -->
                <el-form-item label="上级组织" label-position="top" prop="parentId">
                    <el-select v-model="form.parentId" clearable placeholder="请选择上级组织" :disabled="props.type === 'add'">
                        <el-option v-for="org in orgList" :key="org.id" :label="org.name" :value="org.id" />
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="save(formRef)" :loading="loading"> 保存 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
/* .org-update-dialog :deep(.el-select__wrapper) {
    height: 45px;
    font-size: 14px;
}

.org-update-dialog :deep(.el-input__inner) {
    height: 45px;
    font-size: 14px;
} */
</style>
