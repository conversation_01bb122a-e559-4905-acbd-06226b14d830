<!-- 全屏企业详情页面 -->
<template>
    <SearchCompanyDetail />
</template>

<script lang='ts' setup>
import { ref, onMounted, provide } from 'vue'
import { useRoute } from 'vue-router'
import SearchCompanyDetail from '@/components/search/company-detail/SearchCompanyDetail.vue'

const route = useRoute()

const socialCreditCode = ref(route.params.socialCreditCode)

provide('socialCreditCode', socialCreditCode)

onMounted(() => { })
</script>

<style lang='scss' scoped></style>