<script lang='ts' setup>
import { onMounted } from 'vue'
// import aicService from '@/service/aicService'

onMounted(() => {
    // 1.获取授权类目
    getCategories()
})
const getCategories = async () => {
    console.log('获取授权类目')
    
}

</script>
<template>
    <el-dialog>
        <el-form>
            <el-form-item label="授权类目">
                <el-cascader></el-cascader>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang='scss'>
</style>
