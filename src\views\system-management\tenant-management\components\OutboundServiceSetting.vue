<script lang="ts" setup>
import { ref, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import outboundService from '@/service/outboundService'
import type { IAppSaveRequest } from '@/types/autoDialer'
import type { FormInstance, FormRules } from 'element-plus'
import type { ITenantPageItem } from '@/types/tenant'
const props = defineProps<{
    visible: boolean
    tenantInfo: Partial<ITenantPageItem>
}>()
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref<FormInstance>()

watch(
    () => props.visible,
    (newVal) => {
        dialogVisible.value = newVal
        if (newVal) {
            // 获取智能外呼配置
            outboundService.appConfig({tenantId: props.tenantInfo.id || ''}).then((res) => {
                console.log('获取智能外呼配置', res)
                const { errCode, data } = res
                if (errCode === 0) {
                    const { openAiPhone, website, aiSeatsNum, appid, privateKey } = data || {}
                    form.openAiPhone = openAiPhone || false
                    form.website = website || ''
                    form.aiSeatsNum = aiSeatsNum || 1
                    form.appid = appid || ''
                    form.privateKey = privateKey || ''
                }
            })
        }
       
    }
)


const form = reactive<IAppSaveRequest>({
    openAiPhone: false,
    aiSeatsNum: 1,
    appid: '',
    orgId: '',
    privateKey: '',
    tenantId: '',
    website: ''
})
const rules = reactive<FormRules<IAppSaveRequest>>({
    openAiPhone: [
        {
            required: true,
            message: '请选择',
            trigger: 'change',
        },
    ],
    website: [
        {
            required: true,
            message: '请输入域名网址',
            trigger: 'change',
        },
    ],
    aiSeatsNum: [
        { required: true, message: '坐席数不能为空', trigger: 'blur' },
        { type: 'number', min: 1, message: '坐席数不能小于1', trigger: 'blur' }
    ],
    appid: [
        {
            required: true,
            message: '请输入appid',
            trigger: 'change',
        },
    ],
    privateKey: [
        {
            required: true,
            message: '请输入appid接口秘钥',
            trigger: 'change',
        },
    ],
})
const emit = defineEmits(['update:visible'])
const handleClose = () => {
    console.log('关闭弹窗')
    emit('update:visible', false)
    form.openAiPhone = false
    form.aiSeatsNum = 1
    form.appid = ''
    form.orgId = ''
    form.privateKey = ''
    form.tenantId = ''
    form.website = ''
}
const handleCancel = () => {
    handleClose()
}
const submitForm = async (formEl: FormInstance | undefined) => {
    // console.log('form', form)
    if (!formEl) return
    submitLoading.value = true
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            // console.log('props.tenantInfo',props.tenantInfo)
            form.orgId = props.tenantInfo.orgId as string
            form.tenantId = props.tenantInfo.id as string
            outboundService.appSave(form).then(() => {
                ElMessage.success('智能外呼配置成功')
                submitLoading.value = false
                handleClose()
            })
        } else {
            console.log('form表单效验不通过', fields)
            submitLoading.value = false
        }
    })
}
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        title="智能外呼"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()"
    >
        <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
            <el-form-item  label="智能外呼绑定" prop="openAiPhone">
                <el-switch v-model="form.openAiPhone" :active-value="true" :inactive-value="false" />
            </el-form-item>
            <el-form-item v-if="form.openAiPhone" label="域名网址" prop="website">
                <el-input v-model.trim="form.website"></el-input>
            </el-form-item>
            <el-form-item v-if="form.openAiPhone" label="坐席数" prop="aiSeatsNum">
                <el-input type="number" v-model.number="form.aiSeatsNum" />
            </el-form-item>
            <el-form-item v-if="form.openAiPhone" label="APPID" prop="appid">
                <el-input v-model.trim="form.appid"></el-input>
            </el-form-item>
            <el-form-item v-if="form.openAiPhone" label="APPID接口秘钥" prop="privateKey">
                <el-input v-model.trim="form.privateKey"></el-input>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleCancel()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)" :loading="submitLoading">保存</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
