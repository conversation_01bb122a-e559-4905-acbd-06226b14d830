<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { IGetTopTenResponseItem } from '@/types/indicator'
type IListItem = {
    name: string
    value: number | string
}
const props = defineProps<{
    data: IGetTopTenResponseItem[] | []
}>()
const hasChartData = ref(true)
const chartRef = ref(null)
const option = reactive({
    tooltip: {
        trigger: 'item',
        formatter: ({name, percent}) => `${name} ${Number(percent).toFixed(2)}%`,
    },
    legend: {
        show: true,
        orient: 'vertical',
        icon: 'roundRect',
        right: 10,
        top: 0,
        bottom: 0,
        formatter: '{name}',
        textStyle: {
            padding: [0, 6, 0, 8],
            fontSize: 12,
            lineHeight: 10,
        },
    },
    series: [
        {
            type: 'pie',
            center: ['38%', '50%'],
            data: <IListItem[]>[],
            label: {
                formatter: ({name, percent}) => `${name} ${Number(percent).toFixed(2)}%`,
                position: 'outside',
                fontSize: 12,
                color: 'inherit',
                show: true,
            },
            animationEasing: 'cubicInOut',
            animationDuration: 1000,
        },
    ],
})
const initChart = () => {
    option.series[0].data = props.data.map(({ companyName, ratio }, index) => {
        const dataItem = {
            name: companyName,
            value: ratio,
        }
        // 如果是第11条数据（索引为10），设置固定颜色
        if (index === 10) {
            return {
                ...dataItem,
                itemStyle: {
                    color: '#a6a6a6',
                },
            }
        }

        return dataItem
    })

    const chartDom = chartRef.value
    const myChart = echarts.init(chartDom)
    myChart.setOption(option)
}
watch(
    () => props.data,
    async (newVal) => {
        if (newVal?.length) {
            hasChartData.value = true
            await nextTick()
            initChart()
        } else {
            hasChartData.value = false
        }
    },
    {
        immediate: true,
    }
)
</script>
<template>
    <div v-if="hasChartData" class="width-100 height-100" ref="chartRef"></div>
    <img v-else class="height-100" style="margin: auto" src="@/assets/images/company/qyfx-no-data.png" alt="暂无数据" />
</template>
<style scoped lang="scss"></style>
