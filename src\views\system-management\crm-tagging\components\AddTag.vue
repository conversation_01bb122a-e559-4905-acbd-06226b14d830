<template>
    <el-dialog
        v-model="addTagDialogVisible"
        title="新增"
        width="504"
    >
        <div class="t-margin-8">
            <div class="display-flex justify-between">
                <div class="font-16 color-red r-margin-6">*</div>
                <div class="font-16 color-two-grey b-margin-16">标签名称</div>
            </div>
            <el-input class="b-margin-16" v-model="tagName" placeholder="请输入标签名称" style="width: 100%;" maxlength="10"></el-input>
            <div class="display-flex justify-between">
                <div class="font-16 color-red r-margin-6">*</div>
                <div class="font-16 color-two-grey b-margin-16">标签颜色</div>
            </div>
            <div class="lr-padding-16">
                <a v-for="item in colors" :key="item.id" :value="item.color" class="r-margin-16">
                    <el-tag
                        class="pointer no-border"
                        style="width: 28px;height: 28px;"
                        :color="item.color"
                        @click="handleTagClick(item.id)"
                        round="false"
                    >
                        <Icon v-if="item.id === selectedTag" color="#fff" icon="icon-a-huaban74" size="large"></Icon>
                        <!-- <el-icon v-if="item.id === selectedTag" color="#fff" class="display-flex justify-center align-center" size="100px"><Check /></el-icon> -->
                    </el-tag>
                </a>
            </div>
        </div>
        <div class="display-flex justify-flex-end all-padding-10 t-margin-20">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="loading">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>

import { ref, onMounted, computed } from 'vue'
import crmService from '@/service/crmService'
import type { IAddTagParams } from '@/types/lead'
import { ElMessage } from 'element-plus'
import Icon from '@/components/common/Icon.vue'

const addTagParams = ref<IAddTagParams>({
    tagName: '',
    color: ''
})
const props = defineProps<{
    visible: boolean,
    refresh: () => void;
}>()

const emit = defineEmits(['update:visible'])

const addTagDialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const tagName = ref('')
const colors = [
    {
        id: 1,
        color: '#7ACCCC'
    }, 
    {
        id: 2,
        color: '#7AA4CC'
    },
    {
        id: 3,
        color: '#4F73DE'
    },
    {
        id: 4,
        color: '#9977D4'
    },
    {
        id: 5,
        color: '#AA9F70'
    },
    {
        id: 6,
        color: '#7AAC4E'
    },
    {
        id: 7,
        color: '#CF7CC5'
    },
    {
        id: 8,
        color: '#C8C985'
    },
    {
        id: 9,
        color: '#DF974E'
    },
    {
        id: 10,
        color: '#E84838'
    }
]

// selectedTag用来选择颜色
const selectedTag = ref(1)

const loading = ref(false)
const handleTagClick = (id: number) => {
    selectedTag.value = id
}

const cancel = () => {
    addTagDialogVisible.value = false
    tagName.value = ''
    selectedTag.value = 1
    props.refresh()
}

const confirm = () => {
    loading.value = true
    if (!tagName.value) {
        ElMessage.error('标签名称不能为空')
    }else{
        addTagParams.value.tagName = tagName.value
        addTagParams.value.color = colors.find(item => item.id === selectedTag.value)!.color
        crmService.crmTagAdd(addTagParams.value).then(res => {
            if (res.success) {
                ElMessage.success('新增成功')
                tagName.value = ''
                selectedTag.value = 1
                addTagDialogVisible.value = false
                props.refresh()
            }else{
                ElMessage.error(res.errMsg)
            }
        })
    }
    loading.value = false
}

onMounted(() => {
})
</script>

<style lang='scss' scoped>
.no-border {
    border: none;
}
</style>