<script lang="ts" setup>
import crmService from '@/service/crmService'
import systemService from '@/service/systemService'
import type { IUserListItem, IUserListRequest } from '@/types/org'
import type { IServiceDetailResponse, IServiceDetailRules } from '@/types/service'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'

// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================

// ====================== Store & Computed Values ======================
const userList = computed(() => {
    const temp: IUserListItem[] = JSON.parse(JSON.stringify(userListRef.value))
    temp.map((user, index) => {
        const target = selectedUser.value.find((e) => e.id === user.id)
        if (!target) {
            temp[index].belong = -1
        } else {
            temp[index].belong = target.index
        }
    })

    return temp
})

const rules = computed(() => {
    if (!data.value) return []
    return rulesRef.value
})

// ====================== Refs & Reactive State ======================
const type = 1
const data = ref<IServiceDetailResponse | null>(null)
const userListRef = ref<IUserListItem[]>([])
const queryingUser = ref(false)
const status = ref(false)
const selectedUser = ref<
    {
        id: string
        index: number
    }[]
>([])
const rulesRef = ref<IServiceDetailRules[]>([])
// ====================== Methods ======================
const getUsers = () => {
    queryingUser.value = true
    let params: IUserListRequest = {}

    try {
        systemService
            .userList(params)
            .then((res) => {
                queryingUser.value = false
                userListRef.value = res
            })
            .finally(() => {
                queryingUser.value = false
            })
    } catch (error) {
        console.log(error)
        queryingUser.value = false
    }
}

const onSelectChange = (selected: string[], index: number) => {
    selected.map((e) => {
        selectedUser.value.push({
            id: e,
            index,
        })
    })

    updateDetail()
}

const getDetail = () => {
    crmService.serviceDetail({ type }).then((res) => {
        data.value = res
        rulesRef.value = res.rules
        status.value = res.isOpen
        res.rules.map((e, index) => {
            e.users.map((e) => {
                selectedUser.value.push({
                    id: e,
                    index: index,
                })
            })
        })
    })
}

const onSwitchChange = (v: boolean) => {
    crmService.serviceSwitch({ type, isOpen: v })
}

const onNumChange = () => {
    updateDetail()
}

const updateDetail = () => {
    if (!data.value) return

    const rules = getValidRules()

    crmService
        .serviceUpdate({
            id: data.value.id,
            type,
            isOpen: status.value,
            rules: rules,
        })
        .then((res) => {
            const { errCode, errMsg } = res || {}
            if (errCode === 0) {
                console.log('成功')
            } else {
                ElMessage({
                    type: 'error',
                    message: errMsg,
                })
                if (!data.value) return
                getDetail()
            }
        })
}

const addNewRule = () => {
    if (!data.value) return
    rulesRef.value.push({
        num: 1,
        allUser: false,
        users: [],
    })
}

const getValidRules = () => {
    const list = rulesRef.value.filter((e) => e.users.length > 0 || e.allUser)
    return list
}

const deleteRule = (index: number) => {
    rules.value.splice(index, 1)
    selectedUser.value.splice(index, 1)
    updateDetail()
}

onMounted(() => {
    getDetail()
    getUsers()
})
</script>

<template>
    <div>
        <div class="flex flex-row top-bottom-center">
            <div class="flex flex-row gap-8 top-bottom-center font-16">
                <el-switch
                    v-model="status"
                    size="large"
                    inline-prompt
                    active-text="开启"
                    inactive-text="关闭"
                    @change="onSwitchChange"
                />
                <span>限制每个员工拥有的最大线索数</span>
            </div>
            <div class="flex flex-1 justify-flex-end">
                <el-button type="primary" @click="addNewRule">新增规则</el-button>
            </div>
        </div>
        <el-table :data="rules" style="width: 100%">
            <el-table-column prop="num" label="最大线索数">
                <template #default="scope">
                    <el-input-number
                        v-model="scope.row.num"
                        :min="0"
                        :max="999999999"
                        :step="1"
                        step-strictly
                        @change="onNumChange"
                    >
                        <template #suffix>
                            <span>条</span>
                        </template>
                    </el-input-number>
                </template>
            </el-table-column>
            <el-table-column prop="taskName" label="规则针对人员">
                <template #default="scope">
                    <div v-if="scope.row.allUser">所有员工（默认）</div>
                    <el-select
                        v-if="!scope.row.allUser"
                        multiple
                        v-model="scope.row.users"
                        placeholder="选择针对人员"
                        style="width: 302px"
                        @change="(v: string[]) => onSelectChange(v, scope.$index)"
                        :loading="queryingUser"
                        @focus="getUsers"
                    >
                        <el-option
                            :label="user.nickname"
                            :value="user.id"
                            :key="user.id"
                            v-for="user in userList"
                            :disabled="user.belong !== -1 && user.belong !== scope.$index"
                        />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="taskName" width="60">
                <template #default="scope">
                    <el-button
                        text
                        type="primary"
                        :icon="Delete"
                        circle
                        @click="deleteRule(scope.$index)"
                        v-if="!scope.row.allUser"
                    />
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<style lang="scss" scoped>
:deep(.el-input-number) {
    width: 160px;
}
</style>
