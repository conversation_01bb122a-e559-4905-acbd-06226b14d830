<script lang="ts" setup>
import type { IAutoDialerTaskDetailFilter, IAutoDialerTaskDetailInfoItem } from '@/types/autoDialer'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
// import NumberListFilter from './components/NumberListFilter.vue'
import NumberListTable from './components/NumberListTable.vue'
import SearchBox from '@/components/common/SearchBox.vue'

// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================
defineProps<{
    taskDetail: IAutoDialerTaskDetailInfoItem | null
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const filterParams = ref<IAutoDialerTaskDetailFilter>()
const route = useRoute()
const taskCode = route.query.taskCode

// ====================== Methods ======================

const filterQuery = (params: IAutoDialerTaskDetailFilter) => {
    const { date } = params || {}
    if (date && Array.isArray(date) && date.length === 2) {
        params.startCallTime = date[0] as string
        params.endCallTime = date[1] as string
        delete params['date']
    }
    filterParams.value = params
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-column gap-16 height-100 t-padding-14">
        <div class="flex flex-row gap-24 font-14">
            <div><span class="color-three-grey">号码总数：</span>{{ taskDetail?.outboundTotal || '-' }}</div>
            <div><span class="color-three-grey">号码已接通：</span>{{ taskDetail?.connectedCount || '-' }}</div>
            <div><span class="color-three-grey">号码未接通：</span>{{ taskDetail?.missedCount || '-' }}</div>
            <div><span class="color-three-grey">号码未拨打：</span>{{ taskDetail?.notDialedCount || '-' }}</div>
        </div>
        <div class="back-color-white border-radius-4">
            <!-- <NumberListFilter :get-data="filterQuery" /> -->
            <SearchBox :searchOptionKey="'DIALING_TASK_DETAILE_NUMBER_SEARCH_OPTIONS'"  @updateSearchParams="filterQuery"/>
        </div>
        <div class="back-color-white height-100 oh">
            <NumberListTable :filter-params="filterParams" :task-code="taskCode?.toString() || ''" />
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
