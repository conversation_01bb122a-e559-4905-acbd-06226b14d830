<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import type { ModelRes } from '@/types/home'
import type { IAicConditionDataOptionItem } from '@/types/aic'
import type { ITenant } from '@/types/user'
import { watch, computed, ref, onMounted, reactive } from 'vue'
import aicService from '@/service/aicService'
import homeService from '@/service/homeService'
import { useStore } from 'vuex'
const store = useStore()
const props = defineProps<{
    data: ModelRes[]
    tenantInfo: ITenant | undefined
}>()
const chooseAreaCode = ref('')
const chooseIndustry = ref('')
const staticArea = ref<IAicConditionDataOptionItem[]>([])
const staticIndustry = ref<IAicConditionDataOptionItem[]>([])
const areaOptions = computed(() => {
    if (!staticArea.value.length) return []
  
    const areaCode = props.tenantInfo?.areaCode
    if (!areaCode) return staticArea.value
  
    const city = staticArea.value.find(city => city.value === areaCode.slice(0, 2))
    if (!city?.children?.length) return []
  
    return city.children.filter(town => town.value === areaCode) || []
})
const cascaderProps = {
    label: 'label', // 自定义显示字段为 label
    value: 'value', // 自定义选中值字段为 value
    children: 'children', // 自定义子节点字段
}
const dataList = ref([
    {
        groupName: '区域企业总览',
        children: [
            {
                label: '存续企业总数',
                value: '',
            },
            {
                label: '规上企业',
                value: '',
            },
        ],
    },
    {
        groupName: '投资创收概览',
        children: [
            {
                label: '本年度新增投资总额',
                value: '',
            },
            {
                label: '资产总额',
                value: '',
            },
            {
                label: '资产总额中位数',
                value: '',
            },
            {
                label: '实现营业收入',
                value: '',
            },
            {
                label: '实现利润总额',
                value: '',
            },
            {
                label: '利润总额中位数',
                value: '',
            },
        ],
    },
    {
        groupName: '社保就业情况概览',
        children: [
            {
                label: '实行就业人数',
                value: '',
            },
            {
                label: '新增就业人数',
                value: '',
            },
            {
                label: '总社保人数',
                value: '',
            },
            {
                label: '社保人数中位数',
                value: '',
            },
            {
                label: '平均工资',
                value: '',
            },
            {
                label: '中位数工资',
                value: '',
            },
        ],
    },
    {
        groupName: '税收情况概览',
        children: [
            {
                label: '税收总额',
                value: '',
            },
            {
                label: '税收中位数',
                value: '',
            },
            {
                label: '税收同比',
                value: '',
            },
        ],
    },
])
const dealDataRes = (res: ModelRes[]) => {
    res.map((item) => {
        const [key, value] = Object.entries(item)[0]
        dataList.value.forEach((item) => {
            let childrenItem = item.children.filter((o) => o.label === key)
            if (childrenItem.length) {
                childrenItem[0].value = value.indexValue
            }
        })
    })
}
watch(
    () => props.data,
    (newVal) => {
        if (newVal) {
            dealDataRes(props.data)
        }
    },
    { immediate: true }
)
const getStaticData = async() => {
    let staticConfigRes = await aicService.conditionGetData({}) 
    store.dispatch('app/setStaticConfig', staticConfigRes)
    staticArea.value = staticConfigRes.area
    staticIndustry.value = staticConfigRes.industry
}
onMounted(async () => {
    getStaticData()
})
const changeArea = (e: string[] | undefined) => {
    if (e) {
        chooseAreaCode.value = e[e.length - 1]
    } else {
        chooseAreaCode.value = ''
    }
}
const queryParams = reactive({
    code: props.tenantInfo?.areaCode ? props.tenantInfo?.areaCode : '3201',
    roundName: '',
    industryCode: '',
    params: [
        {
            modelCode: 'Model007',
            code: <string[]>[],
            roundName: [],
            industryCode: <string[]>[],
        },
    ],
})
watch(
    [chooseAreaCode, chooseIndustry], // 直接传 ref 对象，不需要 .value
    ([newAreaCode, newIndustry]) => {
        queryParams.params[0].code = newAreaCode ? [newAreaCode] : []
        queryParams.params[0].industryCode = newIndustry ? [newIndustry] : []
        getModel007Data()
    }
)
const getModel007Data = () => {
    homeService.searchHomeData(queryParams).then((res) => {
        const { Model007 } = res
        if (Model007) {
            dealDataRes(Model007)
        }
    })
}
</script>
<template>
    <div class="qyqyyt">
        <div class="b-margin-24 display-flex space-between top-bottom-center">
            <ModuleTitle title="全域企业云图"></ModuleTitle>
            <div class="display-flex">
                <div class="w-158 r-margin-24">
                    <el-cascader
                        :options="areaOptions"
                        clearable
                        placeholder="地区"
                        :props="cascaderProps"
                        @change="changeArea"
                        popper-class="custom-cascader-dropdown"
                    />
                </div>
                <div class="w-158">
                    <el-select v-model="chooseIndustry" placeholder="全部行业" clearable>
                        <el-option
                            v-for="item in staticIndustry"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
            </div>
        </div>
        <ul class="display-flex space-between">
            <li class="qyqyyt-contant" v-for="(item, index) in dataList" :key="index">
                <div class="content-header">{{ item.groupName }}</div>
                <div class="display-flex left-right-center flex-wrap">
                    <div class="content-item" v-for="(obj, index) in item.children" :key="index">
                        <div class="text-center item-title">{{ obj.label }}</div>
                        <div class="text-center item-content">{{ obj.value ? Number(obj.value) : '-' }}</div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</template>
<style scoped lang="scss">
// ul {
//     padding: 0;
//     margin: 0;
//     li {
//         list-style-type: none;
//         padding-left: 0;
//     }
// }
.qyqyyt {
    padding: 16px;
    background-color: #fff;
    .qyqyyt-contant {
        width: 23%;
        padding: 16px;
        font-size: 14px;
        font-weight: 500;
        background:
            linear-gradient(0deg, rgba(248, 249, 253, 1), rgba(248, 249, 253, 1)),
            linear-gradient(90deg, rgb(238, 241, 249) 0%, rgb(229, 232, 247) 100%);
        .content-header {
            text-align: center;
            color: var(--main-black);
            margin-bottom: 24px;
        }
        .content-item {
            min-width: 110px;
            min-height: 80px;
            .item-title {
                // height: 41px;
                color: var(--two-grey);
                margin-bottom: 12px;
            }
            .item-content {
                font-size: 18px;
                font-weight: 700;
            }
        }
    }
}
</style>
