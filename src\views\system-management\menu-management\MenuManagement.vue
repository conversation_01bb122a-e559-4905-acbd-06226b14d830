<script lang="ts" setup>
import systemService from '@/service/systemService'
import type { IMenuResponse, IMenuSysResponse } from '@/types/menu'
import { onMounted, ref } from 'vue'
import { MenuAddDialog } from './components'
import { ElMessage, ElMessageBox } from 'element-plus'
import commonData from '@/js/common-data'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const loading = ref(false)
const list = ref<IMenuResponse[]>([])
const dialogVisible = ref(false)
const updateType = ref('add')
const currentMenu = ref<IMenuResponse | null>(null)
const source = ref<0 | 1 | 2 | 3>(0)
const allowDialog = ref(false)

// ====================== Methods ======================
const getData = () => {
    loading.value = true
    systemService
        .menuTree()
        .then((res: IMenuSysResponse) => {
            const { errCode, data } = res || {}
            if (errCode === 0) {
                list.value = data
            } else {
                list.value = []
            }
        })
        .finally(() => {
            loading.value = false
        })
}

const showAddBth = (row: IMenuResponse) => {
    return row.type !== 3
}

const doAddMenu = () => {
    updateType.value = 'add'
    showAddDilog()
}
const showAddDilog = () => {
    allowDialog.value = true
    setTimeout(() => {
        dialogVisible.value = true
    }, 30)
}

const onUpdateDialogVisible = ({ refresh, closed }: { refresh?: boolean; closed?: boolean }) => {
    dialogVisible.value = false
    currentMenu.value = null
    if (refresh) {
        getData()
    }
    if (closed) {
        allowDialog.value = false
    }
}

const removeMenu = (menu: IMenuResponse) => {
    ElMessageBox.confirm(`是否确认删除菜单/权限（${menu.name}）`, '确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        systemService
            .menuRemove({ menuId: menu.menuId })
            .then((res) => {
                const { errCode } = res
                if (errCode === 0) {
                    ElMessage.success('删除成功')
                    getData()
                } else {
                    ElMessage.warning('删除失败')
                }
            })
            .catch(() => {
                ElMessage.warning('系统错误，删除失败')
            })
    })
}

const changeStatus = (status: number, menu: IMenuResponse) => {
    systemService
        .menuEdit({ ...menu, status })
        .then((res) => {
            const { errCode } = res
            if (errCode === 0) {
                getData()
            } else {
                ElMessage.warning('修改失败')
            }
        })
        .catch(() => {
            ElMessage.warning('系统错误，修改失败')
        })
}

const editMenu = (menu: IMenuResponse) => {
    updateType.value = 'edit'
    currentMenu.value = menu
    showAddDilog()
}

const addSubMenu = (menu: IMenuResponse) => {
    updateType.value = 'add'
    currentMenu.value = menu
    showAddDilog()
}

const getTypeName = (v: number) => {
    const target = commonData.menuTypeList.find((e) => e.value === v)
    if (!target) return ''
    return target.label
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getData()
})
</script>

<template>
    <div class="flex flex-column gap-16 height-100 back-color-white menu-manage-table">
        <div class="flex flex-column flex-1 all-padding-16" style="overflow: hidden">
            <div class="flex flex-row gap-16 top-bottom-center space-between b-margin-16">
                <div class="flex"></div>
                <div class="flex">
                    <el-button type="primary" @click="doAddMenu"> 新增 </el-button>
                </div>
            </div>

            <el-table :data="list" style="width: 100%" v-loading="loading" height="100%" row-key="id">
                <el-table-column prop="sort" label="排序值" />
                <el-table-column prop="name" label="名称" />
                <el-table-column prop="menuId" label="标识" />
                <el-table-column prop="url" label="URL" />
                <el-table-column prop="type" label="类型">
                    <template #default="scope">
                        <div>{{ getTypeName(scope.row.type) }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="type" label="状态">
                    <template #default="scope">
                        <el-dropdown>
                            <el-tag class="pointer" :type="scope.row.status == 1 ? 'success' : 'info'">
                                <div class="flex flex-row gap-4 top-bottom-center">
                                    {{ scope.row.status === 1 ? '启用' : '停用' }} <el-icon><ArrowDown /></el-icon>
                                </div>
                            </el-tag>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="changeStatus(1, scope.row)" v-if="scope.row.status === 0">
                                        启用
                                    </el-dropdown-item>
                                    <el-dropdown-item @click="changeStatus(0, scope.row)" v-if="scope.row.status === 1">
                                        停用
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>

                <el-table-column label="操作" fixed="right" width="140" align="left">
                    <template #default="scope">
                        <div class="display-flex top-bottom-center gap-8">
                            <a class="pointer color-blue" style="text-decoration: none" @click="editMenu(scope.row)">
                                修改
                            </a>
                            <a
                                class="pointer color-blue"
                                style="text-decoration: none"
                                v-if="showAddBth(scope.row)"
                                @click="addSubMenu(scope.row)"
                            >
                                新增
                            </a>

                            <a class="pointer color-blue" style="text-decoration: none" @click="removeMenu(scope.row)">
                                删除
                            </a>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <MenuAddDialog
            v-if="allowDialog"
            :visible="dialogVisible"
            :onClose="onUpdateDialogVisible"
            :type="updateType"
            :current-menu="currentMenu"
            :source="source"
        />
    </div>
</template>

<style lang="scss" scoped>
.menu-manage-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
