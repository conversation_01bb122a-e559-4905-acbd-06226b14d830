<script setup lang="ts">
import type { TabsPaneContext, TabBarInstance } from 'element-plus'
import { ref, onMounted } from 'vue'
import { Organization, RoleMenus, RoleDataScope } from './components'
const scopeRef = ref('org')
const mainContentRef = ref<HTMLDivElement | null>(null)
const tabBarRef = ref<TabBarInstance | null>(null)
const tabPanegheight=ref(0)

const getTabsPaneHeight = () => {
    if (mainContentRef.value && tabBarRef.value) {
        tabPanegheight.value = mainContentRef.value.offsetHeight - tabBarRef.value?.$el?.querySelector('.el-tabs__nav').offsetHeight - 32 - 19
    }
}
onMounted(() => {
    getTabsPaneHeight()
})
const handleClick = (tab: TabsPaneContext, event: Event) => {
    console.log(tab, event)
}
</script>

<template>
    <div class="flex flex-column gap-16 flex-1 internal-organization height-100" ref="mainContentRef">
        <div class="flex flex-column back-color-white all-padding-16 gap-20 border-radius-4 flex-1">
            <el-tabs v-model="scopeRef" @tab-click="handleClick" class="flex-1 flex" ref="tabBarRef">
                <el-tab-pane label="架构及用户设置" name="org" class="height-100">
                    <Organization v-if="scopeRef === 'org'" :height="tabPanegheight"/>
                </el-tab-pane>
                <el-tab-pane label="角色权限" name="roleMenus" class="height-100">
                    <RoleMenus v-if="scopeRef === 'roleMenus'" :height="tabPanegheight"/>
                </el-tab-pane>
                <el-tab-pane label="数据权限" name="roleDataScope" class="height-100">
                    <RoleDataScope v-if="scopeRef === 'roleDataScope'" :height="tabPanegheight"/>
                </el-tab-pane>
            </el-tabs>
            <SearchFilter />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.internal-organization :deep(.el-tabs__header) {
    margin: 0;
}
</style>
