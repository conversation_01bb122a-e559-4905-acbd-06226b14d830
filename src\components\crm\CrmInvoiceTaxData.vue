<template>

    <div class="t-margin-20">
        <div v-if="loadingReportData" v-loading="loadingReportData" class="t-padding-30"></div>
        <div v-if="!loadingReportData">
            <!-- 销项发票信息 -->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">销项发票信息</div>
                <div class="t-margin-16 display-flex flex-wrap">
                    <div class="text-left " style="width: 33.3%;" v-for="(item, index) in cancellationData"
                         :key="index">
                        <div class="all-padding-12 font-14"
                             style="border: 0.5px solid #f0f0f0;background-color: #f5fafe;">{{ item.label }}</div>
                        <div class="all-padding-12" style="border: 0.5px solid #f0f0f0;">{{ item.value }}</div>
                    </div>
                </div>
            </div>
            <!-- 销项开票同比统计（单位：万元） -->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">销项开票同比统计（单位：万元）</div>
                <div class="t-margin-16">
                    <el-table :data="cancellationYoyStatistics"
                              :header-cell-style="{ background: '#F5FAFE', fontWeight: 500, fontSize: '14px' }"
                              :cell-style="{ fontWeight: 500, fontSize: '14px' }" border>
                        <el-table-column prop="time" label="时间" />
                        <el-table-column prop="openTaxNum" label="开票金额" />
                        <el-table-column prop="yoyNum" label="同比金额" />
                    </el-table>
                </div>
            </div>

            <!--  销项开票环比统计（单位：万元）-->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">销项开票环比统计（单位：万元）</div>
                <div class="t-margin-16">
                    <el-table :data="cancellationRingStatistics"
                              :header-cell-style="{ background: '#F5FAFE', fontWeight: 500, fontSize: '14px' }"
                              :cell-style="{ fontWeight: 500, fontSize: '14px' }" border>
                        <el-table-column prop="time" label="时间" />
                        <el-table-column prop="openTaxNum" label="开票金额" />
                        <el-table-column prop="yoyNum" label="环比金额" />
                    </el-table>
                </div>
            </div>

            <!-- 销项开票明细（单位：万元） -->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">销项开票明细（单位：万元）</div>
                <div class="t-margin-16 display-flex">
                    <el-table :data="cancellationTaxPaymentData.month"
                              :header-cell-style="{ background: '#F5FAFE', fontWeight: 500, fontSize: '14px' }"
                              :cell-style="{ fontWeight: 500, fontSize: '14px' }" border>
                        <el-table-column label="时间" prop="month"></el-table-column>
                        <template v-for="(item, index) in cancellationTaxPaymentData.prop" :key="index">
                            <el-table-column :label="item.year">
                                <template #default="scope">
                                    <div>
                                        {{ getTaxPaymentNum(scope.row, item.year,
                                                            cancellationTaxPaymentData.taxPaymentList) }}
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                    </el-table>
                </div>
            </div>

            <!-- 进项发票信息 -->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">进项发票信息</div>
                <div class="t-margin-16 display-flex flex-wrap">
                    <div class="text-left" style="width: 33.3%;" v-for="(item, index) in inComeData" :key="index">
                        <div class="all-padding-12 font-14"
                             style="border: 0.5px solid #f0f0f0;background-color: #f5fafe;">{{
                                 item.label }}</div>
                        <div class="all-padding-12" style="border: 0.5px solid #f0f0f0;">{{ item.value }}</div>
                    </div>
                </div>
            </div>
            <!--  进项开票同比统计  -->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">进项开票同比统计（单位：万元）</div>
                <div class="t-margin-16">
                    <el-table :data="inComeYoyStatistics"
                              :header-cell-style="{ background: '#F5FAFE', fontWeight: 500, fontSize: '14px' }"
                              :cell-style="{ fontWeight: 500, fontSize: '14px' }" border>
                        <el-table-column prop="time" label="时间" />
                        <el-table-column prop="openTaxNum" label="开票金额" />
                        <el-table-column prop="yoyNum" label="同比金额" />
                    </el-table>
                </div>
            </div>
            <!--  进项开票环比统计（单位：万元）-->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">进项开票环比统计（单位：万元）</div>
                <div class="t-margin-16">
                    <el-table :data="inComeRingStatistics"
                              :header-cell-style="{ background: '#F5FAFE', fontWeight: 500, fontSize: '14px' }"
                              :cell-style="{ fontWeight: 500, fontSize: '14px' }" border>
                        <el-table-column prop="time" label="时间" />
                        <el-table-column prop="openTaxNum" label="开票金额" />
                        <el-table-column prop="yoyNum" label="环比金额" />
                    </el-table>
                </div>
            </div>

            <!-- 进项开票明细（单位：万元） -->
            <div class="t-margin-20">
                <div class="font-16 model-title relative">进项开票明细（单位：万元）</div>
                <div class="t-margin-16 display-flex">
                    <el-table :data="inComeTaxPaymentData.month"
                              :header-cell-style="{ background: '#F5FAFE', fontWeight: 500, fontSize: '14px' }"
                              :cell-style="{ fontWeight: 500, fontSize: '14px' }" border>
                        <el-table-column label="时间" prop="month"></el-table-column>
                        <template v-for="(item, idx) in inComeTaxPaymentData.prop" :key="idx">
                            <el-table-column :label="item.year">
                                <template #default="scope">
                                    <div>{{ getTaxPaymentNum(scope.row, item.year, inComeTaxPaymentData.taxPaymentList)
                                    }}
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, inject } from 'vue'
import type { Ref } from 'vue'
import dataTransform from '@/utils/data-transform'
import indicatorService from '@/service/indicatorService'
import type { ILeadData } from '@/types/lead'
import type { IIndicatorItem } from '@/types/indicator'


const loadingReportData: Ref<boolean> = ref(false)


const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

interface normalData {
    size?: string,
    value?: string | number,
    id: number,
    label?: string,
    openTaxNum?: number | string,
    yoyNum?: number | string,
    time?: string
}

const cancellationData: Ref<normalData[]> = ref([
    {
        id: 7001,
        value: '0',
        label: '近24个月开票金额',
        size: '万'
    },
    {
        id: 7002,
        value: '0',
        label: '近12个月断票次数',
        size: '次'
    },
    {
        id: 7003,
        value: '0',
        label: '近12个月下游数量',
        size: '个'
    },
    {
        id: 7004,
        value: '0',
        label: '近12个月最大连续开票天数',
        size: '天'
    },
    {
        id: 7005,
        value: '0',
        label: '近12个月开票金额',
        size: '万'
    }
])

const cancellationYoyStatistics: Ref<normalData[]> = ref([
    {
        time: '近12个月',
        id: 7006,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近9个月',
        id: 7007,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近6个月',
        id: 7008,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近3个月',
        id: 7009,
        openTaxNum: 0,
        yoyNum: 0
    }
])
// 开票环比统计
const cancellationRingStatistics: Ref<normalData[]> = ref([
    {
        time: '近12个月',
        id: 7010,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近9个月',
        id: 7011,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近6个月',
        id: 7012,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近3个月',
        id: 7013,
        openTaxNum: 0,
        yoyNum: 0
    }
])



// 进项发票信息
const inComeData: Ref<normalData[]> = ref([
    {
        id: 7015,
        value: '0',
        label: '近24个月开票金额',
        size: '万'
    },
    {
        id: 7016,
        value: '0',
        label: '近12个月上游数量',
        size: '个'
    },
    {
        id: 7017,
        value: '0',
        label: '近12个月开票金额',
        size: '万'
    }
])
// 进项同比统计
const inComeYoyStatistics: Ref<normalData[]> = ref([
    {
        time: '近12个月',
        id: 7018,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近9个月',
        id: 7019,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近6个月',
        id: 7020,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近3个月',
        id: 7021,
        openTaxNum: 0,
        yoyNum: 0
    }
])
// 进项环比统计
const inComeRingStatistics: Ref<normalData[]> = ref([
    {
        time: '近12个月',
        id: 7022,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近9个月',
        id: 7023,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近6个月',
        id: 7024,
        openTaxNum: 0,
        yoyNum: 0
    },
    {
        time: '近3个月',
        id: 7025,
        openTaxNum: 0,
        yoyNum: 0
    }
])

interface ITaxPayment {
    prop: { year: string }[]
    month: { month: string }[]
    taxPaymentList: [string, string, number][]
}
// 销项开票明细
const cancellationTaxPaymentData: Ref<ITaxPayment> = ref({
    prop: [],
    month: [],
    taxPaymentList: []
})
// 进项开票明细
const inComeTaxPaymentData: Ref<ITaxPayment> = ref({
    prop: [],
    month: [],
    taxPaymentList: []
})
const filterNum = (num: string | number, size: string | null = null) => {
    num = Number(num)
    if (num === 0) {
        return num
    } else if (size) {
        if (['万'].includes(size) && num > 10000) {
            return (num / 10000).toFixed(2) + size
        } else {
            return num + size
        }
    } else {
        return (num / 10000).toFixed(2)
    }
}

// 销项开票信息 进项开票信息
const getComOpenTaxData = (dataItem: IIndicatorItem, list: normalData[]) => {
    list.forEach((item: normalData) => {
        if (dataItem.id === item.id && dataItem.currentTime[0]) {
            item.value = filterNum(dataItem.currentTime[0][0], item.size)
        }
    })
    return list
}
//进项/销项 开票同比/环比统计
const getComStatistics = (dataItem: IIndicatorItem, list: normalData[]) => {
    list.forEach(item => {
        if (dataItem.id === item.id && dataItem.currentTime[0]) {
            item.openTaxNum = filterNum(dataItem.currentTime[0][0])
            item.yoyNum = filterNum(dataItem.currentTime[0][1])
        }
    })
    return list
}

interface getTaxPaymentRes {
    yearList: { year: string }[]
    monthList: { month: string }[]
}
// 进 、销项开票明细
const getTaxPayment = (data: string[] | [string, string, number][]): getTaxPaymentRes => {
    let years: { year: string }[] = []
    let month: { month: string }[] = []
    data.forEach((item) => {
        let newDate = item[1].split('-')
        years.push({ year: item[0] })
        month.push({ month: newDate[1] })
    })
    years = dataTransform.uniqueInArr(years, 'year') as { year: string }[]
    let yearList = years.sort((a, b) => {
        return Number(a.year) - Number(b.year)
    })
    month = dataTransform.uniqueInArr(month, 'month') as { month: string }[]
    let monthList = month.sort((a, b) => {
        return Number(a.month) - Number(b.month)
    })
    return {
        yearList,
        monthList
    }
}
const getTaxPaymentNum = (item: { month: string }, year: string, list: [string, string, number][]) => {
    let s = year + '-' + item.month
    let num = '-' //金额
    list.forEach((m) => {
        if (m[1] === s) { // 直接比较字符串
            num = filterNum(m[2]) + ''
        }
    })
    return num
}
const getIndicatorData = () => {
    loadingReportData.value = true
    let dataIds = []
    for (let i = 7001; i <= 7026; i++) {
        dataIds.push(i + '')
    }
    indicatorService.getDecomposeCompareIndicatorData({ ids: dataIds.toString(), socialCreditCode: crmDetail.value.socialCreditCode }).then(res => {
        // console.log(res)
        let indicatorData = Object.values(res)
        console.log(indicatorData)

        indicatorData.forEach((itemArr: IIndicatorItem[]) => {
            let item = itemArr[0]
            if (item.id === 7014) {
                cancellationTaxPaymentData.value.taxPaymentList = item.currentTime as [string, string, number][]
                let cancellationData = getTaxPayment(item.currentTime) //销项开票明细
                console.log('cancellationData', cancellationData)
                cancellationTaxPaymentData.value.prop = cancellationData.yearList //年份
                cancellationTaxPaymentData.value.month = cancellationData.monthList //月份
            } else if ([7006, 7007, 7008, 7009].includes(item.id)) {
                cancellationYoyStatistics.value = getComStatistics(item, cancellationYoyStatistics.value) //开票同比统计
            } else if ([7010, 7011, 7012, 7013].includes(item.id)) {
                cancellationRingStatistics.value = getComStatistics(item, cancellationRingStatistics.value) //开票环比统计
            } else if ([7001, 7002, 7003, 7004, 7005].includes(item.id)) {
                cancellationData.value = getComOpenTaxData(item, cancellationData.value) //销售发票信息
            } else if ([7015, 7016, 7017].includes(item.id)) {
                inComeData.value = getComOpenTaxData(item, inComeData.value) //进项发票信息
            } else if ([7018, 7019, 7020, 7021].includes(item.id)) {
                inComeYoyStatistics.value = getComStatistics(item, inComeYoyStatistics.value) //进项同比统计
            } else if ([7022, 7023, 7024, 7025].includes(item.id)) {
                inComeRingStatistics.value = getComStatistics(item, inComeRingStatistics.value) //进项环比统计
            } else if (item.id === 7026) {
                inComeTaxPaymentData.value.taxPaymentList = item.currentTime as [string, string, number][] //进项原始数据
                let inComeData = getTaxPayment(item.currentTime) //进项开票明细
                inComeTaxPaymentData.value.prop = inComeData.yearList //年份
                inComeTaxPaymentData.value.month = inComeData.monthList //月份
            }
        })


    }).finally(() => {
        loadingReportData.value = false
    })
}

onMounted(() => {
    getIndicatorData()
})

</script>

<style lang='scss' scoped>
.model-title {
    // border-left: 4px solid var(--main-blue-);
    padding-left: 8px;
}

.model-title::before {
    content: '';
    border: 2px solid var(--main-blue-);
    height: 100%;
    position: absolute;
    left: 0;
    border-radius: 418px;
    height: 100%;
}
</style>