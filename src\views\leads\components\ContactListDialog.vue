<script lang="ts" setup>
import { provide, computed, ref, watch } from 'vue'
import ContactList from '@/components/company/ContactList.vue'
import type { ILeadData } from '@/types/lead'

const props = defineProps<{
    visible: boolean
    companyInfo?: ILeadData
}>()
const dialogVisible = ref(props.visible)
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})
const socialCreditCode = computed(() => {
    return props.companyInfo?.socialCreditCode || ''
})
const companyName = computed(() => {
    return props.companyInfo?.companyName || ''
})
provide('socialCreditCode', socialCreditCode)
provide('companyName', companyName)
const emit = defineEmits(['close','update:visible'])
const handleClose = () => {
    emit('update:visible', false)
}
</script>
<template>
    <div>
        <el-drawer v-model="dialogVisible"  direction="rtl" size="475" @close="handleClose">
            <template #header>
                <div class="title" >{{ props.companyInfo?.companyName }}</div>
            </template>
            <template #default>
                <div>
                    <ContactList/>
                </div>
            </template>
        </el-drawer>
    </div>
    
</template>
<style scoped lang="scss">
.title{
    font-size: 20px;
    color: var(--main-black);
}
:deep(.el-drawer__header){
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
}
</style>
