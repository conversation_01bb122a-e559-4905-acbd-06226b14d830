<template>
    <div class="display-flex flex-column height-100">
        <div class=" b-padding-40 flex-1">
            <el-row :gutter="16">
                <el-col :xs="12" :sm="12" :md="8" :lg="6" :xl="4" v-for="(item) in shareModelList"
                        @click="useTemplete(item)" @mouseover="hoverMarketId = item.id" @mouseleave="hoverMarketId = ''"
                        :key="item.id" draggable="true" @dragover.prevent @dragstart="dragstart(item)" @drop="onDrop(item)"
                        class="b-margin-10 market-item">
                    <div class="flex-1 border all-padding-16 border-radius-4 pointer justify-between top-bottom-center">
                        <div class="display-flex b-padding-24">
                            <div
                                class="name-item text-ellipsis flex-1 font-weight-500  font-first-title-active r-padding-5">
                                {{ item.name }}
                            </div>
                            <div class="icon-box border-radius-4 display-flex left-right-center top-bottom-center"
                                 :class="hoverMarketId == item.id ? 'back-color-blue' : ''">
                                <Icon icon="icon-icon_city" size="20px"
                                      :color="hoverMarketId == item.id ? commonColor.mainWhite : commonColor.mainBlue">
                                </Icon>
                            </div>
                        </div>
                        <div class="display-flex space-between font-14 color-two-grey">
                            <div>创建者：{{ item.createUserName }}</div>
                            <div class="color-two-grey">{{ item.shareTime ? moment(item.shareTime).format('YYYY/MM/DD') :  '-' }}</div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>

        <div v-if="!shareModelList.length" class="flex-grow-1 top-bottom-center">
            <el-empty />
        </div>
        <div class="page-box display-flex justify-flex-end tb-padding-10">
            <el-pagination @change="getUserShareTemplate()" v-model:page-size="pageInfo.pageSize"
                           :page-sizes="[10, 30, 50, 100]" v-model:current-page="pageInfo.page"
                           layout="total,sizes,prev, pager, next" :total="pageInfo.total" />
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, getCurrentInstance, reactive } from 'vue'
import { useRouter } from 'vue-router'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import moment from 'moment'

import { ElMessage } from 'element-plus'
import type { ISearchGetTemplateItem, ISearchGetTemplateResponse } from '@/types/company'
import type { ISearchGetEntTemplateItem } from '@/types/aic'

const shareModelList: Ref<ISearchGetTemplateItem[]> = ref([])
const hoverMarketId = ref('')
const instance = getCurrentInstance()
const commonColor = instance?.appContext.config.globalProperties.$commom.color
const router = useRouter()
const pageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})

const startTemplete: Ref<ISearchGetTemplateItem> = ref({} as ISearchGetTemplateItem)


const dragstart = (templateItem: ISearchGetTemplateItem) => {
    startTemplete.value = templateItem
}
const onDrop = (templateItem: ISearchGetTemplateItem) => {
    aicService.searchUpdateTemplate({
        sort: templateItem.sort,
        templateId: startTemplete.value.id
    }).then(() => {
        getUserShareTemplate()
    })
}


const useTemplete = (item: ISearchGetEntTemplateItem) => {
    router.push({
        name: 'more-search-company',
        params: {
            templeteId: item.id,
            type: 'share'
        },
    })
}

const getUserShareTemplate = () => {
    aicService.searchGetEntTemplate({
        page: pageInfo.page,
        pageSize: pageInfo.pageSize
    }).then((res: ISearchGetTemplateResponse) => {
        const { data, errCode, total } = res
        if (errCode === 0) {
            shareModelList.value = data
            pageInfo.total = total
        } else {
            shareModelList.value = []
            ElMessage({
                type: 'error',
                message: '获取用户分享的模板失败',
            })
            return   
        }
    })
}

onMounted(() => { getUserShareTemplate() })
</script>

<style lang='scss' scoped>
.market-item {
    height: 170px;

    .name-item {
        height: 70px;
    }

    &:hover {
        border-color: var(--main-blue-);
    }

    .icon-box {
        width: 40px;
        height: 40px;
        box-shadow: 0px 2px 4px rgb(0, 0, 0, 0.1);
    }

    .menu-box {
        border-top: 1px solid var(--border-color);
    }
}

.font-first-title-active {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 3;
    text-overflow: ellipsis;
    line-height: 1.5;
    height: 50px;
    overflow: hidden;
}

.page-box {
    height: 50px;
}
</style>