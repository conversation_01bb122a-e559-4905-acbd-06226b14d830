<template>
    <el-dialog
        v-model="countTagDialogVisible"
        title="删除标签"
        width="700"
        style="height: 366px;"
    >
        <div class="color-two-grey font-16 font-weight-400 b-margin-16">该标签已关联了企业，删除后将取消关联关系，请确认是否删除？</div>
        <el-table
            :data="props.countData"
            style="width: 100%;height: 200px;"
            empty-text="暂无数据"
            show-overflow-tooltip
            :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
        >
            <el-table-column label="标签所属" prop="name" min-width="150"></el-table-column>
            <el-table-column label="关联数量" prop="num" min-width="150"></el-table-column>
            <el-table-column
                fixed="right"
                label="操作"
                width="150px"
            >
                <template #default="scope">
                    <div v-if="isShowButton(scope.row)">
                        <a class="font-14 pointer color-blue" @click="linkToList(scope.row)">查看</a> 
                    </div>
                    <div v-else>
                        <a class="not-allow" style="color: #8CB2FF;">查看</a>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <!-- <div class="tb-padding-16 font-16 ">
            <div class="color-black display-flex center font-weight-600" style="width: 100%; height: 50px;background-color: #F5F7FA;" >
                <div style="width: 35%;">标签所属</div>
                <div style="width: 35%;">关联数量</div>
                <div style="width: 20%;">操作</div>
            </div>
            <div class="border"></div>
            <div class="color-black display-flex center" style="width: 100%; height: 50px;"> 
                <div style="width: 35%;">线索列表</div>
                <div style="width: 35%;">{{ props.countData.lead }}</div>
                <div class="color-primary font-16 pointer" style="width: 20%;" @click="goTolist">查看</div>
            </div>
            <div class="border"></div>
            <div class="color-black display-flex center" style="width: 100%; height: 50px;"> 
                <div style="width: 35%;">线索池</div>
                <div style="width: 35%;">{{ props.countData.leadPool }}</div>
                <div class="color-primary font-16 pointer" style="width: 20%;" @click="goTolist">查看</div>
            </div>
            <div class="border"></div>
            <div class="color-black display-flex center" style="width: 100%; height: 50px;"> 
                <div style="width: 35%;">客户列表</div>
                <div style="width: 35%;">{{ props.countData.custom }}</div>
                <div class="color-primary font-16 pointer" style="width: 20%;" @click="goTolist">查看</div>
            </div>
            <div class="border"></div>
            <div class="color-black display-flex center" style="width: 100%; height: 50px;"> 
                <div style="width: 35%;">客户公海</div>
                <div style="width: 35%;">{{ props.countData.customPool }}</div>
                <div class="color-primary font-16 pointer" style="width: 20%;" @click="goTolist">查看</div>
            </div>
            <div class="border"></div>
        </div> -->
        <div class="display-flex justify-flex-end t-margin-16">
            <el-button class="font-16 lr-padding-16 tb-padding-8" style="width:68px; height: 40px;" @click="handleClose">关 闭</el-button>
            <el-button class="font-16 lr-padding-16 tb-padding-8" type="primary" style="width:68px; height: 40px;" @click="handelDelete" >删 除</el-button>
        </div>
    </el-dialog>>
</template>

<script lang='ts' setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type {ITagCountResponseData} from '@/types/lead'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'
import crmService from '@/service/crmService'

const router = useRouter()
const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})
const props = defineProps<{
    refresh: () => void;
    visible: boolean,
    countData:ITagCountResponseData[],
    tagId:string
}>()
const emit = defineEmits(['update:visible'])
const countTagDialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const handleClose = () => {
    countTagDialogVisible.value = false
}


const remove = (id: string) => {
    crmService.crmTagDelete(id).then ((res) => {
        if (res.success) {
            ElMessage.success('删除成功')
            props.refresh()
        }else{
            ElMessage.error(res.errMsg)
        }
    })
}

const handelDelete = () => {
    ElMessageBox.confirm(`确认删除该标签吗？`,'提示',{
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        await remove(props.tagId)
        countTagDialogVisible.value = false
    }).finally(() => {
        
    })
    
}

const isShowButton = (row: ITagCountResponseData) => {
    if(row.member){
        return row.member.includes(user.value!.id)
    }
    return true
}

const linkToList = (row: ITagCountResponseData) => {
    // console.log('1111111',router)
    // console.log(row)
    // console.log(props.tagId)
    const tagIds = [props.tagId]
    if(!row.id && row.name === '线索列表'){
        router.push({
            path:'/leads/lead-list',
            query:{tagIds:tagIds},
        })
    }
    if(!row.id && row.name === '客户列表'){
        router.push({
            path:'/leads/customer-list',
            query:{tagIds:tagIds},
        })
    }
    if(row.id && row.type === 1){
        router.push({
            path:'/leads/lead-pool',
            query:{tagIds:tagIds,clueId:row.id},
        })
    }
    if(row.id && row.type === 2){
        router.push({
            path:'/leads/customer-public-pool',
            query:{tagIds:tagIds,clueId:row.id},
        })
    }
}
</script>

<style lang='scss' scoped>
</style>