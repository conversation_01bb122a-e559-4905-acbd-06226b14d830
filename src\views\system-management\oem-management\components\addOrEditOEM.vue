<template>
    <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="1000px"
        @close="handleClose()"
    >
        <div class="all-paddding-16">
            <div>
                <div class="flex top-bottom-center gap-8 t-margin-16" style="width: 55%;">
                    <span class="font-24 flex top-bottom-center" style="color: #D66353;">*</span>
                    <span class="color-two-grey font-16" style="width: 100px;">OEM KEY</span>
                    <el-input
                        v-model="oem_key"
                        placeholder="请输入OEM KEY"
                        size="large"
                        style="width: 100%;"
                    ></el-input>
                </div>
            </div>
            <el-tabs v-model="activeName" class="demo-tabs t-margin-16" >
                <el-tab-pane label="协同平台" name="xtpt">
                    <XTPTVue v-model="formXTPT" :editData="XTPTData"/>
                </el-tab-pane>
                <el-tab-pane label="经营慧眼" name="jyhy">
                    <JYHYVue v-model="formJYHY" :editData="JYHYData" :dialogVisible="dialogVisible" />
                </el-tab-pane>
                <el-tab-pane label="报告" name="bg">
                    <ReprotVue v-model="formReport" :editData="ReportData"/>
                </el-tab-pane>
                <el-tab-pane label="其他" name="other">
                    <OthersVue v-model="formOthers" :editData="OthersData"/>
                </el-tab-pane>
            </el-tabs>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="submit()" :loading="loading" >
                    确定
                </el-button>
            </div>
        </div>
    </el-dialog>  

</template>

<script lang='ts' setup>
import { ref, watch } from 'vue'
import type { OEMUploadItem } from '@/types/OEM'
import systemService from '@/service/systemService'
import { ElMessage } from 'element-plus'
import XTPTVue from './xtpt.vue'
import JYHYVue from './jyhy.vue'
import ReprotVue from './report.vue'
import OthersVue from './others.vue'
import type { XTPT, JYHY, bgCover, other, IOEMListResponseItem } from '@/types/OEM'

const formXTPT = ref<XTPT>({})
const formJYHY = ref<JYHY>({})
const formReport = ref<bgCover>({})
const formOthers = ref<other>({})
const props = defineProps<{
    visible: boolean;
    dialogTitle:string;
    refresh: () => void;
    editData:IOEMListResponseItem
}>()
// 传参
const updateData = ref<OEMUploadItem>({
    key:'',
    modules:[
        {
            productType:0,
            config:{
               
            }
        },
        {
            productType:1,
            config:{
               
            }
        },
        {
            productType:2,
            config:{
              
            }
        },
        {
            productType:3,
            config:{
               
            }
        },
    ]
})

const dialogVisible = ref(props.visible)
watch(() =>props.visible, (val) => {
    dialogVisible.value = val
})
const loading = ref(false)
const activeName = ref('xtpt')

const XTPTData = ref()
const JYHYData = ref()
const ReportData = ref()
const OthersData = ref()

// 域名相关
const domain = ref('') //域名
const KeyFile = ref('') //密钥文件
const PemFile = ref('') //密钥文件
const oem_key = ref('')
const agreement = ref('1')
watch(oem_key, () => updateData.value.key = oem_key.value, { immediate: true, deep: true })
watch(() => dialogVisible, (val) => {
    if(val){
        if(props.dialogTitle === '编辑'){
            console.log('editData',props.editData)
            XTPTData.value = props.editData.platform
            JYHYData.value = props.editData.cloudService
            ReportData.value = props.editData.report
            OthersData.value = props.editData.other
            console.log('XTPTData.value',XTPTData.value)
            console.log('JYHYData.value',JYHYData.value)
            if(val){
                oem_key.value = JSON.parse(JSON.stringify(props.editData.key))
            }
        } 
    }
},{ immediate: true,deep: true })

watch(() => props.dialogTitle,() => {
    if(props.dialogTitle === '新增'){
        // console.log('新增')
        resetAllData()
    }
})

// 域名相关
watch([domain,agreement, KeyFile, PemFile], () => {
    updateData.value.domain = domain.value
    if(agreement.value === '1'){
        updateData.value.domainProtocol = 'https'
    }else if(agreement.value === '2'){
        updateData.value.domainProtocol = 'http'
    }else{
        updateData.value.domainProtocol = ''
    }
    updateData.value.domainKey = KeyFile.value
    updateData.value.domainPem = PemFile.value
},{ immediate: true, deep: true }) 


// 重置所有数据
const resetAllData = () => {
    oem_key.value = ''
    XTPTData.value = {}
    JYHYData.value = {
        inviteNew:true,
        productMatch:true,
        policyMatch:true,
    }
    ReportData.value = {}
    OthersData.value = {}

    // 重置 updateData
    updateData.value = {
        id:'',
        key:'',
        domainKey:'',
        domainPem:'',
        modules:[
            {
                productType:0,
                config:{

                }
            },
            {
                productType:1,
                config:{

                }
            },
            {
                productType:2,
                config:{

                }
            },
            {
                productType:3,
                config:{

                }
            },
        ]
    }
}

const handleUrl = (val: string | undefined) => {
    if(val && val.includes('http')){
        return val.split('com/')[1]
    }
    
    if(val && val.startsWith('/upload')) {
        return val.replace('/upload/', 'upload/')
    }
    return val
}

// 统一处理URL字段的函数
const processUrlFields = (config: Record<string, unknown>, fields: string[]) => {
    fields.forEach(field => {
        const value = config[field]
        if (value && typeof value === 'string') {
            config[field] = handleUrl(value)
        }
    })
}

const handelUpdateParams = () => {
    // 更新配置数据
    updateData.value.modules[0].config = formXTPT.value
    updateData.value.modules[1].config = formJYHY.value
    updateData.value.modules[2].config = formReport.value
    updateData.value.modules[3].config = formOthers.value

    // 定义需要处理URL的字段映射
    const urlFieldsMap = [
        {
            moduleIndex: 0,
            fields: ['domainKey', 'domainPem', 'loginLogo', 'logoBk', 'logoImg']
        },
        {
            moduleIndex: 1,
            fields: ['domainKey', 'domainPem', 'mpHomeBanner', 'mpHomeSpec', 'mpLogo', 'mpQrCode', 'qrCodeImg']
        },
        {
            moduleIndex: 2,
            fields: ['gqbgCover', 'swbgCover', 'fpbgCover']
        }
    ]

    // 批量处理URL字段
    urlFieldsMap.forEach(({ moduleIndex, fields }) => {
        processUrlFields(updateData.value.modules[moduleIndex].config, fields)
    })
}

const save = () => {
    handelUpdateParams()
    console.log('updateData.value',updateData.value)
    loading.value = true
    systemService.systemOEMSave(updateData.value).then(res => {
        console.log(res)
        if(res.success){
            ElMessage.success('保存成功')
            resetAllData() 
            handleClose()
            props.refresh()
        }else{
            ElMessage.error(res.errMsg)
        }
        loading.value = false
    })
}

const editConfirm = () => {
    updateData.value.id = props.editData.id
    handelUpdateParams()
    console.log('updateData.value',updateData.value)
    loading.value = true
    systemService.systemOEMEdit(updateData.value).then(res => {
        console.log(res)
        if(res.success){
            ElMessage.success('编辑成功')
            resetAllData() 
            handleClose()
            props.refresh()
        }else{
            ElMessage.error(res.errMsg)
        }
        loading.value = false
    })
}

const submit = () => {
    if(props.dialogTitle === '新增'){
        save()
    }else if(props.dialogTitle === '编辑'){
        editConfirm()
    }
}

const emit = defineEmits(['update:visible'])
const handleClose = () => {
    emit('update:visible',false)
}

</script>

<style lang='scss' scoped>

.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    margin-top: 20px;
}


</style>
