<script setup lang="ts">
import { Checkbox } from '@/components/enterprise/ui'
import { Search } from '@element-plus/icons-vue'
import { onMounted, ref, watch } from 'vue'
const props = defineProps<{
    defaultValue: string
    placeholder: string
    onSearch?: (v: string) => void
    onMatchTypechange: (v: string, keyword: string) => void
    onKeywordChange: (v: string) => void
    matchType: string
}>()

const input = ref('')
const mathType = ref('most_fields')

watch(
    () => props.defaultValue,
    (value) => {
        input.value = value
    }
)

watch(
    () => props.matchType,
    (value) => {
        mathType.value = value
    }
)

const submit = () => {
    if (!props.onSearch) return
    props.onSearch(input.value)
}

const onInput = (v: string) => {
    input.value = v
    props.onKeywordChange(input.value)
}

const onChange = (v: string) => {
    if (v === 'most_fields') {
        mathType.value = 'phrase_prefix'
        props.onMatchTypechange('phrase_prefix', input.value)
    }

    if (v === 'phrase_prefix') {
        mathType.value = 'most_fields'
        props.onMatchTypechange('most_fields', input.value)
    }
}

onMounted(() => {
    input.value = props.defaultValue
})
</script>

<template>
    <div class="flex flex-row gap-16 top-bottom-center">
        <el-input
            v-model="input"
            style="width: 412px; height: 40px"
            :placeholder="placeholder"
            :prefix-icon="Search"
            clearable
            @input="onInput"
            @keyup.enter="submit"
        />
        <el-button type="primary" style="height: 40px; width: 68px" @click="submit">搜索</el-button>
        <el-tooltip effect="light" placement="top-start">
            <template #content>
                1. 完全匹配：表示输入的词句不会根据分词规则拆分。例如“网络信息”，不会拆成“网络”、“信息”两个词组合搜索；
                <br />
                2. 多个关键词可通过符号隔开组合搜索。（“空格”表示“且”；“+”表示“或”）
            </template>

            <Checkbox
                key="matchType"
                label="完全匹配"
                :value="mathType"
                :checked="mathType === 'phrase_prefix'"
                :onChange="onChange"
            />
        </el-tooltip>
    </div>
</template>

<style scoped></style>
