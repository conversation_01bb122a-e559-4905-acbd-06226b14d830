<script lang="ts" setup>
import { computed, nextTick, ref } from 'vue'
import type { Region } from '@/types/aic'
import { formatRegionLabel } from '@/utils/enterprise/region'

const props = defineProps<{
    regions: Region[]
}>()

const selectedRegions = ref<Region[]>([])

// 展开的城市
const expandedCities = ref<{ province: Region; city: Region }[]>([])

const hotRegions = ref<Region[]>([
    { value: '0', label: '全国', pinyin: 'quanguo' },
    { value: '11', label: '北京', pinyin: 'beijing', parent: { label: '北京', value: '11', pinyin: 'beijing' } },
    { value: '31', label: '上海', pinyin: 'shanghai', parent: { label: '上海', value: '31', pinyin: 'shanghai' } },
    { value: '4401', label: '广州', pinyin: 'guangzhou', parent: { label: '广东', value: '44', pinyin: 'guangdong' } },
    { value: '4403', label: '深圳', pinyin: 'shenzhen', parent: { label: '广东', value: '44', pinyin: 'guangdong' } },
    { value: '3301', label: '杭州', pinyin: 'hangzhou', parent: { label: '浙江', value: '33', pinyin: 'zhejiang' } },
    { value: '4201', label: '武汉', pinyin: 'wuhan', parent: { label: '湖北', value: '42', pinyin: 'hubei' } },
])

const hotCities = computed(() => {
    let cities: { province: Region; city: Region }[] = []

    for (let index = 0; index < hotRegions.value.length; index++) {
        const region = hotRegions.value[index]
        const { parent } = region || {}

        if (!parent) continue

        const province = props.regions.find((e) => e.value === parent?.value)

        if (!province) continue

        cities.push({
            province: province,
            city: region,
        })
    }

    return cities
})

// 所有字母
const letters = computed(() => {
    return regionGroups.value.map((group) => group.letter)
})

// 处理地区数据，添加首字母
const processedRegions = computed(() => {
    return props.regions.map((region) => ({
        ...region,
        // 使用拼音首字母大写
        firstLetter: region.pinyin?.charAt(0)?.toUpperCase() || '',
    }))
})

const regionGroups = computed(() => {
    const groups: Record<string, Region[]> = {}

    processedRegions.value.forEach((region) => {
        const letter = region.firstLetter || ''
        if (!/^[A-Z]$/.test(letter)) {
            // 如果不是A-Z的字母，归类到"#"
            if (!groups['#']) {
                groups['#'] = []
            }
            groups['#'].push(region)
        } else {
            if (!groups[letter]) {
                groups[letter] = []
            }
            groups[letter].push(region)
        }
    })

    const result = Object.keys(groups)
        .sort((a, b) => {
            if (a === '#') return 1
            if (b === '#') return -1
            return a.localeCompare(b)
        })
        .map((letter) => ({
            letter,
            provinces: groups[letter],
        }))

    // 转换为数组并排序（#放在最后）
    return result
})

// 移除子地区
const removeChildRegions = (parent: Region) => {
    if (!parent?.value) return

    selectedRegions.value = selectedRegions.value.filter((region) => {
        // 如果region有parent，检查是否是parent的子级
        if (region.parent) {
            return region.parent.value !== parent.value
        }
        return true
    })
}

// 切换区县选择
const toggleDistrict = (district: Region, city: Region, province: Region) => {
    if (!district?.value || !city?.value || !province?.value) return

    // 检查是否已选择
    const index = selectedRegions.value.findIndex((selected) => selected.value === district.value)

    if (index >= 0) {
        // 已选择，取消选择
        selectedRegions.value.splice(index, 1)
    } else {
        // 未选择，添加选择
        // 先移除所有子地区
        removeChildRegions(district)

        // 检查是否选择了父级（城市或省份）
        const cityIndex = selectedRegions.value.findIndex((selected) => selected.value === city.value)
        const provinceIndex = selectedRegions.value.findIndex((selected) => selected.value === province.value)

        if (cityIndex >= 0) {
            // 如果选择了城市，则替换为区县
            selectedRegions.value.splice(cityIndex, 1)
        } else if (provinceIndex >= 0) {
            // 如果选择了省份，则替换为区县
            selectedRegions.value.splice(provinceIndex, 1)
        }

        addSelectedRegions({
            ...district,
            parent: city,
        })

        if (isCityAllSelected(city)) {
            toggleAllDistrict(city, province)
        }
    }
}

const toggleAllCity = (province: Region) => {
    // 检查是否已选择
    const index = selectedRegions.value.findIndex((selected) => selected.value === province.value)

    if (index >= 0) return

    removeChildRegions(province)

    addSelectedRegions({
        ...province,
    })
}

const toggleAllDistrict = (city: Region, province: Region) => {
    // 检查是否已选择
    const index = selectedRegions.value.findIndex((selected) => selected.value === city.value)

    if (index >= 0) return

    removeChildRegions(city)

    addSelectedRegions({
        ...city,
        parent: province,
    })
}

const toggleCity = async (city: Region, province: Region) => {
    if (!city?.value) return

    // 过滤掉同省份的记录
    expandedCities.value = expandedCities.value.filter((item) => item.province.value !== province.value)

    // 插入新的记录
    expandedCities.value.push({
        province,
        city,
    })

    await nextTick() // 等待 DOM 更新

    if (expandedCities.value.some((item) => item.city.value === city.value)) {
        await scrollToDistrict(city)
    }
}

const addSelectedRegions = (region: Region) => {
    selectedRegions.value.push(region)
}

const removeSelectedRegion = (region: Region) => {
    const index = selectedRegions.value.findIndex((selected) => selected.value === region.value)
    selectedRegions.value.splice(index, 1)
}

const isRegionSelected = (region: Region) => {
    return selectedRegions.value.some((selected) => selected.value === region.value)
}

const countSelectedDistrict = (region: Region) => {
    const districts = selectedRegions.value.filter((selected) => selected.parent?.value === region.value)
    const length = districts.length
    if (length === 0) return ''
    return districts.length
}

const scrollTo = (letter: string) => {
    document.getElementById(letter)?.scrollIntoView({ behavior: 'smooth' })
}

const toggleAll = () => {
    selectedRegions.value = []
}

const isAllRegion = () => {
    return selectedRegions.value.length === 0
}

const scrollToDistrict = (city: Region) => {
    const districtEl = document.getElementById(`district-${city.value}`)
    if (!districtEl) return

    const scrollContainer = document.querySelector('.all-regions')
    if (!scrollContainer) return

    const elementRect = districtEl.getBoundingClientRect()
    const containerRect = scrollContainer.getBoundingClientRect()
    const diff = containerRect.bottom - elementRect.bottom

    if (diff > 0) return
    document.getElementById(`district-${city.value}`)?.scrollIntoView({ behavior: 'smooth', block: 'end' })
}

const isCityAllSelected = (city: Region) => {
    if (!city || !city.children) return false
    return city.children.every((child: Region) =>
        selectedRegions.value.some((selected: Region) => selected.value === child.value)
    )
}

const isCityExpand = (city: Region) => {
    if (!city || !city.children) return false

    return expandedCities.value.some((expanded) => expanded.city.value === city.value)
}

const reset = () => {
    selectedRegions.value = []
    expandedCities.value = []
}

const getSelectedRegions = () => {
    return selectedRegions.value
}

const setSelectedRegions = (regions: Region[]) => {
    selectedRegions.value = regions
}

const setExpandedCitiesRegions = (city: Region, province: Region) => {
    toggleCity(city, province)
}

defineExpose({ reset, getSelectedRegions, setSelectedRegions, setExpandedCitiesRegions })
</script>

<template>
    <div class="selector flex flex-column">
        <div class="flex flex-row">
            <div class="hot-city flex flex-row gap-12 top-bottom-center tb-padding-12">
                <div
                    class="font-14 tb-padding-4 lr-padding-10 pointer border-radius-4 flex top-bottom-center left-right-center"
                    :class="{ 'color-blue': isAllRegion(), 'back-color-three-blue': isAllRegion() }"
                    @click="toggleAll"
                >
                    全国
                </div>
                <div
                    v-for="region in hotCities"
                    :key="region.city.value"
                    class="font-14 tb-padding-4 lr-padding-10 pointer border-radius-4 flex top-bottom-center left-right-center"
                    :class="{
                        'color-blue': isRegionSelected(region.city),
                        'back-color-three-blue': isRegionSelected(region.city),
                    }"
                    @click="toggleAllDistrict(region.city, region.province)"
                >
                    {{ region.city.label }}
                </div>
            </div>
        </div>
        <div class="split-line"></div>
        <div class="first-letter flex felx-row gap-16 tb-padding-12">
            <div class="font-14">省份首字母</div>
            <div
                v-for="letter in letters"
                :key="letter"
                class="pointer font-14 color-black font-weight-700"
                @click="scrollTo(letter)"
            >
                {{ letter }}
            </div>
        </div>
        <div class="split-line"></div>
        <el-scrollbar view-class="flex flex-column h-400" wrap-class="all-regions">
            <div v-for="group in regionGroups" :key="group.letter" class="flex flex-row" :id="group.letter">
                <div class="font-16 w-40 t-padding-8 color-black font-weight-700">{{ group.letter }}</div>
                <div class="flex flex-column flex-1">
                    <div v-for="province in group.provinces" :key="province.value" class="font-14 flex flex-row flex-1">
                        <div class="flex flex-1">
                            <div class="w-60 t-padding-8 font-14 color-black font-weight-700">{{ province.label }}</div>
                            <div class="flex flex-1 flex-column">
                                <div class="font-14 flex flex-row flex-wrap flex-1">
                                    <div
                                        class="pointer all-padding-8"
                                        :class="{ 'color-blue': isRegionSelected(province) }"
                                        @click="toggleAllCity(province)"
                                    >
                                        全部
                                    </div>
                                    <div
                                        v-for="city in province.children"
                                        :key="city.value"
                                        class="pointer all-padding-8"
                                        :class="{
                                            'color-blue':
                                                countSelectedDistrict(city) ||
                                                isRegionSelected(city) ||
                                                isCityExpand(city),
                                        }"
                                        @click="toggleCity(city, province)"
                                    >
                                        {{ city.label }} {{ countSelectedDistrict(city) }}
                                    </div>
                                </div>
                                <div class="font-14 flex flex-row">
                                    <template v-for="item in expandedCities" :key="item.city.value">
                                        <div
                                            v-if="item.province.value === province.value"
                                            class="flex flex-row flex-wrap back-five-grey lr-padding-4 border-radius-8"
                                            :ref="`district-${item.city.value}`"
                                            :id="`district-${item.city.value}`"
                                        >
                                            <div
                                                class="pointer all-padding-8"
                                                :class="{ 'color-blue': isRegionSelected(item.city) }"
                                                @click="toggleAllDistrict(item.city, province)"
                                            >
                                                全部
                                            </div>
                                            <div
                                                v-for="district in item.city.children"
                                                :key="district.value"
                                                class="pointer all-padding-8"
                                                :class="{ 'color-blue': isRegionSelected(district) }"
                                                @click="toggleDistrict(district, item.city, province)"
                                            >
                                                {{ district.label }}
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-scrollbar>
        <div class="split-line"></div>
        <div class="selected-regions tb-padding-8">
            <div class="font-14 b-padding-8">已选择 ({{ selectedRegions.length }})</div>
            <el-scrollbar view-class="flex flex-row gap-8 flex-wrap top-bottom-start max-height-86">
                <div
                    v-for="region in selectedRegions"
                    :key="region.value"
                    class="font-14 tb-padding-4 lr-padding-6 border-color-blue border-radius-4 flex flex-row pointer tag"
                    @click="removeSelectedRegion(region)"
                >
                    所在地区：
                    <span class="color-blue"> {{ formatRegionLabel(region) }}</span>
                    <span class="flex top-bottom-center left-right-center l-padding-2">
                        <el-icon class="close-btn"><CloseBold /></el-icon>
                    </span>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.tag:hover .close-btn {
    color: var(--dark-red-);
}
</style>
