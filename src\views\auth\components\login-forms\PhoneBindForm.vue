<template>
    <div class="password-form">
        <div class="title">账号登录</div>
        <div class="form">
            <div class="steps">
                <div class="step">
                    <div class="icon step-bg">
                        <el-icon><Select /></el-icon>
                    </div>
                    <div class="phone-bond-step-label">账户验证</div>
                </div>
                <div class="step-split"></div>
                <div class="step">
                    <div class="icon current-step-bg">2</div>
                    <div class="phone-bond-step-label">手机号绑定</div>
                </div>
            </div>
            <el-form :model="form" ref="formRef">
                <el-form-item label="" class="password-form-item">
                    <el-input v-model="form.phone" placeholder="输入手机号码" class="input" />
                </el-form-item>
                <el-row style="width: 100%">
                    <el-col :span="15">
                        <el-form-item label="" class="password-form-item">
                            <el-input v-model="form.code" placeholder="短信验证码" class="input" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item label="" style="margin-bottom: 0px; margin-left: 12px">
                            <el-button
                                type="primary"
                                @click="getCode"
                                :disabled="countdown > 0"
                                :loading="querying"
                                class="code-btn"
                            >
                                {{ countdown > 0 ? `${countdown} 秒` : '获取验证码' }}</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="help"></div>
            </el-form>
        </div>
        <div class="btns">
            <div>
                <el-button round class="submit-btn" @click="submitForm()" :loading="loading">
                    <div>确认绑定</div>
                </el-button>
            </div>
            <div>
                <el-button class="login-btn" @click="toLogin()"> 账户登录 </el-button>
            </div>
        </div>
        <div class="tips">
            <div class="label">温馨提示：</div>
            <div class="desc">若无账号请联系后台管理员进行开通</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import systemService from '@/service/systemService'
import { phoneValidate } from '@/utils/validate'
import { ElMessage, type FormInstance } from 'element-plus'
import { onUnmounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const formRef = ref<FormInstance>()
const form = reactive({
    phone: '',
    code: '',
})
const loading = ref(false) // 控制按钮的 loading 状态
const querying = ref(false) // 控制按钮的 loading 状态
const countdown = ref(0) // 记录倒计时秒数
const timer = ref<ReturnType<typeof setInterval> | null>(null)

const validate = () => {
    let flag = true
    let message = ''
    const { phone, code } = form || {}

    if (!code) {
        message = '短信验证码不能为空'
    }

    if (!phone) {
        message = '手机号码不能为空'
    }

    if (!phoneValidate(phone.trim())) {
        message = '手机号码格式不正确'
    }

    if (message) {
        ElMessage.error(message)
        return
    }

    return flag
}

const submitForm = () => {
    if (!validate()) return

    const { phone, code } = form || {}

    if (loading.value) return

    loading.value = true
    systemService
        .userAuthMobile({
            mobile: phone.trim(),
            code: code.trim(),
        })
        .then(() => {
            loading.value = false
            router.push('/')
        })
        .catch((err) => {
            loading.value = false
            console.log(err)
        })
}

const getCode = async () => {
    const { phone } = form || {}

    if (!phone) {
        ElMessage.error('手机号码不能为空')
        return
    }

    if (!phoneValidate(phone.trim())) {
        ElMessage.error('手机号码格式不正确')
        return
    }

    if (countdown.value > 0 || querying.value) return // 防止重复点击
    querying.value = true
    console.log('请求验证码...')
    systemService
        .userSendAuthMobileCode({
            mobile: phone,
        })
        .then(() => {
            console.log('验证码发送成功')
            // 开始倒计时
            countdown.value = 60
            querying.value = false // 取消 loading
            timer.value = setInterval(() => {
                countdown.value--
                if (countdown.value <= 0 && timer.value) {
                    clearInterval(timer.value)
                    timer.value = null
                }
            }, 1000)
        })
        .catch((err) => {
            console.log(err)
            querying.value = false
        })
}

const toLogin = () => {
    router.push('login')
}

// 组件卸载时清除定时器
onUnmounted(() => {
    if (timer.value) clearInterval(timer.value)
})
</script>

<style lang="scss" scoped>
@use './style.scss';
</style>
