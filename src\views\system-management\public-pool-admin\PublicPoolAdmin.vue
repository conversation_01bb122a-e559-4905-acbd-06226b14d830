<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7;">
        <div style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div ref="actionBarContentRef" class="display-flex justify-flex-end font-16">
                <!-- 新增按钮 -->
                <el-button v-if="permissionService.isPublicpoolAddPermitted()" type="primary" @click="handleAdd">新增</el-button>
            </div>
            <div class="t-margin-10">
                <!-- 表格 -->
                <el-table
                    :data="poolData"
                    style="width: 100%"
                    :height="tableHeight+'px'"
                    v-loading="tableLoading"
                    show-overflow-tooltip
                    row-key="id"
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <template v-if="!tableLoading" #empty>
                        <div class="display-flex flex-column top-bottom-center" style="height: 70vh">
                            <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                            <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                        </div>
                    </template>
                    <el-table-column
                        prop="name"
                        label="客户公海名称" 
                        min-width="300px"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="users"
                        label="客户公海成员"
                        width="300"
                    >
                        <template #default="scope">
                            {{ scope.row.users.map((user: Users) => user.nickname).join(', ') }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sort"
                        label="优先级"
                        min-width="300px"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="openReback"
                        label="是否自动回收"
                        min-width="250px"
                    >
                        <template #default="scope">
                            <div> {{ scope.row.openReback === 1 ? '是' : '否' }} </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        props="openGetrebackRule"
                        label="抢回规则"
                        min-width="250px"
                    >
                        <template #default="scope">
                            <div> {{ scope.row.openGetrebackRule === 1 ? '是' : '否' }} </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        fixed="right"
                        label="操作"
                        width="150px"
                    >
                        <template v-slot:default="scope" >
                            <div class="display-flex gap-10">
                                <div
                                    style="color: #1966FF"
                                    :class="['font-16', permissionService.isPublicpoolEditPermitted()? 'pointer':'not-allow']"
                                    type="primary"
                                    text
                                    @click="permissionService.isPublicpoolEditPermitted() && setBaseSetting(scope.row)"
                                >
                                    基础设置
                                </div>
                                <div>
                                    <el-dropdown>
                                        <div 
                                            style="color: #1966FF"
                                            class="font-16 pointer t-padding-3"
                                            type="primary" 
                                            text
                                        >
                                            更多
                                        </div>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item>
                                                    <el-button
                                                        type="primary"
                                                        style="color: var(--main-blue-)"
                                                        text
                                                        @click="setRule(scope.row)"
                                                    >
                                                        规则设置
                                                    </el-button>
                                                </el-dropdown-item>
                                                <el-dropdown-item  >
                                                    <el-button
                                                        type="danger"
                                                        @click="confirmRemove(scope.row)"
                                                        :disabled="!permissionService.isPublicpoolDeletePermitted()"
                                                        text
                                                    >
                                                        删除
                                                    </el-button>
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                            
                        </template>
                    </el-table-column> 
                </el-table>
                <!-- 分页器 -->
                <el-affix position="bottom" :offset="0">
                    <div class="pagination-bar">
                        <el-pagination
                            v-model:currentPage="pageInfo.page"
                            v-model:page-size="pageInfo.pageSize"
                            :total="pageInfo.total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @change="pageChange"
                        />
                    </div>
                </el-affix>
            </div>
            <!-- 新增/修改弹窗 -->
            <el-dialog
                :title="dialogTitle"
                v-model="addOrEditDialogVisible"
                width="500px"
                @close="addOrEditDialogVisible = false"
            >   
                <AddPool v-if="addDialogVisible" @cancel="handleCancel" @confirm="addConfirm"></AddPool>
                <EditPool v-if="editDialogVisible" :addData="currentRow" @cancel="handleCancel" @confirm="updateConfirm"></EditPool>
            </el-dialog>
            <SetRule v-if="currentRow" v-model:visible="setRuleDialogVisible" :poolname="poolname" :setRuleData="currentRow" @refreshData="refreshData"></SetRule>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { ISearchPoolListParams, SearchPoolListResponseItem,IAddPoolParams,Users } from '@/types/lead'
import { ref, reactive, onMounted, onBeforeMount } from 'vue'
import { ElMessageBox,ElMessage } from 'element-plus'
import EditPool from './components/EditPool.vue'
import AddPool from './components/AddPool.vue'
import SetRule from './components/SetRule.vue'
import crmService from '@/service/crmService'
import permissionService from '@/service/permissionService'

const mainContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
const tableHeight = ref(500)
const getTableHeight = () => {
    if (mainContentRef.value && actionBarContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - actionBarContentRef.value.clientHeight - 32 - 16 - 16 - 16 - 16 - 16
    }
}
const poolname = ref('customerpool')
const tableLoading = ref(false)
const poolData = ref<SearchPoolListResponseItem[]>([])
const addOrEditDialogVisible = ref(false)
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const setRuleDialogVisible = ref(false)
const currentRow = ref<SearchPoolListResponseItem>()
const dialogTitle = ref('')
const totalNum = ref(0)
const addPoolParams = ref<IAddPoolParams>({
    name: '',
    type: '2',
    userIds: []
})

const updataPoolParams = ref<IAddPoolParams>({
    name: '',
    type: '2',
    userIds: []
})

const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

const getCrmManageListParams = ref<ISearchPoolListParams>({
    type:'2', 
    page:pageInfo.page,
    pageSize:pageInfo.pageSize 
})


const pageChange = (currentPage: number, currentPagesize: number) => {
    getCrmManageListParams.value.page = currentPage
    getCrmManageListParams.value.pageSize = currentPagesize
    console.log('getCrmManageListParams:', getCrmManageListParams)
    getCrmManageList(getCrmManageListParams.value)
}

const getCrmManageList = (param:ISearchPoolListParams) => {
    tableLoading.value = true
    param.page = pageInfo.page
    param.pageSize = pageInfo.pageSize
    crmService.crmManageList(param).then(res => {
        console.log('res:', res)
        poolData.value = res.data
        totalNum.value = res.total
    }).catch(err => {
        console.log('err:', err)
    }).finally(() => {
        tableLoading.value = false
    })
}

const refreshData = () => {
    getCrmManageList(getCrmManageListParams.value)
}

const handleAdd = () => {
    dialogTitle.value = '新增'
    console.log('currentRow:', currentRow.value)
    editDialogVisible.value = false
    addOrEditDialogVisible.value = true
    addDialogVisible.value = true
}

const setBaseSetting = (row: SearchPoolListResponseItem) => {
    dialogTitle.value = '基础设置'
    addDialogVisible.value = false
    addOrEditDialogVisible.value = true
    editDialogVisible.value = true
    currentRow.value = row
    console.log('currentRow:', currentRow.value)
}

const setRule = (row: SearchPoolListResponseItem) => {
    setRuleDialogVisible.value = true
    currentRow.value = row
}

const handleCancel = () => {
    addOrEditDialogVisible.value = false
}

const addConfirm = (formData:SearchPoolListResponseItem, resolve:() => void ) => {
    console.log('新增客户公海名称:', formData.name)
    console.log('新增客户公海成员:', formData.users)
    if(formData.name === '' || !formData.name){
        ElMessage.error('客户公海名称不能为空')
    }else if(formData.users.length === 0 || !formData.users){
        ElMessage.error('客户公海成员不能为空')
    }else{
        // 处理确认逻辑
        addPoolParams.value.name = formData.name
        addPoolParams.value.userIds = formData.users
        crmService.crmManageAdd(addPoolParams.value).then(res => {
            console.log('res:', res)
            if(res.success === true){
                ElMessage.success('新增成功')
                resolve()
            }else{
                ElMessage.error(`${res.errMsg}`)
            }
        }).catch(err => {
            console.log('err:', err)
        }).finally(() => {
            addOrEditDialogVisible.value = false
            getCrmManageList(getCrmManageListParams.value)
        })
    }
}

const updateConfirm = (formData:SearchPoolListResponseItem) => {
    console.log('编辑客户公海名称:', formData.name)
    console.log('编辑客户公海成员:', formData.users)
    console.log('编辑客户公海id:', formData.poolId)
    if(formData.name === '' || !formData.name){
        ElMessage.error('客户公海名称不能为空')
    }else if(formData.users.length === 0 || !formData.users){
        ElMessage.error('客户公海成员不能为空')
    }else{
        // 处理确认逻辑
        updataPoolParams.value.poolId = formData.poolId
        updataPoolParams.value.name = formData.name
        updataPoolParams.value.userIds = formData.users
        crmService.crmManageUpdate(updataPoolParams.value).then(res => {
            console.log('res:', res)
            if(res.success === true){
                ElMessage.success('修改成功')
            }else{
                ElMessage.error(`${res.errMsg}`)
            }
        }).catch(err => {
            console.log('err:', err)
        }).finally(() => {
            addOrEditDialogVisible.value = false
            getCrmManageList(getCrmManageListParams.value)
        })
        
    }
}

const confirmRemove = ( row:SearchPoolListResponseItem ) => {
    ElMessageBox.confirm('是否要删除该客户公海，此操作不可恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        deleteRow(row)
    })
}

const deleteRow = (row: SearchPoolListResponseItem) => {
    console.log('deleteRow:', row)
    if(row.isYxtz){
        ElMessage.error('默认客户公海不可删除')
    }else{
        crmService.crmManageDelete(row.id).then(res => {
            console.log('res111:', res)
            if(res.success === true){
                ElMessage.success('删除成功')
            }else{
                ElMessage.error(`${res.errMsg}`)
            }
        }).catch(err => {
            console.log('err:', err)
        }).finally(() => {
            getCrmManageList(getCrmManageListParams.value)
        })
    }
}

onBeforeMount(() => {
})

onMounted(async () => {
    getCrmManageList(getCrmManageListParams.value)
    getTableHeight()
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.no-hover-border {
    border-color: transparent;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}

</style>
