

import type { IAllRecord } from './record'
import type { IPaginationResponse } from './axios'
export interface getCompareIndicatorDataParams extends IAllRecord {
    ids: string
    socialCreditCode?: string
}

export interface getIndicatorResultParams extends IAllRecord {
    tableName: string
    socialCreditCode: string
    type: string
    pageNum: number
    pageSize: number
}

export interface getIndicatorResultRecordItem {
    value: string
    type: string
    socialCreditCode: string
    indicator: string
}

export interface getIndicatorResultResponse {
    records: getIndicatorResultRecordItem[]
}

export interface IIndicatorItem {
    currentTime: string[] | [string, string, number][]
    dataId: string[]
    headers: string[]
    id: number | number
}

export interface getCompareIndicatorDataResponse {
    [key: number]: IIndicatorItem[]
}

export interface IGetRiskTypeData {
    [key: string]: number | string
}

export interface getRiskEntListparams {
    socialCreditCode: string
}

export interface IRiskEntListItem {
    content: string
    sortTime: string
    typeName: string
}

export interface IGetRiskEntListResponse extends IPaginationResponse {
    data:IRiskEntListItem[]
}