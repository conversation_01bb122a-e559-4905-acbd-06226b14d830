

import type { IAllRecord } from './record'
import type { IPaginationResponse } from './axios'
export interface getCompareIndicatorDataParams extends IAllRecord {
    ids: string
    socialCreditCode?: string
}

export interface getIndicatorResultParams extends IAllRecord {
    tableName: string
    socialCreditCode: string
    type: string
    pageNum: number
    pageSize: number
}

export interface getIndicatorResultRecordItem {
    value: string
    type: string
    socialCreditCode: string
    indicator: string
}

export interface getIndicatorResultResponse {
    records: getIndicatorResultRecordItem[]
}

export interface IIndicatorItem {
    currentTime: string[] | [string, string, number][]
    dataId: string[]
    headers: string[]
    id: number | number
}

export interface getCompareIndicatorDataResponse {
    [key: number]: IIndicatorItem[]
}

export interface IGetRiskTypeData {
    [key: string]: number | string
}

export interface getRiskEntListparams {
    socialCreditCode: string
}

export interface IRiskEntListItem {
    content: string
    sortTime: string
    typeName: string
}

export interface IGetRiskEntListResponse extends IPaginationResponse {
    data:IRiskEntListItem[]
}

export interface getTopTenParams {
    socialCreditCode: string
    scope: string // 1上游2下游
    dateInterval?: [] // 时间区间，不传默认取过去12个月
}

export interface IGetTopTenResponseItem {
    companyName: string,
    socialCreditCode: string,
    ratio: string,
    isLead?: boolean // 上下游企业是否转移过
}

export interface getProportionListParams {
    socialCreditCod: string,
    companyName: string,
    scope: string,
    dateInterval:[] // 时间区间，不传默认取过去12个月
    proportionInterva: [] // 占比区间 只接受小数
    pag: number,
    pageSiz: number
}
export interface IGetProportionListResponse extends IPaginationResponse{
    data: IGetTopTenResponseItem[]
}