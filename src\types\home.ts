import type { IAllRecord } from '@/types/record'

// 定义 topTenParams 项接口
interface TopTenParam {
    id: string
    name: string
}

// 定义 params 项接口
interface RequestParam {
    code: string[]
    industryCode: string[]
    modelCode: string
    roundName: string[]
}
export interface MapType {
    2023: string
    2024: string
    2025: string
}
export interface Model001ResInfo {
    id: string
    industryPhyCode: string
    industryPhyName: string
    invseRoundName: string
    indexName: string
    indexValue: string
    indexModelName: string
    indexModelId: string
    province: string
    district: string
    secdistrict: string
    createTime: string
    updateTime: string
    outhorDate: string
    map: string | MapType
    num: number
    title?: string
    key?: string
}

export interface ModelRes {
    [key: string]: Model001ResInfo
}

// 定义主请求接口
export interface GetHomeDataParams {
    code: string
    industryCode?: string
    params?: RequestParam[]
    parentIds?: string[]
    roundName?: string
    topTenParams?: TopTenParam[]
    topTenType?: number
}
export interface GetHomeDataResponse {
    Model001?: ModelRes[]
    Model003?: ModelRes[]
    Model004?: ModelRes[]
    Model005?: ModelRes[]
    Model006?: ModelRes[]
    Model007?: ModelRes[]
    Model002?: ModelRes[]
}

export interface GetCustomerDataResponse {
    label: string
    name: string
    num: string
    pinyin: string
    val: string
    children?: GetCustomerDataResponse[]
}

export interface ISearchCustomerParams extends IAllRecord{
    key: string
}
