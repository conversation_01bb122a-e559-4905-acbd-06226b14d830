<template>
    <el-table
        ref="tableRef"
        :data="tableData"
        @selection-change="selectionChange"
        show-overflow-tooltip
        :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
    >
        <el-table-column type="selection" width="64"></el-table-column>
        <el-table-column label="企业信息">
            <template #default="scope">
                <div class="t-margin-6 display-flex font-18 pointer">{{ scope.row.companyName }}</div>
                <div class="display-flex flex-wrap">
                    <div class="l-margin-4 t-margin-4" v-for="(tagItem,i) in scope.row.companyTags" :key="i">
                        <el-tag class="font-14" effect="plain">{{ tagItem.label }}</el-tag>
                    </div>
                </div>
                <div class="t-margin-6 display-flex font-16">
                    <div>{{ scope.row.legalperson }}</div>
                    <div class="r-margin-6 l-margin-6">|</div>
                    <div>{{ scope.row.esdate }}</div>
                    <div class="r-margin-6 l-margin-6">|</div>
                    <div>{{ scope.row.regCapDisplay }}</div>
                </div>
                <div class="t-margin-6 display-flex color-three-grey font-16">地区：{{ scope.row.companyArea }}</div>
                <div class="t-margin-6 display-flex color-three-grey font-16">招标投数：{{ scope.row.bidNum }}</div>
            </template>
        </el-table-column>
        <el-table-column label="最新公告" style="width: 50%">
            <template #default="scope">
                <div class="t-margin-10 display-flex font-16">
                    <div class="color-three-grey">公告类型:</div>
                    <div>{{ scope.row.announcementType }}</div>
                </div>
                <div class="t-margin-6 display-flex font-16 color-three-grey">
                    <div>{{ scope.row.announcementTime }}</div>
                    <div class="r-margin-6 l-margin-6">|</div>
                    <div>{{ scope.row.announcementCity }}</div>
                </div>
                <div class="t-margin-10 display-flex font-16">
                    <div class="color-three-grey">标的物:</div>
                    <div>{{ scope.row.announcementSubject }}</div>
                </div>
                <div class="t-margin-10 display-flex font-16">
                    <div class="color-three-grey">项目名称:</div>
                    <div>{{ scope.row.announcementTitle }}</div>
                </div>
            </template>
        </el-table-column>
    </el-table>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface CompanyDataItem {
    title?: string;
    tenderType?:tenderTypeItem[];
    url?: string;
    name?: string;
    companyArea?: string;
    budgetPrice?: string;
    tenderPublicDate?: string;
    outbidUnitList?: { name: string }[];
    companyName?: string;
    companyTags?: companyTagsItem[];
    legalperson?: string;
    esdate?: string;
    regCapDisplay?: string;
    bidNum?: string;
    announcementType?: string;
    announcementTime?: string;
    announcementCity?: string;
    announcementSubject?: string;
    announcementTitle?: string;
}

interface tenderTypeItem {
    label?: string
}

interface companyTagsItem {
    label?: string
}

const props = defineProps<{
    tableData: CompanyDataItem[]
}>()

const emit = defineEmits(['selection-change'])

const tableRef = ref(null)

const selectionChange = (val:CompanyDataItem[]) => {
    emit('selection-change', val)
    console.log('选中的行',val)
}

console.log(props.tableData)
</script>

<style scoped>

</style>