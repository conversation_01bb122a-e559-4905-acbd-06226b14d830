import http from '@/axios'

export default {
    getBasicInfo(body: { socialCreditCode: string }): Promise<{ reportDate: string | null }> {
        return http.get(`/api/company/getBasicInfo`, {
            params: body
        })
    },
    refresh(data: { socialCreditCode: string, companyName: string }): Promise<{ reportDate: string | null }> {
        return http.post(`/api/company/refresh`, data)
    },

}