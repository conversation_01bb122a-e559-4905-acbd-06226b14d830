import type { IPaginationResponse } from './axios'
import type { IAllRecord } from './record'

export interface IBusinessEntryListParams extends IAllRecord {
    companyName?: string //公司名称
    mobile?: string //手机号
    name?: string //姓名
    socialCreditCode?:string // 税号
    sqed?:string //申请额度
    zjyt?:string //资金用途
    page: number
    pageSize: number
}

export interface IBusinessEntryListResponse extends IPaginationResponse {
    data:IBusinessEntryListResponseItem[]
}

export interface IBusinessEntryListResponseItem {
    id:string 
    companyName: string //公司名称
    createDate:number //创建时间
    mobile: string //手机号
    name: string //姓名
    socialCreditCode:string // 税号
    sqed:string //申请额度
    zjyt:string //资金用途
    userName:string //用户名
}