<script lang="ts" setup>
import { ref, watch } from 'vue'
import * as echarts from 'echarts'
import type { LineChartOption } from '@/types/echart'

type AllData = {
    [key: string]: string[]
}
const props = defineProps<{
    data: AllData
}>()

const chartRef = ref(null)
const lineOption = ref<LineChartOption>({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
                backgroundColor: '#6a7985',
            },
        },
        formatter: function (params) {
            const colors = {
                高风险: '#d77b75',
                中风险: '#d4a75e',
                低风险: '#82af51',
            }

            let html = `<div>${params[0].name}</div>`
            params.forEach((item) => {
                const color = colors[item.seriesName]
                html += `
                <div style="display:flex;align-items:center;margin-top:5px;">
                    <span style="display:inline-block;width:12px;height:12px;background:${color};margin-right:5px;border-radius:2px;"></span>
                    <span>${item.seriesName}: ${item.value}</span>
                </div>
            `
            })
            return html
        },
    },
    legend: {
        data: [
            {
                name: '高风险',
                icon: 'rect',
                itemStyle: {
                    color: '#d77b75', // 高风险图例颜色
                },
            },
            {
                name: '中风险',
                icon: 'rect',
                itemStyle: {
                    color: '#d4a75e', // 中风险图例颜色
                },
            },
            {
                name: '低风险',
                icon: 'rect',
                itemStyle: {
                    color: '#82af51', // 低风险图例颜色
                },
            },
        ],
        bottom: 0,
        left: 'center',
    },
    xAxis: [
        {
            type: 'category',
            boundaryGap: false,
            data: [],
        },
    ],
    yAxis: [
        {
            type: 'value',
        },
    ],
    series: [
        {
            name: '高风险',
            showSymbol: true, // 改为true，默认显示数据点
            symbolSize: 6,
            itemStyle: {
                color: '#d77b75',
                borderColor: '#d77b75',
                borderWidth: 2,
            },
            type: 'line',
            stack: 'Total',
            emphasis: {
                showSymbol: true,
                symbol: 'emptyCircle',
                symbolSize: 10,
                itemStyle: {
                    color: '#d77b75',
                    borderColor: '#d77b75',
                    borderWidth: 2,
                },
            },
            data: [],
            lineStyle: {
                color: '#d77b75', // 线条颜色
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(215, 123, 117, 0.6)',
                        },
                        {
                            offset: 1,
                            color: 'rgba(215, 123, 117, 0.1)',
                        },
                    ],
                },
            },
        },
        {
            name: '中风险',
            showSymbol: true, // 改为true，默认显示数据点
            symbolSize: 6,
            itemStyle: {
                color: '#d4a75e',
                borderColor: '#d4a75e',
                borderWidth: 2,
            },
            type: 'line',
            stack: 'Total',
            emphasis: {
                showSymbol: true,
                symbol: 'emptyCircle',
                symbolSize: 10,
                itemStyle: {
                    color: '#d4a75e',
                    borderColor: '#d4a75e',
                    borderWidth: 2,
                },
            },
            data: [],
            lineStyle: {
                color: '#d4a75e', // 线条颜色
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(212, 167, 94, 0.6)',
                        },
                        {
                            offset: 1,
                            color: 'rgba(212, 167, 94, 0.1)',
                        },
                    ],
                },
            },
        },
        {
            name: '低风险',
            showSymbol: true, // 改为true，默认显示数据点
            symbolSize: 6,
            itemStyle: {
                color: '#82af51',
                borderColor: '#82af51',
                borderWidth: 2,
            },
            type: 'line',
            stack: 'Total',
            emphasis: {
                showSymbol: true,
                symbol: 'emptyCircle',
                symbolSize: 10,
                itemStyle: {
                    color: '#82af51',
                    borderColor: '#82af51',
                    borderWidth: 2,
                },
            },
            data: [],
            lineStyle: {
                color: '#82af51', // 线条颜色
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(130, 175, 81, 0.6)',
                        },
                        {
                            offset: 1,
                            color: 'rgba(130, 175, 81, 0.1)',
                        },
                    ],
                },
            },
        },
    ],
})
const setChart = () => {
    const myChart = echarts.init(chartRef.value)
    myChart.setOption(lineOption.value)
}
const showChart = ref(true)
const dealData = (data: AllData) => {
    console.log(
        '风险事件等级分布',
        data['1319598628537344'],
        data['1320371974308864'],
        data['1320372364379136'],
        data['1320372611843072']
    )
    if (
        (data['1319598628537344'] && (data['1320371974308864'] || data['1320372364379136'])) ||
        data['1320372611843072']
    ) {
        showChart.value = true
    } else {
        showChart.value = false
    }
    lineOption.value.xAxis[0].data = Array.isArray(data['1319598628537344'])
        ? data['1319598628537344']
        : [data['1319598628537344']]
    lineOption.value.series[0].data = Array.isArray(data['1320371974308864'])
        ? data['1320371974308864']
        : [data['1320371974308864']]
    lineOption.value.series[1].data = Array.isArray(data['1320372364379136'])
        ? data['1320372364379136']
        : [data['1320372364379136']]
    lineOption.value.series[2].data = Array.isArray(data['1320372611843072'])
        ? data['1320372611843072']
        : [data['1320372611843072']]
    setChart()
}
watch(
    () => props.data,
    (newVal) => {
        dealData(newVal)
    }
)
</script>
<template>
    <div class="width-100 height-100 back-color-white border-radius-8 all-padding-16 display-flex flex-column">
        <div class="font-16 color-black font-weight-500">风险事件等级分布</div>
        <div class="flex-1">
            <div v-if="showChart" class="width-100 height-100" ref="chartRef"></div>
            <div v-else class="width-100 height-100 display-flex center">
                <div class="w-260 h-260 no-data"></div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.no-data {
    background: url('@/assets/images/no-chart-data.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: center;
}
</style>
