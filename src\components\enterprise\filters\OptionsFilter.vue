<script lang="ts" setup>
import { ref } from 'vue'
import type { IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import { Dropdown } from '@/components/enterprise/ui'
import SelectFilter from './SelectFilter.vue'
import MultiSelectFilter from './MultiSelectFilter.vue'
const current = ref<IAicNormalSearchRules | null>(null)

const props = defineProps<{
    options: IAicNormalSearchRules[]
    storeParams: INormalFilterParams[]
}>()

const onChange = (key: string) => {
    const target = props.options.find((option) => option.key === key)
    if (target && target.key === current.value?.key) {
        current.value = null
        return
    }
    if (target) {
        current.value = target
    } else {
        current.value = null
    }
}
</script>

<template>
    <div class="flex left-right-center flex-column gap-14">
        <div class="flex flex-row">
            <div class="w-112">
                <div class="lh-24 font-16 color-black">筛选条件：</div>
            </div>
            <div class="flex flex-column gap-14 left-right-center flex-1">
                <div class="flex flex-row top-bottom-center gap-24 flex-wrap">
                    <Dropdown
                        :label="option.name"
                        :value="option.key"
                        :checked="false"
                        :onChange="onChange"
                        :current="current"
                        v-for="option in options"
                        :key="option.key"
                    />
                </div>
                <template v-for="option in options" :key="option.key">
                    <div class="flex flex-row" v-show="current?.key === option.key">
                        <div class="flex flex-row top-bottom-center gap-24 flex-wrap flex-1">
                            <SelectFilter
                                v-if="option.dataType === 'select'"
                                :data="option"
                                :storeParams="storeParams"
                            />
                            <MultiSelectFilter
                                v-if="option.dataType === 'multiSelect'"
                                :data="option"
                                :storeParams="storeParams"
                            />
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
