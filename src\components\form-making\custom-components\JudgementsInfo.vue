<script lang="ts" setup>
import { onMounted, ref, inject, defineEmits } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IJudgementsInfoResponseItem } from '@/types/model'
import JudgementsInfoDetailDialog from '@/components/search/custom-modal/components/JudgementsInfoDetailDialog.vue'

const props = defineProps<{
    modelItem: IGetModelCategoryResponse
}>()
const tableLoading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})
const emits = defineEmits(['updateTotal'])
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const tableData = ref<IJudgementsInfoResponseItem[]>([])
const columns = [
    { label: '案件名称', prop: 'title' },
    { label: '案由类型', prop: 'caseReason' },
    { label: '案件类型', prop: 'caseType' },
    { label: '案号', prop: 'caseNum' },
    { label: '审判程序', prop: 'trialProcedure' },
    { label: '发布日期', prop: 'publishDate' },
    { label: '执行法院', prop: 'court' }
]
const getModelData = () => {
    tableLoading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
        })
        .then((res) => {
            const { total, items } = res
            tableData.value = items as IJudgementsInfoResponseItem[]
            pageInfo.value.total = total
            emits('updateTotal', total)
            if (pageInfo.value.page !== 1) {
                jumpHref()
            }
        })
        .finally(() => {
            tableLoading.value = false
        })
}

const jumpHref = () => {
    document.getElementById(`model-${props.modelItem.name}`)?.scrollIntoView({
        behavior: 'smooth',
        inline: 'nearest',
    })
}

//计算表格的index
const indexFilter = (index: number) => {
    return (pageInfo.value.page - 1) * pageInfo.value.pageSize + index + 1
}

onMounted(() => {
    getModelData()
})
</script>
<template>
    <el-table
        v-loading="tableLoading"
        :data="tableData"
        tooltip-effect="dark"
        border
        table-layout="fixed"
        fit
        :header-cell-style="{
            background: '#ECF5FF',
        }"
        size="large"
        empty-text="暂无数据"
    >
        <el-table-column type="index" label="序号" width="80" align="center" :index="indexFilter" header-align="center"></el-table-column>
        <el-table-column
            align="center"
            header-align="center"
            v-for="(item, index) in columns"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
        >
            <template #default="{ row }">
                <div v-if="item.prop === 'title'" class="!color-blue pointer">
                    <JudgementsInfoDetailDialog :row="row" />
                </div>
                <div v-else>
                    {{ row[item.prop]}}
                </div>
            </template>
        </el-table-column>
    </el-table>
    <el-pagination
        v-if="pageInfo.total > 0"
        class="flex justify-end"
        v-model:current-page="pageInfo.page"
        v-model:page-size="pageInfo.pageSize"
        :page-sizes="[10]"
        layout="total, prev, pager, next"
        :total="pageInfo.total"
        @change="getModelData"
    />
</template>
<style scoped lang="scss"></style>
