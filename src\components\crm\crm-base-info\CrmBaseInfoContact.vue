<template>
    <el-table ref="table" :data="contactsData" tooltip-effect="dark" border table-layout="fixed" :header-cell-style="{
        background: '#ECF5FF',
    }" size="large" empty-text="暂无联系方式">
        <el-table-column prop="contact" label="联系人" align="left">
            <template #default="scope">
                {{ scope.row.contact || '-' }}
            </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" align="left">
            <template #default="scope">
                {{ getType(scope.row.type) }}
            </template>
        </el-table-column>
        <el-table-column prop="content" label="联系方式" align="left" />
        <el-table-column prop="firstSourceName" label="来源" align="left" />
        <el-table-column prop="contact" label="标签" align="left">
            <template #default="scope">
                {{ scope.row.tagType === 1 ? '关键' : scope.row.tagType === 2 ? '推荐' : '-' }}
            </template>
        </el-table-column>
        <el-table-column prop="numArea" label="地区" align="left" />
    </el-table>
    <div class="display-flex top-bottom-center margin-top-20 justify-flex-end">
        <el-pagination :hide-on-single-page="true" v-model:currentPage="pageInfo.page"
                       v-model:page-size="pageInfo.pageSize" layout="total, prev, pager, next" :total="pageInfo.total" />
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, inject } from 'vue'
import type { Ref } from 'vue'
import type { ILeadData } from '@/types/lead'
const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})

const contactsData = computed(() => {
    if (!crmDetail.value.allContacts?.length) {
        return []
    }
    console.log(pageInfo.value.pageSize)
    return crmDetail.value.allContacts.slice((pageInfo.value.page - 1) * pageInfo.value.pageSize, pageInfo.value.page * pageInfo.value.pageSize)
})

const contactTypes = ref([
    {
        label: '全部',
        val: 0,
    },
    {
        label: '手机',
        icon: 'icon-a-huaban277',
        val: 1,
    },
    {
        label: '固话',
        icon: 'icon-a-huaban275',
        val: 2,
    },
    {
        label: 'qq',
        icon: 'icon-a-huaban276',
        val: 3,
    },
    {
        label: '邮箱',
        icon: 'icon-a-huaban278',
        val: 4,
    },
])

const getType = (type: number) => {
    return contactTypes.value.find((item) => item.val === type)?.label
}

onMounted(() => {
    pageInfo.value.total = crmDetail.value.allContacts?.length || 0
})
</script>

<style lang='scss' scoped></style>