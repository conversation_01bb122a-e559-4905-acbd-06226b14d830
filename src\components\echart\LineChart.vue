<script lang='ts' setup>
import { onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import type { LineChartOption } from '@/types/echart'
const props = defineProps<{
    loading?: boolean
    lineOption?: LineChartOption // 使用自定义类型
}>()

// 监听图表数据变化
watch(
    () => props.lineOption,
    () => {
        initChart()
    },
    { deep: true }
)

const lineChart = ref(null)
const initChart = () => {
    const myChart = echarts.init(lineChart.value)
    myChart.setOption({
        // 提示框配置（鼠标悬停时显示数据详情）
        tooltip: props.lineOption?.tooltip,

        // 标题配置（显示在底部中间）
        title: props.lineOption?.title,

        // 图例配置（垂直显示在右侧中间）
        legend: props.lineOption?.legend,
        xAxis: props.lineOption?.xAxis,
        yAxis: props.lineOption?.yAxis,
        // 数据系列配置（核心部分）
        series: props.lineOption?.series,
        
    })
}
onMounted(() => {
    initChart()
})
</script>
<template>
    <div ref="lineChart" class="height-100 width-100" v-loading="props.loading"></div>
</template>
<style scoped lang='scss'>
</style>
