<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import <PERSON><PERSON><PERSON> from './PieChart.vue'
import SearchBox from '@/components/common/SearchBox.vue'
import { RELATE_COMPANY_TABLE_COLUMNS } from '@/js/table-options'
import indicatorService from '@/service/indicatorService'
import crmService from '@/service/crmService'
import type { ILeadColumn, IGetCrmLeadParams, ICrmAddFromListRequest, ILeadData } from '@/types/lead'
import type { IGetTopTenResponseItem } from '@/types/indicator'
import { getLastYearRange} from '@/utils/parse-time'
const props = defineProps<{
    from: 'up' | 'down',
    companyInfo?: ILeadData
}>()
const tableAllOptions = ref<ILeadColumn[]>(RELATE_COMPANY_TABLE_COLUMNS)
const tableData = ref<IGetTopTenResponseItem[]>([])
const tableLoading = ref(false)
let pageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})
const defaultQueryParams = ref<Record<string, boolean | string | number[] | string[]>>({})

let queryParams = reactive<IGetCrmLeadParams>({
    page: 1,
    pageSize: 10,
    socialCreditCode: props.companyInfo?.socialCreditCode
})
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    getProportionList()
}
const updateSearchParams = (params: IGetCrmLeadParams) => {
    console.log('params', params,params.proportionInterval)
    if (params.proportionInterval) {
        // 如果有值，将 0-100 的区间转换为 0-1 的百分比
        params.proportionInterval = [
            params.proportionInterval[0] / 100,
            params.proportionInterval[1] / 100
        ]
    } else {
        // 如果没有值，设置默认值 [0, 1]
        params.proportionInterval = [0, 1]
    }
    queryParams = params
    getProportionList()
}
const topTenList= ref<IGetTopTenResponseItem[] | []>([])
const getTopTen = () => {
    if(!props.companyInfo?.socialCreditCode) return
    indicatorService.getTopTen({
        socialCreditCode: props.companyInfo?.socialCreditCode,
        scope: props.from === 'up' ? '1' : '2'
    }).then(res => {
        topTenList.value = res
    })
}
const getProportionList = () => {
    tableLoading.value = true
    if (props.from === 'up') {
        queryParams.scope = '1'
    } else {
        queryParams.scope = '2'
    }
    
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    queryParams.socialCreditCode = props.companyInfo?.socialCreditCode
    indicatorService.getProportionList(queryParams).then(res => {
        tableLoading.value = false
        const { errCode, total, data } = res
        if (errCode === 0) {
            pageInfo.total = total
            tableData.value = data
        }
    })
}
onMounted(() => {
    const { start, end } = getLastYearRange()
    defaultQueryParams.value = {
        'dateInterval': [start, end],
        'proportionInterval':[0, 100]
    }
    getTopTen()
})

const handleTurn = (row: IGetTopTenResponseItem, transferto: string) => {
    console.log('点击了转移', row, transferto)
    ElMessageBox.confirm('转移操作将会扣除对应权益额度，是否确定转移？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const postData: ICrmAddFromListRequest = {
            companyInfos: [{socialCreditCode: row.socialCreditCode, companyName: row.companyName}],
            transferTo: transferto,
            transferType: '2',
        }
        crmService.crmAddFromList(postData)
            .then((res) => {
                const { errCode, errMsg } = res || {}
                if (errCode === 0) {
                    ElMessage.success('已提交转移')
                    getProportionList()
                } else {
                    ElMessage.error(errMsg || '转移任务执行失败，请稍后再试')
                }
            }).catch(() => {
                ElMessage.error('系统错误，请稍后再试')
            })
    })
}
</script>
<template>
    <div class="width-100 height-100">
        <!-- 前十大企业 -->
        <div class="display-flex top-bottom-center font-16">
            <div class="w-2 h-16 border-radius-16 r-margin-8" style="background-color: var(--el-color-primary)"></div>
            <div class="font-weight-500 color-black">
                {{ companyInfo?.companyName }} 前十大企业
            </div>
        </div>
        <div class="font-14 t-margin-8">注：数据来源近12月</div>


        <div class="h-300 display-flex">
            <PieChart :data="topTenList"></PieChart>
        </div>
        <!-- 企业列表 -->
        <div class="display-flex top-bottom-center font-16 t-margin-16">
            <div class="w-2 h-16 border-radius-16 r-margin-8" style="background-color: var(--el-color-primary)"></div>
            <div class="font-weight-500 color-black">企业列表</div>
        </div>
        <div class="t-margin-30">
            <SearchBox :searchOptionKey="'RELATED_COMPANY_SEARCH_OPTIONS'" :defaultValue="defaultQueryParams"  @updateSearchParams="updateSearchParams"></SearchBox>
        </div>
        <!-- table -->
        <div class="t-margin-16">
            <el-table ref="tableListRef" :data="tableData" v-loading="tableLoading" row-key="id">
                <el-table-column
                    v-for="columns in tableAllOptions.filter((item) => item.isShow === true)"
                    :key="columns.key"
                    :prop="columns.prop"
                    :label="columns.label"
                    :width="columns.width"
                    :type="columns.type"
                    :fixed="columns.fixed"
                    :sortable="columns.sortable"    
                >
                    <template #default="scope">
                        <div v-if="columns.prop === 'action'">
                            <span v-if="scope.row.isLead" class="font-16" style="color: #a8abb2;">已转移</span>
                            <el-dropdown v-else>
                                <text class="color-primary pointer">转移</text>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="handleTurn(scope.row, '2')">
                                            <span class="color-black">转移至我的线索</span>
                                        </el-dropdown-item>
                                        <el-dropdown-item @click="handleTurn(scope.row, '3')">
                                            <span class="color-black">转移至我的客户</span>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                        <div v-else-if="columns.prop === 'ratio'">
                            <span v-if="scope.row.ratio">
                                {{ Number(scope.row.ratio * 100).toFixed(2) + '%' }}
                            </span>
                            <span v-else>-</span>
                            
                        </div>
                        <span v-else>{{ scope.row[columns.prop] || '-' }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页器 -->
        <el-affix position="bottom" :z-index="2">
            <div class="pagination-bar">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :total="pageInfo.total"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @change="pageChange"
                />
            </div>
        </el-affix>
    </div>
</template>
<style scoped lang="scss">
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>
