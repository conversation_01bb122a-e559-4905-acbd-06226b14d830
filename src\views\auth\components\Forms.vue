<template>
    <div class="form">
        <div class="bg">
            <Triangles />
        </div>
        <div class="container">
            <PasswordForm v-if="isPasswordLogin" />
            <ForgotPassForm v-if="isForgot" />
            <PhoneBindForm v-if="isPhoneBind" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { ForgotPassForm, PasswordForm, PhoneBindForm } from './login-forms'
import { computed } from 'vue'
import Triangles from './Triangles.vue'

const route = useRoute()

// 通过路由 name 判断
const isPasswordLogin = computed(() => route.name === 'login')
const isForgot = computed(() => route.name === 'forgot')
const isPhoneBind = computed(() => route.name === 'phoneBind')
</script>

<style scoped>
.form {
    /* height: 100%;
    z-index: 3; */
    position: relative;
    display: flex;
    width: 532px;
    min-width: 48px;
    /* width: 100%; */
}

.container {
    width: 100%;
    height: 100%;
    background-color: var(--main-white);
    border-radius: 4px;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    z-index: 2;
}

.bg {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    margin-left: 0px;
    width: 100%;
    z-index: 1;
}

/* @media screen and (max-width: 600px) {
    .form {
        width: 100%;
    }

    .container {
        border-radius: 12px;
    }

    .bg {
        opacity: 0.2;
    }
} */

/* 自适应宽度 */
@media screen and (max-width: 1600px) {
    .form {
        width: 440px;
    }
}

@media screen and (max-width: 1200px) {
    .form {
        width: 330px;
    }
}

@media screen and (max-width: 992px) {
    .form {
        width: 268px;
    }
}

@media screen and (max-width: 768px) {
    .form {
        width: 206px;
    }
}

@media screen and (max-width: 576px) {
    .form {
        width: 160px;
    }
}
</style>
