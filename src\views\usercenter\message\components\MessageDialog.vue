<template>
    <el-dialog style="padding: 20px 24px" v-model="visible" :width="width" :show-close="false">
        <template #header>
            <div>
                <div class="display-flex space-between top-bottom-center">
                    <div class="dialog-title font-22">{{ detail?.title || message?.title }}</div>
                    <div class="font-22 pointer" @click="hideDialog">
                        <el-icon><Close /></el-icon>
                    </div>
                </div>
                <div class="dialog-sub-title font-16">{{ message?.content }}</div>
            </div>
        </template>
        <template #default>
            <div v-loading="loading" class="font-16" v-html="detail?.content"></div>
            <div class="t-margin-12 font-14">{{ message?.createTime }}</div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import systemService from '@/service/systemService'
import type { ISystemMessageItem, IMessageDetail } from '@/types/message'
import { ref } from 'vue'

const visible = ref(false)
const detail = ref<IMessageDetail | null>(null)
const message = ref<ISystemMessageItem | null>(null)
const loading = ref(false)

const { width } = defineProps({
    title: {
        type: String,
        default: '',
    },
    width: {
        type: String,
        default: '800',
    },
})

const handleTurnRead = async (val: number, isRead: boolean) => {
    if (!isRead) {
        systemService.messageMarkNoticeRead({ msgId: [val] })
        emit('refresh')
    }
}

const queryMessageDetail = (linkNoticeId: number) => {
    loading.value = true
    systemService
        .messageDetail({ linkNoticeId: linkNoticeId })
        .then((data) => {
            detail.value = data
        })
        .finally(() => {
            loading.value = false
        })
}

const emit = defineEmits(['refresh'])
const showDialog = (record: ISystemMessageItem) => {
    visible.value = true
    message.value = record
    handleTurnRead(record.msgId, record.isRead)
    queryMessageDetail(record.linkNoticeId)
}

const hideDialog = () => {
    visible.value = false
    detail.value = null
}

defineExpose({
    showDialog,
    hideDialog,
})
</script>

<style lang="scss" scoped>
.dialog-title {
    color: var(--six-grey);
    font-weight: var(--weight-500);
}
.dialog-sub-title {
    color: var(--three-grey);
    margin-top: 4px;
}
.message-content {
    color: var(--two-grey);
}
</style>
