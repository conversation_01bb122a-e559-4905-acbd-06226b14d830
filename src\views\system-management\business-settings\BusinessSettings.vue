<script lang="ts" setup>
import { ref } from 'vue'
import { CommonSetting } from './components'
const scopeRef = ref('leads')
</script>

<template>
    <div class="flex flex-column gap-16 height-100">
        <div class="back-color-white border-radius-4 all-padding-16">
            <el-tabs v-model="scopeRef">
                <el-tab-pane label="线索设置" name="leads" class="height-100">
                    <div class="back-color-table-bg-2 all-padding-16 font-14 lh-24 color-black">
                        <div>提示：</div>
                        <div>1、员工拥有线索超过规则上限时，将无法新增线索</div>
                        <div>2、在规则设置前，员工已有超出上限的线索将保留</div>
                        <div>3、规则优先级：自定义规则 > 默认规则</div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="客户设置" name="customer" class="height-100">
                    <div class="back-color-table-bg-2 all-padding-16 font-14 lh-24 color-black">
                        <div>提示：</div>
                        <div>1、员工拥有线索超过规则上限时，将无法新增客户</div>
                        <div>2、在规则设置前，员工已有超出上限的客户将保留，不受影响</div>
                        <div>3、规则优先级：自定义规则 > 默认规则</div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <div class="back-color-white height-100 oh all-padding-16">
            <CommonSetting v-if="scopeRef === 'leads'" :service-type="1" tips="限制每个员工拥有的最大线索数量" />
            <CommonSetting v-if="scopeRef === 'customer'" :service-type="2" tips="限制每个员工拥有的最大客户数量" />
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
