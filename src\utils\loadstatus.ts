import yunyingService from '@/service/yunyingService'

export const loadStatus = async () => {
    const serverStatus = {
        maintenance: false,
    }

    const link = import.meta.env.VITE_APP_STATUS_URL + '?v=' + Math.random().toString()

    try {
        const status = await fetch(link).then((res) => res.json())
        const ip = await yunyingService.microsrvGetIp()
        const { maintenance, white_ip } = status

        if (maintenance && !white_ip.includes(ip)) {
            serverStatus.maintenance = true
        }
    } catch (error) {
        console.info(error)
    }

    return serverStatus
}
