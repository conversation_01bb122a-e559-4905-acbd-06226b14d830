import { ElMessage } from 'element-plus'
export const copy = {
    mounted(el) {
        // 监听点击事件
        el.addEventListener('click', (event) => {
            // 获取元素内容

            const text = el.innerText || el.textContent
            const textArea = document.createElement('textarea')

            console.log(text)

            // 设置内容并添加到文档中
            textArea.value = text.trim()
            document.body.appendChild(textArea)

            // 选中并拷贝内容
            textArea.select()
            document.execCommand('copy')

            // 删除临时的 textarea
            document.body.removeChild(textArea)

            // 可选: 提示用户内容已被复制
            ElMessage({
                message: '复制成功',
                type: 'success',
            })
            event.stopPropagation()
        })
    }
}