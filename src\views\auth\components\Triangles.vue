<template>
    <div class="triangles">
        <div class="img-container">
            <img src="../../../assets/images/auth/triangles.png" alt="" class="img" />
        </div>
    </div>
</template>

<script setup lang="ts"></script>

<style scoped>
.triangles {
    height: 100%;
    display: flex;
    align-items: center;
}
.img-container {
    width: 1870px;
    margin-right: -1072px;
    display: flex;
    align-items: center;
}

.triangles .img {
    width: 100%;
    height: 100%;
}
/* 自适应宽度 */
@media screen and (max-width: 1600px) {
}

@media screen and (max-width: 1200px) {
    .triangles .img {
        width: 74%;
    }
}

@media screen and (max-width: 992px) {
    .triangles .img {
        width: 64%;
    }
}

@media screen and (max-width: 768px) {
    .triangles .img {
        width: 54%;
    }
}

@media screen and (max-width: 576px) {
    .triangles .img {
        width: 54%;
    }
}
</style>
