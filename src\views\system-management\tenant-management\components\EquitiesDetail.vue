<script setup lang="ts">
import { ref, reactive, onMounted, watch, getCurrentInstance } from 'vue'
// import { EQUITIES_LIST_TABLE_COLUMNS } from '@/common/config/table/table-columns'
import { EQUITIES_LIST_TABLE_COLUMNS } from '@/js/table-options'
import type { ITenantPageItem } from '@/types/tenant'
import orderService from '@/service/orderService'
import type { IServiceOrderResponseItem } from '@/types/order'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
interface TabItem {
    label: string
    key: string
    num: number | string
    unit: string
    service_id: string
}

const tabList = ref<TabItem[]>([
    { label: '线索号码', key: 'xs', num: 0, unit: '家', service_id: '' },
    { label: '高企报告', key: 'gqbg', num: 0, unit: '份', service_id: '' },
    { label: '发票报告', key: 'fpbg', num: 0, unit: '份', service_id: '' },
    { label: '财税报告', key: 'swbg', num: 0, unit: '份', service_id: '' },
    { label: '智能外呼', key: 'znwh', num: 0, unit: '条', service_id: '' },
])
const props = defineProps<{
    visible: boolean
    tenantInfo: Partial<ITenantPageItem>
}>()
const dialogVisible = ref(false)
const tenantId = ref('')
watch(() => props, (newVal) => {
    dialogVisible.value = newVal.visible
    if (newVal.tenantInfo) {
        const { id } = newVal.tenantInfo
        tenantId.value = id || ''
    }
}, {
    deep: true,
    immediate: true
})
const lessDataLoading = ref (false)
const tableLoading = ref(false)
const selectedEquity = ref<TabItem | null>(null)
const tableData = ref<IServiceOrderResponseItem[]>([])

onMounted(() => {
    getLessData()
    getServiceOrder()
})

const emit = defineEmits(['closeVisible'])
const handleClose = () => {
    emit('closeVisible')
}

let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

const handleClickEquity = (item: TabItem) => {
    selectedEquity.value = selectedEquity.value?.key === item.key ? null : item
    getServiceOrder()
}

const getLessData = async() => {
    lessDataLoading.value = true
    await orderService.orderServiceStatistics(
        {
            serviceKeys:'xs,gqbg,fpbg,swbg,znwh',
            tenantId: tenantId.value
        }
    ).then((res) => {
        console.log('res', res)
        res.filter((item) => item.label === 'xs').forEach((item) => {
            tabList.value[0].num = item.num
        })
        res.filter((item) => item.label === 'gqbg').forEach((item) => {
            tabList.value[1].num = item.num
        })
        res.filter((item) => item.label === 'fpbg').forEach((item) => {
            tabList.value[2].num = item.num
        })
        res.filter((item) => item.label === 'swbg').forEach((item) => {
            tabList.value[3].num = item.num
        })
        res.filter((item) => item.label === 'znwh').forEach((item) => {
            tabList.value[4].num = item.num
        })
    })
    lessDataLoading.value = false
}

const getServiceOrder = async () => {
    tableLoading.value = true
    try {
        let queryParams = {
            page: pageInfo.page,
            pageSize: pageInfo.pageSize,
            serviceKeys: selectedEquity.value?.key,
            tenantId: tenantId.value
        }
        let res = await orderService.orderServiceOrderPage(queryParams)
        tableLoading.value = false
        if (res.errCode === 0) {
            tableData.value = res.data
            pageInfo.total = res.total
        }
    } catch (error) {
        tableLoading.value = false
        console.log('error', error)
    }
}
const pageChange = (currentPage: number, pageSize: number) => {
    pageInfo.page = currentPage
    pageInfo.pageSize = pageSize
    getServiceOrder()
}
</script>

<template>
    <el-dialog v-model="dialogVisible" title="权益明细" width="1200" @close="handleClose">
        <div class="equitiesList" v-loading="lessDataLoading">
            <div
                class="equitiesList-item"
                :class="{ active: item.key === selectedEquity?.key }"
                v-for="item in tabList"
                :key="item.key"
                @click="handleClickEquity(item)"
            >
                <div class="equitiesList-item-title">
                    {{ item.label }}
                </div>
                <div class="quitiesList-item-residue">
                    {{ item.num }}<span style="font-size: 12px">{{ item.unit }}</span>
                </div>
            </div>
        </div>

        <el-table
            :data="tableData"
            style="width: 100%; min-height: 500px"
            v-loading="tableLoading"
            show-overflow-tooltip
            row-key="_id"
            height="500"
        >
            <el-table-column
                v-for="column in EQUITIES_LIST_TABLE_COLUMNS"
                :key="column.key"
                :prop="column.prop"
                :label="column.label"
                :width="column.width"
                :type="column.type"
            >
                <template #default="scope">
                    <div v-if="column.prop === 'service_name'">
                        {{ scope.row.serviceName }}
                    </div>
                    <div v-if="column.prop === 'balance'">
                        {{ scope.row.remainingAmount }}/{{ scope.row.totalAmount }}
                    </div>
                    <div v-if="column.prop === 'createTime'">
                        {{ scope.row.createTime? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </div>
                    <div v-if="column.prop === 'endTime'">
                        {{ scope.row.endTime? moment(scope.row.endTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <el-row style="padding-top: 16px" justify="end">
            <el-pagination
                v-model:currentPage="pageInfo.page"
                v-model:page-size="pageInfo.pageSize"
                :total="pageInfo.total"
                layout="total, sizes, prev, pager, next, jumper"
                @change="pageChange"
            />
        </el-row>
    </el-dialog>
</template>

<style lang="scss" scoped>
.equitiesList {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .equitiesList-item {
        color: #262626;
        padding: 20px 0px;
        width: 217px;
        height: 98px;
        border-radius: 8px;
        background: #fafbfc;
        border: 1px solid #f0f0f0;
        .equitiesList-item-title {
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        .quitiesList-item-residue {
            padding-top: 12px;
            font-size: 28px;
            color: #303133;
            text-align: center;
        }
    }
    .equitiesList-item:hover {
        background: #ffffff;
        border: 1px solid #f0f0f0;
        box-shadow: 0px 2px 12px #f0f0f0;
    }
    .active {
        color: #262626;
        padding: 20px 0px;
        width: 217px;
        height: 98px;
        border-radius: 8px;
        background: #ffffff;
        border: 1px solid #f0f0f0;
        box-shadow: 0px 2px 12px #f0f0f0;
    }
}
</style>
