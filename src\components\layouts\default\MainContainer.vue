<script lang="tsx" setup>
import { ref, watch } from 'vue'
import RouterBreadcrumb from '@/components/common/RouterBreadcrumb.vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const fullContainer = ref(false)

const hideRouterBread = ref(false)


watch(route, () => {
    //是否满container
    if (route.meta?.fullContainer) {
        fullContainer.value = true
    } else {
        fullContainer.value = false
    }

    //是否隐藏面包屑
    if (route.meta?.hideRouterBread) {
        hideRouterBread.value = true
    } else {
        hideRouterBread.value = false
    }
})



</script>

<template>
    <div class="flex flex-column height-100">
        <RouterBreadcrumb v-show="!hideRouterBread" />
        <div class=" flex flex-1 flex-column overflow-y-auto" :class="{ 'all-padding-16': !fullContainer }">
            <router-view />
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
