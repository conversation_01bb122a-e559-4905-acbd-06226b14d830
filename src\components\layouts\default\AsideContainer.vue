<script lang="tsx" setup>
import { Menu } from './components/aside'
import { ref } from 'vue'

const isCollapse = ref(false)

const toggleCollapse = () => {
    isCollapse.value = !isCollapse.value
}
</script>

<template>
    <el-aside :width="isCollapse ? '64px' : '200px'">
        <Menu :isCollapse="isCollapse" />
        <div class="split-line"></div>
        <div @click="toggleCollapse" class="all-padding-12 pointer flex left-right-center top-bottom-center">
            <el-icon :size="18">
                <Expand v-if="isCollapse" />
                <Fold v-else />
            </el-icon>
        </div>
    </el-aside>
</template>

<style lang="scss" scoped>
.el-aside {
    display: flex;
    flex-direction: column;
    transition: width 0.3s;
    -webkit-transition: width 0.3s;
    -moz-transition: width 0.3s;
    -webkit-transition: width 0.3s;
    -o-transition: width 0.3s;
    max-height: calc(100vh - 49px);
    overflow: hidden;
}
</style>
