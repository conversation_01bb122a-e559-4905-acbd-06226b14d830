<script lang='ts' setup>
import { ref, reactive, defineProps, watch, defineEmits, onBeforeMount } from 'vue'
import type { ILeadData, IGetTagList } from '@/types/lead'
import { ElMessage } from 'element-plus'
import crmService from '@/service/crmService'

type FormType = {
    tagId?: string
    crmIds?: string[]
}

const props = defineProps<{
    visible: boolean
    selectedData:ILeadData[]
}>()

const dialogVisible = ref(false)
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})

const emit = defineEmits(['closeVisible'])

const form = ref<FormType>({})
const handleClose = (val?:string) => {
    form.value = {}
    emit('closeVisible',val)
}

const tagList = ref<IGetTagList[]>([])
const pageInfo = reactive({
    page: 1,
    pageSize: 1000,
    total: 0,
})

const handleGetTagList = (str: string = '') => {
    let queryParams = {
        page: pageInfo.page,
        pageSize: pageInfo.pageSize,
        ...(str ? { tagName: str } : {})
    }
    crmService.crmTagList(queryParams).then((res) => {
        if(res.success){
            tagList.value = res.data
            pageInfo.total = res.total
        }else{
            ElMessage.error(`系统繁忙,请稍后重试`)
        }
    })
}

const handleSubmit = () => {
    if (!form.value.tagId) {
        ElMessage.warning('请选择标签')
        return
    }
    const data = {
        leadIds: props.selectedData.map((item) => item.id),
        tagId: form.value.tagId,
    }
    crmService.branchUpdateTag(data).then((res) => {
        if(res.success){
            ElMessage.success('操作成功')
            handleClose('success')
        }else{
            ElMessage.error(`系统繁忙,请稍后重试`)
        }
    })
}

onBeforeMount(() => {
    handleGetTagList()
})
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        title="添加标签"
        width="500"
        show-close
        destory-on-close
        @close="handleClose('cancel')"
    >
        <el-form :model="form" label-position="left" style="margin: 16px 36px 0">
            <el-form-item label="选择标签" >
                <el-select 
                    v-model="form.tagId"
                    placeholder="请选择标签"
                    clearable
                    remote
                    :remote-method="handleGetTagList"
                >
                    <el-option 
                        v-for="item in tagList"
                        :key="item.id"
                        :label="item.tagName"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose('cancel')">取消</el-button>
                    <el-button type="primary" @click="handleSubmit()">保存</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style lang='scss' scoped>

</style>