<script lang="ts" setup>
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import crmService from '@/service/crmService'
import systemService from '@/service/systemService'
import type { IUserListItem } from '@/types/org'
import type { IServiceDetailResponse, IServiceDetailRules } from '@/types/service'
import permissionService from '@/service/permissionService'

const props = defineProps<{
    serviceType: number
    tips: string
}>()

const serviceData = ref<IServiceDetailResponse | null>(null)
const userList = ref<IUserListItem[]>([])
const isLoadingUsers = ref(false)
const isServiceActive = ref(false)
const userRules = ref<IServiceDetailRules[]>([])

const userRuleMap = ref<Map<string, number>>(new Map())

const filteredUserList = computed(() => {
    return userList.value.map((user) => ({
        ...user,
        isAssigned: userRuleMap.value.has(user.id),
    }))
})

const fetchUsers = async () => {
    try {
        isLoadingUsers.value = true
        const res = await systemService.userList({})
        userList.value = res
    } catch (error) {
        console.error('Failed to fetch users:', error)
        ElMessage.error('获取用户列表失败')
    } finally {
        isLoadingUsers.value = false
    }
}

const fetchServiceDetails = async () => {
    try {
        const res = await crmService.serviceDetail({ type: props.serviceType })
        serviceData.value = res
        userRules.value = res.rules
        isServiceActive.value = res.isOpen

        userRuleMap.value.clear()
        res.rules.forEach((rule, ruleIndex) => {
            rule.users.forEach((userId) => {
                userRuleMap.value.set(userId, ruleIndex)
            })
        })
    } catch (error) {
        console.error('Failed to fetch service details:', error)
        ElMessage.error('获取服务详情失败')
    }
}

const toggleServiceStatus = async (isActive: boolean) => {
    try {
        await crmService.serviceSwitch({ type: props.serviceType, isOpen: isActive })
    } catch (error) {
        console.error('Failed to toggle service status:', error)
        ElMessage.error('更新服务状态失败')
        await fetchServiceDetails()
    }
}

const updateServiceRules = async () => {
    if (!serviceData.value) return

    try {
        const validRules = userRules.value.filter((rule) => rule.users.length > 0 || rule.allUser)

        const res = await crmService.serviceUpdate({
            id: serviceData.value.id,
            type: props.serviceType,
            isOpen: isServiceActive.value,
            rules: validRules,
        })

        if (res.errCode === 0) {
            // 成功后重新获取最新数据以确保一致性
        } else {
            ElMessage.error(res.errMsg || '更新规则失败')
            // 失败后回滚到服务器最新数据
            await fetchServiceDetails()
        }
    } catch (error) {
        console.error('Failed to update service rules:', error)
        ElMessage.error('请求失败，请检查网络连接')
        // 失败后回滚到服务器最新数据
        await fetchServiceDetails()
    }
}

const handleUserSelection = (selectedUserIds: string[], ruleIndex: number) => {
    const newMap = new Map<string, number>()
    selectedUserIds.forEach((userId) => {
        newMap.set(userId, ruleIndex)
    })
    userRuleMap.value = newMap
    updateServiceRules()
}

const addNewRule = () => {
    userRules.value.push({
        num: 1,
        allUser: false,
        users: [],
    })
}

const deleteRule = (ruleIndex: number) => {
    const rule = userRules.value[ruleIndex]
    rule.users.forEach((userId) => {
        userRuleMap.value.delete(userId)
    })

    userRules.value.splice(ruleIndex, 1)
    updateServiceRules()
}

onMounted(() => {
    fetchServiceDetails()
    fetchUsers()
})
</script>

<template>
    <div class="flex flex-column height-100 common-setting-table gap-20">
        <div class="flex flex-row top-bottom-center">
            <div class="flex flex-row gap-8 top-bottom-center font-16">
                <el-switch
                    v-model="isServiceActive"
                    size="large"
                    inline-prompt
                    active-text="开启"
                    inactive-text="关闭"
                    @change="toggleServiceStatus"
                />
                <span>{{ tips }}</span>
            </div>
            <div v-if="permissionService.isBusinessAddPermitted()" class="flex flex-1 justify-flex-end">
                <el-button type="primary" @click="addNewRule" v-if="isServiceActive">新增规则</el-button>
            </div>
        </div>
        <el-table :data="userRules" style="width: 100%; height: 100%">
            <el-table-column prop="num" label="上限">
                <template #default="{ row }">
                    <div class="flex h-48 top-bottom-center">
                        <el-input-number
                            v-model="row.num"
                            :min="0"
                            :max="999999999"
                            :step="1"
                            step-strictly
                            @change="updateServiceRules"
                        >
                            <template #suffix>
                                <span>条</span>
                            </template>
                        </el-input-number>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="taskName" label="规则针对人员">
                <template #default="{ row, $index }">
                    <div v-if="row.allUser">所有员工（默认规则）</div>
                    <el-select
                        v-else
                        multiple
                        v-model="row.users"
                        placeholder="选择针对人员"
                        style="width: 302px"
                        @change="(v: string[]) => handleUserSelection(v, $index)"
                        :loading="isLoadingUsers"
                    >
                        <el-option
                            :label="user.nickname"
                            :value="user.id"
                            :key="user.id"
                            v-for="user in filteredUserList"
                            :disabled="user.isAssigned && userRuleMap.get(user.id) !== $index"
                        />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="taskName" width="60" label="操作">
                <template v-if="permissionService.isBusinessDeletePermitted()" #default="{ row, $index }">
                    <el-button
                        text
                        type="primary"
                        :icon="Delete"
                        circle
                        @click="deleteRule($index)"
                        v-if="!row.allUser"
                    />
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<style lang="scss" scoped>
.common-setting-table :deep(.el-input-number) {
    width: 160px;
}

.common-setting-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
