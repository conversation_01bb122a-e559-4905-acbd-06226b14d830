<template>
    <div class="flex top-bottom-center gap-4 pointer" @click="toTaskManagement">
        <div class="border-radius-16 flex-center relative">
            <Icon icon="icon-a-renwuguanli" size="19"></Icon>
        </div>
        <div class="font-header-label color-two-grey">任务管理</div>
    </div>
</template>

<script lang='ts' setup>
import Icon from '@/components/common/Icon.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const toTaskManagement = () => {
    router.push('/system-management/task-management')
}
</script>

<style lang='scss' scoped>
</style>