<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { RecordDetail, RecordInfo } from './components'
import type { IAutoDialerTaskCallDetailItem, IAutoDialerTaskDetailListItem } from '@/types/autoDialer'
import moment from 'moment'
import outboundService from '@/service/outboundService'

// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================
const props = defineProps<{
    visible: boolean
    taskInfo?: IAutoDialerTaskDetailListItem
}>()

const emit = defineEmits(['update:visible'])

//
// const emit = defineEmits<{
//   (e: 'eventName', payload: SomeType): void
// }>()

// ====================== Store & Computed Values ======================
// const store = useSomeStore()
// const computedValue = computed(() => store.value)
const title = computed(() => {
    const { name, companyName } = currentTask.value || {}
    if (!name && !companyName) return '通话详情'
    if (!companyName) return `${name}的通话详情`
    return `${name}(${companyName})的通话详情`
})

const activeName = ref('')

// ====================== Refs & Reactive State ======================
const drawerVisible = ref(false)
const tabPosition = ref('left')
const list = ref<IAutoDialerTaskCallDetailItem[]>([])
const currentTask = ref<IAutoDialerTaskDetailListItem | null>(null)

// ====================== Methods ======================
const handleClose = () => {
    emit('update:visible', false)
}

const getData = (task: IAutoDialerTaskDetailListItem) => {
    outboundService
        .taskCallDetail({
            taskCode: task.taskCode,
            cellphone: task.phoneNumber,
        })
        .then((res) => {
            console.log(res)
            const { errCode, data } = res || {}
            if (errCode === 0) {
                list.value = data

                if (!list.value) return
                if (!Array.isArray(list.value)) return
                if (list.value.length === 0) return
                activeName.value = list.value[0].callId
            }
        })
}

// ====================== Watchers ======================
// watch(someRef, (newVal) => {
//   // Side effect
// }, { immediate: true })
watch(
    () => props.visible,
    (val) => {
        console.log('visible')
        console.log(props.taskInfo)
        drawerVisible.value = val
        if (val) {
            if (props.taskInfo) {
                currentTask.value = props.taskInfo
                getData(props.taskInfo)
            }
        } else {
            currentTask.value = null
        }
    }
)

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <el-drawer v-model="drawerVisible" :title="title" @close="handleClose" class="call-record-drawer" size="80%">
        <div class="flex flex-row height-100 gap-24">
            <div class="width-100">
                <el-tabs v-model="activeName" class="height-100" :tab-position="tabPosition">
                    <template v-for="record in list" :key="record.callId">
                        <el-tab-pane :name="record.callId" class="height-100">
                            <template #label>
                                <div class="flex flex-row gap-8 top-bottom-center">
                                    <div
                                        class="h-12 w-12 border-radius-12"
                                        :class="{
                                            'back-color-green': record.callStatus === 1,
                                            'back-color-dark-red': record.callStatus !== 1,
                                        }"
                                    ></div>
                                    <div
                                        class="font-20 font-weight-400"
                                        :class="{
                                            'color-green': record.callStatus === 1,
                                            'color-three-grey': record.callStatus !== 1,
                                        }"
                                    >
                                        {{
                                            record.callStartTime
                                                ? moment(record.callStartTime).format('YYYY-MM-DD HH:mm:ss')
                                                : '-'
                                        }}
                                    </div>
                                </div>
                            </template>
                            <div class="flex flex-row height-100 flex-1" v-if="activeName === record.callId">
                                <RecordInfo :record="record" :task-info="currentTask" v-if="currentTask" />
                                <RecordDetail :record="record" :task-info="currentTask" v-if="currentTask" />
                            </div>
                        </el-tab-pane>
                    </template>
                </el-tabs>
            </div>
        </div>
    </el-drawer>
</template>

<style lang="scss" scoped>
// :deep(.el-tabs__nav-wrap.is-left::after) {
//     height: 100%;
//     background-color: var(--table-bg-);
//     // width: 1px;
// }
// :deep(.el-tabs__active-bar.is-left) {
//     // width: 1px;
// }
:deep(.el-tabs__item) {
    padding: 0 24px 0 0;
}
:deep(.el-tabs--left .el-tabs__header.is-left) {
    margin-right: 0px;
}
:deep(.el-drawer) {
    width: 1276px;
}

.call-record-drawer {
    width: 1276px !important;
}

// @media screen and (min-width: 1276px) {
//     :deep(.el-drawer) {
//         width: 980px;
//     }
// }
</style>
