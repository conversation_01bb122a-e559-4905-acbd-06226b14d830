<template>
    <div class="main"></div>
</template>

<script setup lang="ts">
import openapiService from '@/service/openapiService'
import { ElLoading } from 'element-plus'
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'
import { setItem } from '@/utils/storage'

const route = useRoute()
const router = useRouter()
const store = useStore<RootState>()
const s_token = route.query.s_token

const getToken = () => {
    if (!s_token) return
    const loading = ElLoading.service({
        lock: true,
        text: '正在跳转...',
        background: '#ffffff',
    })

    openapiService.ssoExchangeToken({ s_token: s_token as string }).then((res) => {
        loading.close()
        const { errCode, errMsg, data } = res
        const { params } = data

        if (errCode === 0) {
            const { to, queryParams } = params || {}
            store.dispatch('auth/loginSuccess', data)

            if (to === '/find-companies/search/advance' && queryParams) {
                setItem('advanceSearchRules', queryParams)
                router.push(to)

                return
            }

            if (to) router.push(to)
        } else {
            router.push({
                name: '403',
                query: {
                    msg: errMsg,
                    hideBtn: 1,
                },
            })
        }
    })
}

onMounted(() => {
    getToken()
})
</script>

<style lang="scss" scoped></style>
