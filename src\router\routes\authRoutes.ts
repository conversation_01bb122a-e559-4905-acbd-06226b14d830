import { type RouteRecordRaw } from 'vue-router'
import LoginView from '@/views/auth/LoginView.vue'
import StokenView from '@/views/auth/StokenView.vue'
import ForbiddenPage from '@/views/403/ForbiddenPage.vue'

const authRoutes: Array<RouteRecordRaw> = [
    {
        path: '/login',
        name: 'login',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/forgot',
        name: 'forgot',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/phone-bind',
        name: 'phoneBind',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/phone-validate',
        name: 'phoneValidate',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/stoken',
        name: 'stokenLogin',
        component: StokenView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/403',
        name: '403',
        component: ForbiddenPage,
        meta: { requiresAuth: false }, // 路由元信息
    },
]

export default authRoutes
