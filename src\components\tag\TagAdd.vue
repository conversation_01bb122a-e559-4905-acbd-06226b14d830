<template>
    <div>
        <el-icon class="pointer" @click="handleBackToBind"><ArrowLeftBold /></el-icon>
        <span class="l-margin-10 color-black font-16">新建标签</span>
    </div>
    <div class="l-margin-10 t-margin-16">
        <div class="display-flex justify-between">
            <div class="font-16 color-red r-margin-6">*</div>
            <div class=" font-16 color-two-grey b-margin-6">标签名称</div>
        </div>
        <el-input class="b-margin-12 t-margin-8" v-model="tagName" placeholder="请输入标签名称" style="width: 100%;" maxlength="10"></el-input>
        <div class="b-margin-4 display-flex justify-between">
            <div class="font-16 color-red r-margin-6">*</div>
            <div class="font-16 color-two-grey b-margin-6">标签颜色</div>
        </div>

        <a v-for="item in colors" :key="item.id" :value="item.color" class="r-margin-10">
            <el-tag
                class="pointer no-border"
                style="width: 20px;height: 20px;"
                :color="item.color"
                @click="handleTagClick(item.id)"
                round
            >
                <el-icon v-if="item.id === selectedTag" color="#fff"><Check /></el-icon>
            </el-tag>
        </a>
    </div>

    <div class="display-flex justify-flex-end all-padding-10 t-margin-20">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="confirm" :loading="Loading">确 定</el-button>
    </div>
</template>

<script lang='ts' setup>
import { ref } from 'vue'
import crmService from '@/service/crmService'
import { ElMessage } from 'element-plus'
import type { IAddTagParams } from '@/types/lead'
import eventBus from '@/utils/eventBus'

const emit = defineEmits(['backToBind','cancel','addTag'])
const handleBackToBind = () => {
    emit('backToBind')
}
const handleCancel = () => {
    tagName.value = ''
    selectedTag.value = 1
    emit('cancel')
}
const addTagParams = ref<IAddTagParams>({
    tagName: '',
    color: ''
})
const Loading = ref(false)
const tagName = ref('')
const colors = [
    {
        id: 1,
        color: '#7ACCCC'
    }, 
    {
        id: 2,
        color: '#7AA4CC'
    },
    {
        id: 3,
        color: '#4F73DE'
    },
    {
        id: 4,
        color: '#9977D4'
    },
    {
        id: 5,
        color: '#AA9F70'
    },
    {
        id: 6,
        color: '#7AAC4E'
    },
    {
        id: 7,
        color: '#CF7CC5'
    },
    {
        id: 8,
        color: '#C8C985'
    },
    {
        id: 9,
        color: '#DF974E'
    },
    {
        id: 10,
        color: '#E84838'
    }
]
const selectedTag = ref(1)
const handleTagClick = (id: number) => {
    selectedTag.value = id
}

const confirm = () => {
    
    if (!tagName.value) {
        ElMessage.error('标签名称不能为空')
    }else{
        Loading.value = true
        addTagParams.value.tagName = tagName.value
        addTagParams.value.color = colors.find(item => item.id === selectedTag.value)!.color
        crmService.crmTagAdd(addTagParams.value).then(res => {
            if (res.success) {
                // ElMessage.success('新建成功')
                eventBus.$emit('refreshSearchOptions')
                emit('addTag',res.data)  
                tagName.value = ''
                selectedTag.value = 1
            } 
            else {
                ElMessage.error(res.errMsg)
            }
        }).finally(() => {
            Loading.value = false
        })
    }
    
}

</script>

<style lang='scss' scoped>
.no-border{
    border: none;
}
</style>