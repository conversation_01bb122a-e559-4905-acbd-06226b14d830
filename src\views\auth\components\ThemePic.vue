<template>
    <div class="theme-pic">
        <img class="pic" :src="logoImg" alt="pic" />
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import ThemePic from '@/assets/images/auth/theme_pic.png'
import fileService from '@/service/fileService'

const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

const oemStorage = sessionStorage.getItem('oemConfig')
const logoImg = computed(() => {
    if(oemStorage && JSON.parse(oemStorage) && JSON.parse(oemStorage).modules.length > 0 && JSON.parse(oemStorage).modules[0].config.logoImg){
        return fileService.getFileUrl(JSON.parse(oemStorage).modules[0].config.logoImg)
    }else if(oemInfo?.value?.logoImg){
        return fileService.getFileUrl(oemInfo.value.logoImg)
    }else{
        return ThemePic
    }
})

onMounted(() => {
    console.log('ThemePic mounted',oemInfo.value)
})
</script>

<style scoped>
.theme-pic {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

@media screen and (max-width: 3000px) {
    .pic {
        height: 500px;
    }
}

@media screen and (max-width: 1600px) {
    .pic {
        height: 400px;
    }
}

@media screen and (max-width: 1200px) {
    .pic {
        height: 312px;
    }
}

@media screen and (max-width: 992px) {
    .pic {
        height: 256px;
    }
}

@media screen and (max-width: 768px) {
    .pic {
        height: 200px;
    }
}

@media screen and (max-width: 576px) {
    .pic {
        height: 150px;
    }
}
</style>
