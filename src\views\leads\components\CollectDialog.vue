<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps<{
    visible: boolean
    collectUrl: string
    requestId: string
}>()
const collectDialogVisible = ref(false)
const showQrCode = ref(false)
import reportService from '@/service/reportService'
let ti = 0

const emit=defineEmits(['update:visible'])
const closeCollectDialog = () => {
    clearInterval(ti)
    emit('update:visible', false)
    // collectDialogVisible.value = false
}
const copyUrl = () => {
    const textArea = document.createElement('textarea')

    // 设置内容并添加到文档中
    // textArea.value = collectUrl.value.trim()
    textArea.value = props.collectUrl.trim()
    document.body.appendChild(textArea)

    // 选中并拷贝内容
    textArea.select()
    document.execCommand('copy')

    // 删除临时的 textarea
    document.body.removeChild(textArea)

    // 可选: 提示用户内容已被复制
    ElMessage({
        message: '复制成功',
        type: 'success',
    })
}
const showScanCode = () => {
    showQrCode.value = !showQrCode.value
    if (showQrCode.value) {
        getCollectRes()
        // 获取扫码状态
    } else {
        clearInterval(ti)
    }
}
const getCollectRes = () => {
    ti = setInterval(() => {
        reportService
            .collectPage({
                requestId: props.requestId,
            })
            .then((res) => {
                console.log(res)
                if (res.data[0].status !== 'WAITING') {
                    //用户已授权成功,预计30分钟授权数据完成,期间请勿登录税局,如登录需再次发起授权!
                    emit('update:visible', false)
                    // collectDialogVisible.value = false
                }
            })
    }, 3000)
}
watch(() => props.visible, (newVal) => {
    collectDialogVisible.value = newVal
})


</script>
<template>
    <el-dialog v-model="collectDialogVisible" width="700px" v-if="collectDialogVisible" @close="closeCollectDialog">
        <template #header="{ titleId, titleClass }">
            <div class="display-flex space-between padding-top-5 top-bottom-center tb-margin-0">
                <div :id="titleId" :class="titleClass">企业税票授权</div>
                <div class="margin-right-36 font-size-14 pointer">
                    <el-popover :visible="showQrCode" placement="top" :width="220">
                        <div>
                            <vue-qr
                                class="border t-margin-8"
                                ref="qrcode"
                                :title="collectUrl"
                                :text="collectUrl"
                                :size="180"
                            ></vue-qr>
                        </div>
                        <div class="margin-top-16">
                            <el-button type="primary" @click="copyUrl">复制链接发送给客户填写</el-button>
                        </div>
                        <template #reference>
                            <div @click="showScanCode" class="display-flex top-bottom-center pointer gap-10">
                                <el-icon>
                                    <FullScreen />
                                </el-icon>
                                面对面扫码
                            </div>
                        </template>
                    </el-popover>
                </div>
            </div>
        </template>
        <iframe id="myIframe" class="width-100" style="height: 100vh" :src="collectUrl" frameborder="0"></iframe>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
