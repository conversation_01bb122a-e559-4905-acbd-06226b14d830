<template>
    <el-dialog 
        v-model="dialogVisible"
        title='权益列表'
        width="60%"
    >
        <div class="border"></div>
        <el-table
            ref="tableList"
            :data="benefiList"
            style="width: 100%"
            v-loading="benefiListLoading"
            show-overflow-tooltip
            empty-text="暂无数据"
            :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
        >
            <el-table-column label="权益名称" prop="name" min-width="150" >
                <template #default="scope">
                    {{ scope.row.serviceName }}
                </template>
            </el-table-column>
            <el-table-column label="总额度" prop="totalAmount" />
            <el-table-column label="剩余额度" prop="remainingAmount" />
            <el-table-column label="状态" prop="status">
                <template #default="scope">
                    {{ scope.row.status ? '未过期' : '已过期' }}
                </template>
            </el-table-column>
            <el-table-column label="购买时间" prop="createTime" min-width=“150” >
                <template #default="scope">
                    {{ scope.row.createTime? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="到期时间" prop="endTime" min-width=“150” >
                <template #default="scope">
                    {{ scope.row.endTime? moment(scope.row.endTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页器 -->
        <el-affix position="bottom">
            <div class="pagination-bar">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :total="pageInfo.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @change="pageChange"
                />
            </div>
        </el-affix> 
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, defineProps, reactive, computed, watch, getCurrentInstance } from 'vue'
import type { IOrderTransListParams, IServiceOrderResponseItem } from '@/types/order'
import orderService from '@/service/orderService'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 10,
    total: totalNum,
})

const props = defineProps<{
    visible: boolean
    tenantId:string
    serviceKey:string  
}>()
const emit = defineEmits(['update:visible'])
const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})
const benefiListLoading = ref(false)
const benefiList = ref<IServiceOrderResponseItem[]>([])
const currentDate = new Date()
const afterThirtyDays = new Date(currentDate)
afterThirtyDays.setDate(currentDate.getDate() + 30)
currentDate.setHours(0, 0, 0, 0)
afterThirtyDays.setHours(23, 59, 59, 999)
const currentDateTimestamp = currentDate.getTime()
const afterThirtyDaysTimestamp = afterThirtyDays.getTime()
const queryParams = ref<IOrderTransListParams>({
    page: 1,
    pageSize: 10,
    tenantId: '',
    serviceKey: '',
    endTimeRange:[currentDateTimestamp,afterThirtyDaysTimestamp]
})
const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getBenefitList(queryParams.value)
}
const getBenefitList = (params: IOrderTransListParams) => {
    benefiListLoading.value = true
    orderService.orderServiceOrderPage(params).then(res => {
        benefiList.value = res.data
        totalNum.value = res.total
    }).finally(() => {
        benefiListLoading.value = false
    })
}

watch(() => props.visible, async (val) => {
    if (val) {
        queryParams.value.tenantId = props.tenantId
        queryParams.value.serviceKey = props.serviceKey
        getBenefitList(queryParams.value)
    }
})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>