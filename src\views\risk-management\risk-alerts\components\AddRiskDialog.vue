<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'

import type { FormInstance, FormRules } from 'element-plus'
import type { ICompanyInfo } from '@/types/company'

import aicService from '@/service/aicService'
import { ElMessage, ElMessageBox } from 'element-plus'
import crmService from '@/service/crmService'

const props = defineProps<{
    visible: boolean
}>()
const dialogVisible = ref(props.visible)
watch(props, (newVal) => {
    dialogVisible.value = newVal.visible
})
type FormType = {
    companyName: string,
    socialCreditCode: string,
}
const form = reactive<FormType>({
    companyName: '',
    socialCreditCode: '',
})
const submitForm = async(formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            ElMessageBox.confirm(`新增企业将会扣除对应线索权益额度，是否确定使用?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                handleSubmit(crmService.crmRiskAddRisk as ActionType, form)
            })
        } else {
            console.log('form表单效验不通过', fields)
        }
    })   
}
const emit = defineEmits(['refreshData','update:visible'])
type ActionType = (form: FormType) => Promise<boolean>
const handleSubmit = (action: ActionType, form: FormType) => {
    return action(form).then(() => {
        ElMessage({
            type: 'success',
            message: '新增成功'
        })
        handleClose()
        emit('refreshData')
    }).catch(() => {
        handleClose()
    })
}
const handleClose = () => {
    form.companyName = ''
    form.socialCreditCode = ''
    emit('update:visible',false)
}

const rules = reactive<FormRules>({
    companyName: [{ required: true, message: '请输入企业名称', trigger: 'change' }],
    socialCreditCode: [{ required: true, message: '请输入企业税号', trigger: 'change' }],
})

const formRef = ref<FormInstance>()

const companySearchLoading = ref(false)
const companyList = ref<ICompanyInfo[]>([])
const searchCompany = (query: string) => {
    if (query) {
        companySearchLoading.value = true
        setTimeout(async () => {
            let getCompanyByCodeRes = await aicService.searchEnterprise({ keyword: query, scope: '0' })
            if (getCompanyByCodeRes.success === true) {
                companyList.value = getCompanyByCodeRes.data
                companySearchLoading.value = false
            } else {
                ElMessage({
                    type: 'error',
                    message: getCompanyByCodeRes.errMsg
                })
                companySearchLoading.value = false
            }
            
        }, 200)
    } else {
        companyList.value = []
    }
}
const compamyChange = (name: string) => {
    let chooseItem = companyList.value.find((item) => {
        return item.name === name
    })
    if (chooseItem) {
        form.socialCreditCode = chooseItem.socialCreditCode
    }
}
</script>
<template>
    <el-dialog 
        v-model="dialogVisible"
        title="新增企业"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()">
        <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
            <el-form-item label="企业名称" prop="companyName">
                <el-select v-model.trim="form.companyName" filterable remote reserve-keyword placeholder="请输入企业名称"
                           :remote-method="searchCompany" :loading="companySearchLoading" @change="compamyChange">
                    <el-option v-for="item in companyList" :key="item.socialCreditCode" :label="item.name"
                               :value="item.name" />
                </el-select>
            </el-form-item>
            <el-form-item label="企业税号" prop="socialCreditCode">
                <el-input placeholder="请输入企业税号" v-model="form.socialCreditCode"/>
            </el-form-item>
            <div class="font-size-14 color-three-grey b-margin-24">提示：新增时将扣除一次线索权益(每家企业仅在首次新增时扣减一次线索权益，后续重复新增不再扣减)。</div>

            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
