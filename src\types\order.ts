import type { IAllRecord } from '@/types/record'
import type { ContactItem } from '@/types/company'
import type { ICommonResponse,IPaginationResponse } from './axios'

export interface IServiceOrderPageParams extends IAllRecord {
    page: number
    pageSize: number
    serviceKeys?: string | undefined
}
export interface IServiceOrderResponseItemService {
    app_id: string
    created_at: string
    id: number
    service_desc: string
    service_id: number
    service_name: string
    service_type: number
    should_masking: number
    status: number
    unit: string
    unit_quantity: number
    unit_type: number
    updated_at: string
}
export interface IOrderServiceStatisticsParams extends IAllRecord {
    serviceKeys: string
    tenantId?: string
}

export interface IOrderServiceStatisticsResItem {
    label: string;
    num: number; 
}

export interface IOrderBuyLegalResponse extends ICommonResponse {
    contacts: ContactItem[]
    contactNum: number
}

export interface IOrderParams extends IAllRecord {
    serviceKey: string
    socialCreditCode: string
    companyName: string
    orderId?: number | string
}

export interface IOrderCheckEntBuyResponse {
    status: string
    totalBalance: string
    optionId: string
}

export interface IOrderCheckEntBuyResponseArr {
    data: [
        {
            optionId: string
            status: string
            expireTime: string
        }
    ]
}

export interface IOrderCheckEntBuyParams extends IAllRecord {
    socialCreditCode: string
}

export interface IOrderUsageRecordParams extends IAllRecord {
    page: number
    pageSize: number
    companyName?: number
    operatorId?: number
    serviceKey?: string
    socialCreditCode?: string
    usageTimeEnd?: string
    usageTimeStart?: string
    usageType?: string
    createTimes?: string[]
}
export interface IOrderUsageRecordResponseItem {
    companyName: string
    consumeAmount: number
    operatorName: string
    serviceKey: number
    socialCreditCode: string
    tenantName: string
    usageTime: string
    usageType: number
}
export interface IOrderUsageRecordResponse {
    data: IOrderUsageRecordResponseItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}

export interface IOrderTransListParams extends IAllRecord {
    endCreateTime?: number
    endEndTime?: number
    serviceKey?: string
    startCreateTime?: number
    startEndTime?: number
    status?:string
    tenantId?: string
    sortBy?: string
    page: number
    pageSize: number
}

export interface IServiceOrderResponseItem {
    id:number
    transId: string
    createTime: number,
    endTime: number,
    goodsId: string,
    remainingAmount: number,
    serviceKey: string,
    serviceName:string
    startTime: number,
    status: string,
    tenantId: string,
    tenantInfo: {
        name: string
    },
    totalAmount: number
    serviceOrderId: string,
}

export interface IServiceOrderResponse extends IPaginationResponse {
    data: IServiceOrderResponseItem[] 
}

export interface IOrderLegalStatisticParams extends IAllRecord {
    page: number
    pageSize: number
    tenantId?: string
}

export interface IOrderLegalStatisticResponse extends IPaginationResponse {
    data: IOrderLegalStatisticResponseItem[]
}

export interface IOrderLegalStatisticResponseItem {
    createTime:number
    dueSoon:number
    expireAmount:number
    id:string
    latestOrderExpireTime:number
    remainingAmount:number
    serviceKey:string
    tenentId:string
    tenantName:string
    totalAmount:number
    updateTime:number
    usedAmount:number
}

export interface IOrderAllocateEquitiesCustomerParams extends IAllRecord {
  quantity: number,
  serviceKey: string,
  subordinateTenantId: string,
  transId: string
}

export interface IOrderGoodsListParams extends IAllRecord {
    page: number
    pageSize: number
}
export interface IOrderGoodsListResponse extends IPaginationResponse {
    data:IOrderGoodsListResponseItem[]
}

export interface IOrderGoodsListResponseItem {
    createTime:number,//创建时间
    description:string,//商品描述
    goodsId:string,//商品ID
    id:string,
    name:string,//商品名称
    serviceKey:string,//服务key
    specType:string,//规格类型,BY_AMOUNT按次数，BY_TIME按时间
    amountSpec:{//按次数
        unitCount:number,//单位数量
        unitName:string,//单位名称
    }
    timeSpec:{//按时间
        duration:number,//时长
        unit:string
    }
    updateTime:number,//更新时间
}

export interface IOrderAllocateRquitiesParams extends IAllRecord {
    goodsId:string
    quantity:number
    tenantId:string
}

export interface IOrderGoodsListParams extends IAllRecord {
    page: number
    pageSize: number
}
export interface IOrderGoodsListResponse extends IPaginationResponse {
    data:IOrderGoodsListResponseItem[]
}

export interface IOrderGoodsListResponseItem {
    createTime:number,//创建时间
    description:string,//商品描述
    goodsId:string,//商品ID
    id:string,
    name:string,//商品名称
    serviceKey:string,//服务key
    specType:string,//规格类型,BY_AMOUNT按次数，BY_TIME按时间
    amountSpec:{//按次数
        unitCount:number,//单位数量
        unitName:string,//单位名称
    }
    timeSpec:{//按时间
        duration:number,//时长
        unit:string
    }
    updateTime:number,//更新时间
}

export interface IOrderAllocateRquitiesParams extends IAllRecord {
    goodsId:string
    quantity:number
    tenantId:string
}

export interface IOrderInviteRecordListItem {
    id: string,
    accountId: string,
    orgId: string,
    createTime: number,
    relationUser: string,
    relationUserMobile: string,
    relationUserName: string,
    tenantId: string,
    tenantName: string,
    username: string,
    amount: number,
}

export interface IOrderInviteRecordList {
    data: IOrderInviteRecordListItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}

export interface IOrderInviteRecordParams {
    page: number,
    pageSize: number,
    relationUserMobile?: string,
    createTimeRange?: string[],
    relationUser?: string,
    userName?: string,
    userId?: string,
    type?: number,
    tenantId?: string,
    tenantName?: string,
}