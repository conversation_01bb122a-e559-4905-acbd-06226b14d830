import type { INormalFilterParams, Region } from '@/types/aic'

// 格式化已选地区标签
export const formatRegionLabel = (region: Region) => {
    if (!region.parent) {
        return region.label
    }

    if (region.parent.parent) {
        // 区县：显示 城市-区县
        return `${region.parent.label}-${region.label}`
    } else {
        // 城市：显示 省份-城市
        return `${region.parent.label}-${region.label}`
    }
}

export const formatSelectedLabel = (params: INormalFilterParams) => {
    const { label } = params || {}
    return `${label || ''}`
}
