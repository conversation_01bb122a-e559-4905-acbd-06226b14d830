import createPersistedState from 'vuex-persistedstate'
import { createStore, Store } from 'vuex'
import type { RootState } from '@/types/store.js'
import auth, { getAuthDefaultState } from './modules/auth.ts'
import user, { getUserDefaultState } from './modules/user.ts'
import app, { getAppDefaultState } from './modules/app.ts'
import enterprise, { getEnterpriseDefaultState } from './modules/enterprise.ts'
import menu, { getMenuDefaultState } from './modules/menu.ts'

const store = createStore({
    modules: {
        auth,
        user,
        app,
        enterprise,
        menu,
    },
    plugins: [
        createPersistedState({
            storage: sessionStorage,
        }),
    ],
})

const _store: Store<RootState> = store

const getDefaultState = () => {
    return {
        auth: { ...getAuthDefaultState() },
        user: { ...getUserDefaultState() },
        app: { ...getAppDefaultState() },
        enterprise: { ...getEnterpriseDefaultState() },
        menu: { ...getMenuDefaultState() },
    }
}

export default store
export { _store, getDefaultState }
