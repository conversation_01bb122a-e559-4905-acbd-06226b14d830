<script lang='ts' setup>
import { onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import type { PieChartOption } from '@/types/echart'
const props = defineProps<{
    pieOption?: PieChartOption // 使用自定义类型
}>()

// 监听图表数据变化
watch(
    () => props.pieOption,
    () => {
        initChart()
    },
    { deep: true }
)

const pieChart = ref(null)
const initChart = () => {
    const myChart = echarts.init(pieChart.value)
    myChart.setOption({
        // 提示框配置（鼠标悬停时显示数据详情）
        tooltip: props.pieOption?.tooltip,

        // 标题配置（显示在底部中间）
        title: props.pieOption?.title,

        // 图例配置（垂直显示在右侧中间）
        legend: props.pieOption?.legend,

        // 数据系列配置（核心部分）
        series: props.pieOption?.series,
    })
}
onMounted(() => {
    initChart()
})
</script>
<template>
    <div ref="pieChart" class="height-100 width-100"></div>
</template>
<style scoped lang='scss'>
</style>
