<script lang="ts" setup>
import { ref, defineProps, onBeforeMount } from 'vue'

const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    },
})

const dialogVisible = ref(false)

const handleOpenDetail = () => {
    dialogVisible.value = true
}

onBeforeMount(() => {
    console.log('props', props.row)
})

</script>

<template>
    <span class="pointer !color-blue" @click="handleOpenDetail()">查看详情</span>
    <el-dialog v-model="dialogVisible" title="法院公告详情" append-to-body style="height: 600px; overflow-y: auto">
        <div class="font-14 color-two-grey">
            <div class="b-margin-24">
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >关联案号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.caseNum || '-'}}</el-col>

                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >公告类型
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.noticeType || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >公告人</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        row?.court || '-' }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >公告日期
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        row?.publishDate || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >开庭日期</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row?.courtDate || '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >开庭地址
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row?.address || '-' 
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >起诉方</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row.accuserInfo || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >被诉方</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row.accusedInfo || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >第三人</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{  row?.thirdPartyInfo || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >公告内容</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{  row.content|| '-' }}
                    </el-col>
                </el-row>
            </div>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
