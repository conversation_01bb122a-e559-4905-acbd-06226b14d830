<script lang="tsx" setup>
import { ref, onMounted, watch, computed, onBeforeUnmount } from 'vue'
import Icon from '@/components/common/Icon.vue'
import systemService from '@/service/systemService'
import type { IMessageItem, ISystemMessageItem } from '@/types/message'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import MessageDialog from '@/views/usercenter/message/components/MessageDialog.vue'
import { formatDate2Period } from '@/utils/format'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import notice from '@/js/notice-api.js'
import eventBus from '@/utils/eventBus'

const store = useStore<RootState>()
const userId = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { id } = user || {}
    return id
})
watch(
    () => userId.value,
    (newVal) => {
        console.log('hahah')
        if (newVal) {
            notice.createConnect(newVal)
            window.wsClient.on('message', (topic, message) => {
                console.log('收到消息', message.toString())
                console.log('topic', topic)
                initData()
            })
        }
    },
    {
        immediate: true,
    }
)
const router = useRouter()
const activeName = ref('business')
const totalNum = ref(0)
const poolList = ref([
    {
        name: '通知',
        value: 'business',
        icon: 'icon-a-huaban168',
        count: 0,
    },
    {
        name: '公告',
        value: 'system',
        icon: 'icon-a-huaban41',
        count: 0,
    },
])
const msgList = ref<IMessageItem[]>([])
const noticeList = ref<ISystemMessageItem[]>([])
const mixList = ref<ISystemMessageItem[] | IMessageItem[]>([])
const popoverVisible = ref(false)
const popoverRef = ref()
const getClickTarget = (event: MouseEvent) => {
    console.log('获取焦点', popoverRef.value)
    if (!popoverRef.value) return
    console.log('获取焦点2')

    const popoverContentElement = popoverRef.value.popperRef.contentRef //获取popover内容区域DOM
    const popoverTriggerElement = popoverRef.value.popperRef.triggerRef //获取popover触发区域DOM
    if (
        !popoverContentElement.contains(event.target as Node) &&
        !popoverTriggerElement.contains(event.target as Node)
    ) {
        popoverVisible.value = false
    }
}
watch(
    () => popoverVisible.value,
    (newVal) => {
        if (newVal) {
            document.addEventListener('click', getClickTarget)
        } else {
            document.removeEventListener('click', getClickTarget)
        }
    }
)
const initData = () => {
    systemService.messageUnreadCount().then((res) => {
        if (res.errCode === 0) {
            const { data } = res
            const { msgCount, noticeCount, totalCount } = data || {}
            poolList.value[0].count = msgCount
            poolList.value[1].count = noticeCount
            totalNum.value = totalCount
        }
    })
}
const handleSearchMessage = async () => {
    let requestParams = {
        page: 1,
        pageSize: 1000,
        isRead: false,
    }
    try {
        const [msgRes, noticeRes] = await Promise.all([
            systemService.messagePage(requestParams),
            systemService.messageNoticePage(requestParams),
        ])
        msgList.value = msgRes.data || []
        noticeList.value = noticeRes.data || []
    } catch (error) {
        console.error(error)
    }
}
watch(
    [() => activeName.value, () => msgList.value, () => noticeList.value],
    ([newActiveName, newMsgList, newNoticeList]) => {
        mixList.value = newActiveName === 'business' ? newMsgList : newNoticeList
    },
    { immediate: true }
)

onMounted(() => {
    initData()
    eventBus.$on('refreshMessageCount', () => {
        initData()
    })
})

onBeforeUnmount(() => {
    eventBus.$off('refreshMessageCount', () => {})
})

// 打开popover
const handlerShowPpopover = () => {
    initData()
    popoverVisible.value = !popoverVisible.value
}
// 查看更多
const toRouter = (name: string, query?: Record<string, string>) => {
    router.push({
        name,
        query,
    })
}
// 清空所有消息
const handleClearAllMessage = () => {
    if (totalNum.value > 0) {
        if (activeName.value === 'business') {
            systemService.messageMarkAllMsgRead().then(() => {
                ElMessage.success('已成功清空通知')
                initData()
                handleSearchMessage()
            })
        } else {
            systemService.messageMarkAllNoticeRead().then(() => {
                ElMessage.success('已成功清空公告')
                initData()
                handleSearchMessage()
            })
        }
    }
}
const messageDialog = ref()
// 查看详情
const handleOpenMessageDetail = (row: ISystemMessageItem | IMessageItem) => {
    if (activeName.value === 'business') {
        const { jumpUrl } = row as IMessageItem
        if (!jumpUrl) return
        const urlPart = jumpUrl.split('?')
        const queryStr = urlPart[1] || ''
        const queryParams = queryStr.split('&').reduce((acc: { [key: string]: string }, param) => {
            const [key, value] = param.split('=')
            acc[key] = value
            return acc
        }, {})
        // console.log('urlPart[0]', urlPart[0], 'queryParams', queryParams)
        handleTurnRead([row.msgId])
        router.push({
            path: urlPart[0],
            query: queryParams,
        })
    } else {
        messageDialog.value?.showDialog(row)
    }
    popoverVisible.value = false
}
const handleTurnRead = async (val: number[]) => {
    if (activeName.value === 'business') {
        // 业务通知
        await systemService.messageMarkMsgRead({
            msgId: val,
        })
        initData()
        handleSearchMessage()
    } else {
        // 系统公告
        await systemService.messageMarkNoticeRead({
            msgId: val,
        })
        initData()
        handleSearchMessage()
    }
}
</script>

<template>
    <el-popover
        ref="popoverRef"
        :visible="popoverVisible"
        placement="bottom"
        trigger="click"
        popper-style="overflow:hidden; width: 480px; height:480px"
        @show="handleSearchMessage"
        :popper-options="{ modifiers: [{ name: 'offset', options: { offset: [-50, 10] } }] }"
    >
        <template #reference>
            <div class="flex top-bottom-center gap-4 pointer" @click="handlerShowPpopover">
                <div class="border-radius-16 flex-center message">
                    <Icon icon="icon-a-huaban42" size="19" />
                    <div v-show="totalNum > 0" class="alert"></div>
                </div>
                <div class="font-header-label color-two-grey">消息</div>
            </div>
        </template>
        <div class="relative padding-top-10 notice-main-box height-100">
            <div class="display-flex top-bottom-center absolute pointer" style="right: 12px; top: 8px; z-index: 1">
                <div @click="handleClearAllMessage">
                    <Icon
                        icon="icon-a-huaban851"
                        class="r-margin-4"
                        :class="totalNum > 0 ? 'color-black' : 'color-two-grey'"
                    />
                </div>
                <div class="l-margin-20 pointer" @click="toRouter('notification', { tab: activeName })">
                    <Icon icon="icon-a-huaban831"></Icon>
                </div>
            </div>
            <el-tabs v-model="activeName" class="padding-left-right-16 height-100">
                <el-tab-pane
                    v-for="tab in poolList"
                    :key="tab.value"
                    :label="tab.name"
                    :name="tab.value"
                    style="height: 100%"
                >
                    <template #label>
                        <div class="font-16" style="font-weight: 400">
                            <Icon
                                :icon="tab.icon"
                                size="16"
                                :color="activeName === tab.value ? '#1966FF' : '#666666'"
                            />
                            <span class="l-margin-8">
                                {{ tab.name
                                }}{{ tab.count * 1 > 0 ? (tab.count * 1 > 99 ? '(99+)' : '(' + tab.count + ')') : '' }}
                            </span>
                        </div>
                    </template>
                    <div class="message-tab-content">
                        <el-scrollbar>
                            <div v-if="mixList.length > 0">
                                <div
                                    v-for="item in mixList"
                                    :key="item.msgId"
                                    class="tb-padding-7 r-padding-7 font-16 color-black border-bottom"
                                    @click="handleOpenMessageDetail(item)"
                                >
                                    <div class="display-flex space-between top-bottom-center font-16 pointer">
                                        <div class="flex-1 display-flex top-bottom-center color-black">
                                            <div
                                                class="w-24 h-24 text-center"
                                                style="background-color: #1966ff; border-radius: 4px"
                                            >
                                                <Icon :icon="tab.icon" size="16" color="#fff" />
                                            </div>
                                            <div class="l-margin-12 flex-1 ellipsis-2-lines" style="max-height: 45px">
                                                {{ activeName === 'business' ? item.content : item.title }}
                                            </div>
                                        </div>
                                        <div class="color-three-grey l-margin-64 w-44">
                                            {{ formatDate2Period(item.createTime) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="display-flex flex-column top-bottom-center">
                                <img class="w-320 h-233" src="@/assets/images/no-message-data.png" alt="暂无消息" />
                                <div class="font-first-title-unactive color-two-grey">暂无公告消息</div>
                            </div>
                        </el-scrollbar>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </el-popover>
    <MessageDialog ref="messageDialog" @refresh="initData()" />
</template>

<style lang="scss" scoped>
.message {
    position: relative;
}
.alert {
    position: absolute;
    background-color: #e04c3f;
    width: 8px;
    height: 8px;
    border-radius: 8px;
    right: 2px;
    top: 1px;
    border: 1px solid rgba(255, 255, 255, 1);
}
.active {
    background-color: var(--main-blue-);
}
.message-tab-content {
    height: 100%;
    max-height: 600px;
    margin-bottom: 16px;
    overflow-y: auto;
}
.border-top {
    border-top: 1px solid var(--border-color);
}
.border-bottom {
    border-bottom: 1px solid var(--border-color);
}
.no-data {
    background: url('@/assets/images/no-message-data.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: center;
}
.ellipsis-2-lines {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 限制显示的行数 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
}
</style>
