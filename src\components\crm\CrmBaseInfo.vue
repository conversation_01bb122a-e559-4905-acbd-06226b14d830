<template>
    <div class="crm-base-info">
        <div class="display-flex gap-16 flex-wrap model-box tb-padding-10">
            <div class="back-tag-bg--hover border back-color-white all-padding-8 border-radius-4 font-12 pointer"
                 v-for="item in infoList" :key="item.value" @click="jumpHref(item.value)">
                {{ item.label }}
            </div>
        </div>
    </div>
    <div class="model-box">
        <div v-for="item in infoList" :key="item.value" :id="'model-' + item.value" class="b-margin-16 relative">
            <div class="model-title b-margin-17 relative">{{ item.label }}</div>
            <component :is="item.componentName" class="b-margin-16"> </component>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, inject } from 'vue'
import type { Ref, DefineComponent } from 'vue'
import type { ILeadData } from '@/types/lead'

// import CrmBaseInfoBase from './crm-base-info/CrmBaseInfoBase.vue'
import CrmBaseInfoContact from './crm-base-info/CrmBaseInfoContact.vue'
import CrmBaseInfoReport from './crm-base-info/CrmBaseInfoReport.vue'
import CrmBaseInfoOther from './crm-base-info/CrmBaseInfoOther.vue'
import CrmBaseInfoSystem from './crm-base-info/CrmBaseInfoSystem.vue'
import CrmBaseInfoCustomContact from './crm-base-info/CrmBaseInfoCustomContact.vue'
import CrmBaseInfoLead from './crm-base-info/CrmBaseInfoLead.vue'

const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

const infoList: Ref<{ label: string; value: string; componentName: DefineComponent }[]> = ref([
    {
        label: '相关报告',
        value: 'report',
        componentName: CrmBaseInfoReport as DefineComponent,
    },
    // {
    //     label: '基本信息',
    //     value: 'base',
    //     componentName: CrmBaseInfoBase as DefineComponent,
    // },
    {
        label: '联系方式',
        value: 'contact',
        componentName: CrmBaseInfoContact as DefineComponent,
    },
    {
        label: '其他信息',
        value: 'other',
        componentName: CrmBaseInfoOther as DefineComponent,
    },
    {
        label: '系统信息',
        value: 'system',
        componentName: CrmBaseInfoSystem as DefineComponent,
    },
    {
        label: '添加相关联系人',
        value: 'customContact ',
        componentName: CrmBaseInfoCustomContact as DefineComponent,
    },
    {
        label: '线索',
        value: 'lead',
        componentName: CrmBaseInfoLead as DefineComponent,
    },
])

onMounted(() => {
    if (crmDetail.value.isCustomer !== '1') {
        infoList.value.splice(-1, 1)
    }
})

const jumpHref = (name: string) => {
    location.hash = `#model-${name}`
}
</script>

<style lang='scss' scoped>
.model-title {
    // border-left: 4px solid var(--main-blue-);
    padding-left: 8px;
}

.model-title::before {
    content: '';
    border: 2px solid var(--main-blue-);
    height: 100%;
    position: absolute;
    left: 0;
    border-radius: 418px;
}
</style>