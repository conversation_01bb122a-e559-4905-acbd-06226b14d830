<script lang="ts" setup>
import type { IAicNormalSearchRules, INormalFilterParams, Region } from '@/types/aic'
import { computed, inject, ref, watch } from 'vue'
import RegionSelector from './RegionSelector.vue'
import { formatRegionLabel } from '@/utils/enterprise/region'
import type { IPushToGlobal } from '@/types/company'

interface Option {
    value: string
    label: string
}
const props = defineProps<{
    regions: Region[]
    storeParams: INormalFilterParams[]
    data: IAicNormalSearchRules
}>()

const pushToGlobal = inject<(v: IPushToGlobal) => void>('pushToGlobal', () => {})
const selectedProvinceRef = ref('')
const selectedCityRef = ref('')
const selectedDistrictRef = ref('')
const currentProvinceRef = ref<Region | null>(null)
const currentCityRef = ref<Region | null>(null)
const currentDistrictRef = ref<Region | null>(null)
const regionSelectorRef = ref<{
    reset: () => void
    getSelectedRegions: () => Region[]
    setSelectedRegions: (v: Region[]) => void
    setExpandedCitiesRegions: (city: Region, province: Region) => void
}>()
const selectedRegions = ref<Region[]>([])
const isDealPluginData = ref(false)

const province = computed(() => {
    return convertFirstLevelRegionsToOptions(props.regions)
})

const cityOptions = ref<Option[]>([])

const districtOptions = ref<Option[]>([])

/**
 * 获取拼音首字母（大写）
 */
function getFirstLetter(pinyin: string): string {
    return pinyin.charAt(0).toUpperCase()
}

function sortRegionsByPinyin(regions: Region[]): Region[] {
    return [...regions].sort((a, b) => {
        const letterA = getFirstLetter(a.pinyin)
        const letterB = getFirstLetter(b.pinyin)
        return letterA.localeCompare(letterB)
    })
}

const convertFirstLevelRegionsToOptions = (regions: Region[]): Option[] => {
    const sortedRegions = sortRegionsByPinyin(regions)
    return sortedRegions.map((region) => ({
        value: region.value,
        label: region.label,
    }))
}

const provinceChange = (value: string) => {
    const province = props.regions.find((e) => e.value === value)
    if (!province?.children) return

    currentProvinceRef.value = province

    cityOptions.value = convertFirstLevelRegionsToOptions(province.children)

    clearBeyongProvince()
    regionSelectorRef.value?.setSelectedRegions([province])

    if (province && !isDealPluginData.value) {
        const params = {
            ...province,
        }
        pushParams([params])
    }
}

const cityChange = (value: string) => {
    if (!currentProvinceRef.value || !currentProvinceRef.value.children) return

    const city = currentProvinceRef.value.children.find((e) => e.value === value)
    if (!city?.children) return

    currentCityRef.value = city

    districtOptions.value = convertFirstLevelRegionsToOptions(city.children)
    clearBeyondCity()

    // 插件设置
    regionSelectorRef.value?.setSelectedRegions([city])
    regionSelectorRef.value?.setExpandedCitiesRegions(city, currentProvinceRef.value)

    if (city && !isDealPluginData.value) {
        const params = {
            ...city,
            parent: currentProvinceRef.value,
        }
        pushParams([params])
    }
}

const districtChange = (value: string) => {
    if (!currentProvinceRef.value || !currentProvinceRef.value.children) return
    if (!currentCityRef.value || !currentCityRef.value.children) return

    const district = currentCityRef.value.children.find((e) => e.value === value)

    if (district && !isDealPluginData.value) {
        const params = {
            ...district,
            parent: currentCityRef.value,
        }
        pushParams([params])
        currentDistrictRef.value = district
        regionSelectorRef.value?.setSelectedRegions([district])
    }
}

const clearBeyongProvince = (clearOptions?: boolean) => {
    selectedCityRef.value = ''
    selectedDistrictRef.value = ''

    if (clearOptions) {
        cityOptions.value = []
        districtOptions.value = []
    }
}

const clearBeyondCity = (clearOptions?: boolean) => {
    selectedDistrictRef.value = ''
    if (clearOptions) {
        districtOptions.value = []
    }
}

const clearAll = () => {
    selectedProvinceRef.value = ''
    clearBeyongProvince(true)
}

const showMutipleSelector = () => {
    dialogVisible.value = true
}

const pushParams = (regions: Region[]) => {
    const tempArray: INormalFilterParams[] = []
    regions.forEach((region) => {
        const params: INormalFilterParams = {
            label: formatRegionLabel(region),
            value: region.value,
            category: props.data.name,
            categoryKey: props.data.key,
            type: props.data.dataType,
        }

        tempArray.push(params)
    })

    console.log('tempArray', tempArray)

    if (!pushToGlobal) return
    pushToGlobal(tempArray)
}

const resetParams = () => {
    if (!pushToGlobal) return
    pushToGlobal({ categoryKey: props.data.key })
}

const regionReset = (v: 'district' | 'city' | 'province') => {
    if (v === 'district' && currentProvinceRef.value && currentCityRef.value) {
        const params: Region = {
            ...currentCityRef.value,
            parent: currentProvinceRef.value,
        }
        pushParams([params])
    }
    if (v === 'city' && currentProvinceRef.value) {
        const params: Region = {
            ...currentProvinceRef.value,
        }
        pushParams([params])
    }
    if (v === 'province') {
        resetParams()
    }
}

const dialogVisible = ref(false)

const handleClose = () => {
    dialogVisible.value = false
}

const reset = () => {
    selectedProvinceRef.value = ''
    selectedCityRef.value = ''
    selectedDistrictRef.value = ''
    regionSelectorRef.value?.reset()
}

const resetRegionSelector = () => {
    regionSelectorRef.value?.reset()
}

const getRegionSelectorData = () => {
    if (!regionSelectorRef.value) return
    const regions = regionSelectorRef.value.getSelectedRegions()
    selectedRegions.value = regions
    dialogVisible.value = false
    if (regions && regions.length > 0) {
        pushParams(regions)
    } else {
        resetParams()
    }

    if (regions.length === 1) {
        isDealPluginData.value = true
        const { value } = regions[0]
        // 按两位长度分割value， 第一个是省，第二个是市 第三个是县区
        if (value.length === 2) {
            selectedProvinceRef.value = value
        } else if (value.length === 4) {
            const city = value
            const province = value.slice(0, 2)
            selectedProvinceRef.value = province
            selectedCityRef.value = city
        } else if (value.length === 6) {
            const district = value
            const city = value.slice(0, 4)
            const province = value.slice(0, 2)
            console.log(district)
            console.log(city)
            console.log(province)
            selectedProvinceRef.value = province
            setTimeout(() => {
                selectedCityRef.value = city
            }, 100)
            setTimeout(() => {
                selectedDistrictRef.value = district
            }, 100)
        }
        setTimeout(() => {
            isDealPluginData.value = false
        }, 300)
    } else {
        selectedProvinceRef.value = ''
        selectedCityRef.value = ''
        selectedDistrictRef.value = ''
    }
}

const onProvinceChange = (v: string) => {
    if (!v) {
        regionReset('province')
    }
}

const onCityChange = (v: string) => {
    if (!v) {
        regionReset('city')
    }
}

const onDistrictChange = (v: string) => {
    if (!v) {
        regionReset('district')
    }
}

const isHaschanged = computed(() => {
    const target = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    const checked = selectedRegions.value
    return target.length < checked.length
})

const uncheck = () => {
    const target = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    const targetIds = target.map((e) => e.value)
    const filtered = selectedRegions.value.filter((item) => targetIds.includes(item.value))
    selectedRegions.value = filtered
    regionSelectorRef.value?.setSelectedRegions(filtered)
    console.log('uncheck', filtered)
}

watch(
    () => isHaschanged.value,
    (value) => {
        if (value) {
            uncheck()
        }
    }
)

// 省变化
watch(
    () => selectedProvinceRef.value,
    (value) => {
        if (!value) {
            clearAll()
        } else {
            provinceChange(value)
        }
    }
)

// 城市变化
watch(
    () => selectedCityRef.value,
    (value) => {
        if (!value) {
            clearBeyondCity(true)
        } else {
            cityChange(value)
        }
    }
)

// 区县变化
watch(
    () => selectedDistrictRef.value,
    (value) => {
        if (value) {
            districtChange(value)
        }
    }
)

watch(
    () => regionSelectorRef.value,
    () => {
        if (currentDistrictRef.value && currentCityRef.value && currentProvinceRef.value) {
            regionSelectorRef.value?.setSelectedRegions([currentDistrictRef.value])
            regionSelectorRef.value?.setExpandedCitiesRegions(currentCityRef.value, currentProvinceRef.value)
        } else if (currentCityRef.value && currentProvinceRef.value) {
            regionSelectorRef.value?.setSelectedRegions([currentCityRef.value])
            regionSelectorRef.value?.setExpandedCitiesRegions(currentCityRef.value, currentProvinceRef.value)
        } else if (currentProvinceRef.value) {
            regionSelectorRef.value?.setSelectedRegions([currentProvinceRef.value])
        }
    }
)

defineExpose({ reset })
</script>

<template>
    <div class="region-cascade-select flex flex-row left-right-center gap-16 pointer no-select top-bottom-center">
        <el-select
            v-model="selectedProvinceRef"
            placeholder="省份"
            style="width: 128px"
            clearable
            no-data-text="无数据"
            class="cust-select"
            @change="onProvinceChange"
        >
            <el-option v-for="item in province" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select
            v-model="selectedCityRef"
            placeholder="城市"
            style="width: 128px"
            clearable
            no-data-text="无数据"
            @change="onCityChange"
        >
            <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select
            v-model="selectedDistrictRef"
            placeholder="区县"
            style="width: 128px"
            clearable
            no-data-text="无数据"
            @change="onDistrictChange"
        >
            <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button class="h-32" @click="showMutipleSelector">多选</el-button>
        <div class="font-14 color-blue" v-if="selectedRegions.length">已选{{ selectedRegions.length }}个地址</div>
        <el-dialog v-model="dialogVisible" title="地区多选" width="740" :before-close="handleClose" tetx>
            <RegionSelector ref="regionSelectorRef" :regions="regions" />
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="resetRegionSelector">清空</el-button>
                    <el-button type="primary" @click="getRegionSelectorData"> 保存 </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
.region-cascade-select :deep(.el-select__wrapper) {
    min-height: 26px;
    font-size: 12px;
}
</style>
