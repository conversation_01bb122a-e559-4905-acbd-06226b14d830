<script setup lang="ts">
import CompanyDetailDrawer from '@/components/search/company-detail-drawer/company-detail-drawer.vue'
import TransferCrmDialog from '@/components/transfer-crm-dialog/TransferCrmDialog.vue'
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
import type { ICompanyInfo, IGetCompanyByCodeParams } from '@/types/company'
import type { RootState } from '@/types/store'
import { formatFilters } from '@/utils/enterprise/filters'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'
import permissionService from '@/service/permissionService'

const props = defineProps<{
    keyword: string
    externalParams: Record<string, string | number>
}>()
const multipleSelection = ref<ICompanyInfo[]>([])
const companyDetailDrawerVisible = ref(false)
const store = useStore<RootState>()
const list = ref<ICompanyInfo[]>([])
const loading = ref(false)
const searchchannellype = ref(1)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    realTotal: 0,
})
const requestParams = ref<IGetCompanyByCodeParams>({
    keyword: props.keyword,
})
const tableRef = ref()

const filterParams = ref<{ [x: string]: string | string[] }>({})

const socialCreditCode = ref('')
const transferCrmDialogVisible = ref(false)

const toDetail = (row: ICompanyInfo) => {
    if (row.socialCreditCode) {
        socialCreditCode.value = row.socialCreditCode
        companyDetailDrawerVisible.value = true
    } else {
        ElMessage.error('该企业暂无详情')
    }
}

const getData = () => {
    const { scope, keyword } = requestParams.value || {}

    if (scope && scope !== '0' && keyword === '') {
        ElMessage.warning('搜索范围需要配合关键词搜索，请输入关键词')
        return
    }

    // if (loading.value) return
    loading.value = true
    aicService
        .searchLastEnt({
            ...requestParams.value,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            ...filterParams.value,
        })
        .then((res) => {
            loading.value = false
            const { errCode, data, total, channelType, realTotal } = res
            if (errCode === 0) {
                list.value = data
                pageInfo.value.total = total
                pageInfo.value.realTotal = realTotal
                searchchannellype.value = channelType
            } else {
                pageInfo.value.total = 0
                pageInfo.value.realTotal = 0
                list.value = []
            }
        })
        .catch((error) => {
            const { isCanceled } = error
            if (!isCanceled) {
                loading.value = false
                pageInfo.value.total = 0
                pageInfo.value.realTotal = 0
                list.value = []
            }
        })
}

const searchParams = computed(() => {
    const { lastEntFilterParams } = store.state.enterprise

    return lastEntFilterParams
})

onMounted(() => {
    // getData()
})

watch(
    () => searchParams,
    (value) => {
        const formattedData = formatFilters(value.value)
        filterParams.value = formattedData
        getData()
    },
    { deep: true }
)

watch(
    () => props.keyword,
    (value) => {
        requestParams.value = { ...requestParams.value, keyword: value }
        // getData()
    }
)

watch(
    () => props.externalParams,
    (value) => {
        requestParams.value = { ...requestParams.value, ...value }
        getData()
    }
)

const handleSelectionChange = (val: ICompanyInfo[]) => {
    multipleSelection.value = val
}

const transferSelected = () => {
    if (multipleSelection.value.length === 0) {
        ElMessage.warning('请至少选择一条数据')
        return
    }
    transferCrmDialogVisible.value = true
}

const selectable = (row: ICompanyInfo) => {
    return row.socialCreditCode
}

const getPhone = (row: ICompanyInfo) => {
    ElMessageBox.confirm('查看联系方式将会扣除对应线索权益额度，是否确定查看？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        console.log('确认查看' + row.companyName)
        orderService
            .orderBuyLegal({
                serviceKey: 'xs',
                socialCreditCode: row.socialCreditCode,
                companyName: row.companyName,
            })
            .then((res) => {
                const { contacts } = res
                row.isBuy = true
                if (contacts && Array.isArray(contacts) && contacts.length > 0) {
                    const { content } = contacts[0] || {}
                    row.contact = content
                }
                ElMessage.success('已成功获取企业号码')
            })
    })
}

const transferCrmSuccess = () => {
    if (tableRef.value) {
        tableRef.value.clearSelection()
    }
}

const tagList = (row: ICompanyInfo) => {
    const tags = []
    for (let index = 0; index < row.companyTags.length; index++) {
        const element = row.companyTags[index]
        if (element.categoryCode === '001') {
            tags.push({
                label: element.tagName,
                code: element.categoryCode,
                color: '#52C41A',
            })
        }
        if (element.categoryCode === '002') {
            tags.push({
                label: element.tagName,
                code: element.categoryCode,
                color: '#6947CA',
            })
        }
        if (element.categoryCode === '007') {
            const nums = tags.filter((e) => e.code === '007')
            if (nums.length < 3) {
                tags.push({
                    label: element.tagName,
                    code: element.categoryCode,
                    color: '#F0933C',
                })
            }
        }
    }
    return tags
}
</script>

<template>
    <div class="flex flex-column height-100 recent-search-table">
        <div class="flex flex-1 flex-column gap-16" style="overflow: hidden">
            <div class="flex flex-row gap-16 top-bottom-center">
                <div class="flex flex-row gap-8 top-bottom-center">
                    <div class="font-14">
                        共
                        <span class="color-blue lr-padding-2 font-weight-600">
                            {{ pageInfo.realTotal }}
                        </span>
                        结果
                    </div>
                    <div class="w-1 back-color-border h-12"></div>
                    <div class="font-14">
                        已选<span class="color-blue lr-padding-2 font-weight-600">{{ multipleSelection.length }}</span
                        >个
                    </div>
                </div>
                <div class="flex flex-row gap-16">
                    <el-dropdown v-if="permissionService.isTransferNewLeadPermitted()" placement="bottom-start">
                        <el-button>
                            转CRM
                            <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="transferSelected">转移所选</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>

            <el-table
                ref="tableRef"
                :data="list"
                style="width: 100%"
                v-loading="loading"
                @selection-change="handleSelectionChange"
                :default-expand-all="true"
                height="100%"
            >
                <el-table-column type="selection" :selectable="selectable" width="55" />
                <el-table-column label="联系方式" width="130" v-if="searchchannellype === 1">
                    <template #default="props">
                        <div v-if="props.row.isBuy">
                            <span v-if="props.row.hasmobile === '1'">{{ props.row.contact }}</span>
                            <span v-else>暂无联系方式</span>
                        </div>
                        <template v-else>
                            <div
                                v-if="props.row.socialCreditCode && props.row.hasmobile === '1'"
                                class="color-blue pointer"
                                @click="getPhone(props.row)"
                            >
                                获取号码
                            </div>
                            <div v-else>暂无联系方式</div>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column label="企业信息">
                    <template #default="props">
                        <div class="flex flex-column gap-12">
                            <div @click="toDetail(props.row)">
                                <div class="pointer font-18 color-black color-blue--hover font-weight-500">
                                    {{ props.row.companyName }}
                                </div>
                            </div>
                            <div class="flex flex-row gap-16">
                                <div
                                    v-for="(tag, index) in tagList(props.row)"
                                    :key="index"
                                    class="flex top-bottom-center h-29 border-radius-6 lr-padding-8"
                                    :style="{
                                        borderWidth: '1px',
                                        borderStyle: 'solid',
                                        borderColor: tag.color,
                                    }"
                                >
                                    <div :style="{ color: tag.color }">
                                        {{ tag.label }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-row top-bottom-center font-14 lh-14">
                                <div class="color-black">
                                    {{ props.row.legalperson || '-'}} | {{ props.row.esdate || '-' }} | {{ props.row.regCapDisplay || '-' }}
                                </div>
                            </div>
                            <div class="text-ellipsis text-nowrap color-three-grey font-14 lh-14">
                                经营范围：{{ props.row.opscope || '-' }}
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="成立时长" width="120">
                    <template #default="props">
                        <div class="flex top-bottom-center">
                            <div>
                                {{
                                    props.row.esTimeToNow || props.row.esTimeToNow === 0
                                        ? props.row.esTimeToNow + '天'
                                        : '-'
                                }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-affix position="bottom" :offset="0">
            <div class="display-flex top-bottom-center all-padding-18 justify-flex-end back-color-white">
                <el-pagination
                    :hide-on-single-page="true"
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="getData"
                    @current-change="getData"
                />
            </div>
        </el-affix>
        <CompanyDetailDrawer
            v-model:drawer="companyDetailDrawerVisible"
            v-if="companyDetailDrawerVisible"
            :socialCreditCode="socialCreditCode"
            @refreshList="getData"
        />
        <TransferCrmDialog
            v-model:visible="transferCrmDialogVisible"
            :selected="multipleSelection"
            :success="transferCrmSuccess"
        />
    </div>
</template>

<style scoped>
.recent-search-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
