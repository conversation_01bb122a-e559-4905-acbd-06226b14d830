import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig(({ mode }) => {
    // 读取 .env 文件
    const env = loadEnv(mode, process.cwd(), 'VITE_')
    const timestamp = Date.now()
    process.env.VITE_BUILD_TIMESTAMP = timestamp.toString()
    console.log('env', env)
    return {
        define: {
            __APP_ENV__: env.VITE_APP_TITLE, // 可以在代码中用 __APP_ENV__ 访问
        },
        plugins: [
            vue(),
            AutoImport({
                resolvers: [ElementPlusResolver()],
            }),
            Components({
                resolvers: [ElementPlusResolver()],
            }),
        ],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src'),
                '~c': path.resolve(__dirname, './src/components'),
            },
        },
        server: {
            // // 配置https代理与证书
            // open: false, // 是否自动在浏览器打开
            port: 8888, // 端口号
            host: '0.0.0.0',
            // 这里的ip和端口是前端项目的;下面为需要跨域访问后端项目
            proxy: {
                '/api': {
                    target: env.VITE_APP_PROXY,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, ''),
                },
                '/yunying-api': {
                    target: env.VITE_APP_YUNYING_PROXY,
                    changeOrigin: true, // 开启跨域
                    rewrite: (path) => path.replace(/^\/yunying-api/, '/api'),
                    // ↑ 把 /yunying-api 替换成 /api
                },
            },
            allowedHosts: true,
        },
    }
})
