<script lang="ts" setup>
import type { IAutoDialerListItem } from '@/types/autoDialer'
import { ref } from 'vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
defineProps<{
    taskInfo?: IAutoDialerListItem
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const drawerRef = ref(true)

// ====================== Methods ======================

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <el-drawer v-model="drawerRef" title="外呼任务详情" size="720">
        <span>Hi there!</span>
    </el-drawer>
</template>

<style lang="scss" scoped></style>
