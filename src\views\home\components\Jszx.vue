<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import Icon from '@/components/common/Icon.vue'
import type { ModelRes } from '@/types/home'
import { computed } from 'vue'

const props = defineProps<{
    data: ModelRes[]
}>()

const transformedData = computed(() => {
    return props.data.map((item) => {
        const key = Object.keys(item)[0] // 获取对象的键名（例如 "企业技术中心"）
        return {
            ...item[key], // 扩展原有的属性
            title: key, // 添加新的 title 属性，值为原对象的键名
        }
    })
})

const technologyCenter = [
    {
        name: '企业技术中心',
        value: '331',
        icon: 'icon-a-huaban19',
    },
    {
        name: '省级工程技术研究中心企业',
        value: '331',
        icon: 'icon-a-huaban18',
    },
    {
        name: '国家级工程技术研究中心企业',
        value: '331',
        icon: 'icon-a-huaban3',
    },
]
</script>
<template>
    <div class="jszx">
        <div style="margin-bottom: 24px">
            <ModuleTitle title="技术中心"></ModuleTitle>
        </div>
        <ul class="display-flex space-between">
            <li class="company-item" v-for="(item, index) in technologyCenter" :key="index">
                <div class="display-flex b-margin-16">
                    <div class="company-item-icon text-center">
                        <Icon :icon="item.icon" color="#fff"></Icon>
                    </div>
                    <div class="company-item-num">
                        {{ transformedData[index] ? Number(transformedData[index].indexValue) : '-' }}
                    </div>
                </div>
                <div class="company-item-label">{{ item.name }}</div>
            </li>
        </ul>
    </div>
</template>
<style scoped lang="scss">
ul {
    padding: 0;
    margin: 0;
    li {
        list-style-type: none;
        padding-left: 0;
    }
}
.jszx {
    box-sizing: border-box;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    .company-item {
        box-sizing: border-box;
        // min-width: 222px;
        width: 30%;
        padding: 16px;
        border-radius: 4px;
        background:
            linear-gradient(0deg, rgba(248, 249, 253, 1), rgba(248, 249, 253, 1)),
            linear-gradient(90deg, rgb(238, 241, 249) 0%, rgb(229, 232, 247) 100%);
        .company-item-icon {
            width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 24px;
            background-color: var(--main-blue-);
            margin-right: 8px;
        }
        .company-item-num {
            font-size: 18px;
            font-weight: 700;
            color: var(--main-black);
        }
        .company-item-label {
            font-size: 14px;
            font-weight: 400;
            color: var(--main-black);
        }
    }
}
</style>
