<template>
    <button class="Btn" @mouseover="flag = true" @mouseleave="flag = false"
            :style="{ borderColor: color, backgroundColor: !flag ? color : acColor }">
        <div class="sign">
            <Icon v-show='!flag' size="20px" :icon="svgUrl" :color="acColor" />
            <Icon v-show='flag' size="20px" :icon="acSvgUrl || svgUrl" :color="color" />
        </div>

        <div class="text" :style="{ color: color }">{{ btnTxt }}</div>
    </button>
</template>

<script lang='ts' setup>
import { ref, defineProps } from 'vue'
import type { Ref } from 'vue'
import Icon from './Icon.vue'

const flag: Ref<boolean> = ref(false)

defineProps({
    svgUrl: {
        type: String,
        default: ''
    },
    acSvgUrl: {
        type: String,
        default: ''
    },
    btnTxt: {
        type: String,
        default: ''
    },
    color: {
        type: String,
        default: ''
    },
    acColor: {
        type: String,
        default: ''
    }
}
)
</script>

<style lang='scss' scoped>
.Btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition-duration: 0.3s;
    border: 1px solid;
    margin-right: 10px;
}

/* plus sign */
.sign {
    width: 100%;
    transition-duration: 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sign svg {
    width: 17px;
}

.sign svg path {
    fill: white;
}

/* text */
.text {

    opacity: 0;
    color: white;
    font-size: 12px;
    white-space: nowrap;
}

/* hover effect on button width */
.Btn:hover {
    width: auto;
    border-radius: 6px;
    transition-duration: 0.3s;
}

.Btn:hover .sign {
    transition-duration: 0.3s;
    padding-right: 5px;
}

/* hover effect button's text */
.Btn:hover .text {
    opacity: 1;
    padding-right: 10px;
}

/* button click effect*/
.Btn:active {
    transform: translate(2px, 2px);
}
</style>