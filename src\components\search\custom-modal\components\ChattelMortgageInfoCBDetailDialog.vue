<script lang="ts" setup>
import { ref, defineProps, getCurrentInstance, computed } from 'vue'

const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    },
})

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const dialogVisible = ref(false)

const pageInfo = ref({
    page: 1,
    pageSize: 10
})

const collateralInfoData = computed(() => { 
    const data = props.row.collateralInfo || []
    const start = (pageInfo.value.page - 1) * pageInfo.value.pageSize
    const end = start + pageInfo.value.pageSize
    return data.slice(start, end)
})

const handleOpenDetail = () => {
    dialogVisible.value = true
}

</script>


<template>
    <span class="pointer !color-blue" @click="handleOpenDetail()">查看详情</span>
    <el-dialog v-model="dialogVisible" title="动产抵押详情" append-to-body style="height: 600px; overflow-y: auto">
        <div class="font-14 color-two-grey">
            <!-- 登记信息 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >登记信息</el-col
                    >
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >登记编号</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.registNo || '-'}}</el-col>

                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >状态
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{row.status || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >登记日期</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ moment(row.registDate).format("YYYY-MM-DD") || '-' }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >被担保债权数额
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.guaranteeBondDesc || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >登记机关</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.organName || '-' }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >抵押人
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.mortgagor || '-' }}</el-col>
                </el-row>
            </div>

            <!-- 被担保主债权信息 -->
            <div class="b-margin-24">
                <el-row>
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >被担保主债权信息</el-col
                    >
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >种类</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        {{
                            row.guaranteeInfo?.kind || '-'
                        }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >债务人履行债务的期限
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ moment(row.guaranteeInfo?.releaseDate).format("YYYY-MM-DD") + '-' + moment(row.guaranteeInfo?.endDate).format("YYYY-MM-DD") }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >被担保债权数额</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row.guaranteeInfo.guaranteeBondDesc || '-'
                    }}</el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >币种
                    </el-col>
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row.guaranteeInfo.regCapcurType || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >担保范围</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row.guaranteeInfo.scope || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >备注</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row.guaranteeInfo.guaranteeDesc || '-'
                    }}</el-col>
                </el-row>
            </div>
            <!-- 抵押权人信息 -->
            <div class="b-margin-24">
                <el-row justify="space-between">
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        <div>抵押权人信息（{{ row.pledgeInfo?.length || 0 }}）</div>
                    </el-col>
                </el-row>
                <el-table border :data="row.pledgeInfo">
                    <el-table-column prop="pledge" label="抵押权人名称"></el-table-column>
                    <el-table-column prop="identityType" label="证件类型"></el-table-column>
                    <el-table-column prop="address" label="住所地"></el-table-column>
                </el-table>
            </div>
            <!-- 抵押物信息 -->
            <div class="b-margin-24">
                <el-row justify="space-between">
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        <div>抵押物信息（{{ row?.collateralInfo?.length || 0 }}）</div>
                    </el-col>
                </el-row>
                <el-table border :data="collateralInfoData">
                    <el-table-column prop="objectName" label="抵押物名称"></el-table-column>
                    <el-table-column prop="owner" label="所有权或使用权归属"></el-table-column>
                    <el-table-column prop="other" label="详情"></el-table-column>
                    <el-table-column prop="desc" label="备注"></el-table-column>
                </el-table>
                <el-pagination
                    class="flex justify-end"
                    v-model:current-page="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10]"
                    layout="total, prev, pager, next"
                    :total="row?.collateralInfo.length"
                />
            </div>
            <!-- todo -->
            <!-- <div class="b-margin-24">
                <el-row justify="space-between">
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        <div>变更信息（{{ 0 }}）</div>
                    </el-col>
                </el-row>
                <el-table border >
                    <el-table-column prop="keyword" label="关键词"></el-table-column>
                    <el-table-column prop="rank" label="排名"></el-table-column>
                    <el-table-column prop="platform" label="平台"></el-table-column>
                    <el-table-column prop="type" label="类型"></el-table-column>
                </el-table>
            </div>
            <div class="b-margin-24">
                <el-row justify="space-between">
                    <el-col :span="24" class="all-padding-16" style="border: 1px solid var(--border-color)">
                        <div>注销信息（{{ 0 }}）</div>
                    </el-col>
                </el-row>
                <el-table border >
                    <el-table-column prop="keyword" label="关键词"></el-table-column>
                    <el-table-column prop="rank" label="排名"></el-table-column>
                    <el-table-column prop="platform" label="平台"></el-table-column>
                    <el-table-column prop="type" label="类型"></el-table-column>
                </el-table>
            </div> -->
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
