<script lang="ts" setup>
import type { RootState } from '@/types/store'
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore<RootState>()

const roleName = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { roleName } = user || {}

    return roleName || []
})
</script>

<template>
    <div class="flex flex-row flex-wrap gap-8 b-margin-12">
        <div class="default-tag" v-for="(role, index) in roleName" :key="index">{{ role }}</div>
    </div>
</template>

<style lang="scss" scoped></style>
