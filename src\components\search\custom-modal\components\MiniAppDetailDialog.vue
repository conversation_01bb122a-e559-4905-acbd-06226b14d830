<script lang="ts" setup>
import { ref, defineProps, onBeforeMount } from 'vue'

const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    },
})

const dialogVisible = ref(false)

const handleOpenDetail = () => {
    dialogVisible.value = true
}


onBeforeMount(() => {
    console.log('props', props.row)
})

</script>

<template>
    <span class="pointer !color-blue" @click="handleOpenDetail()">详情</span>
    <el-dialog v-model="dialogVisible" title="小程序信息详情" append-to-body style="min-height: 400px; overflow-y: auto">
        <div class="font-14 color-two-grey">
            <div class="b-margin-24">
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >小程序名称</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ row.name || '-'}}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >小程序介绍</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{ 
                        row?.description || '-' }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >分类</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)">{{
                        row?.classification || '-'
                    }}</el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >评分</el-col
                    >
                    <el-col :span="18" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row.score || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >发布时间</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row.releaseDate || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >最新更新时间</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{ row.latestUpdate || '-' }}
                    </el-col>
                </el-row>
                <el-row>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >发布平台</el-col
                    >
                    <el-col :span="6" class="all-padding-16" style="border: 1px solid var(--border-color)"
                    >{{  row.comeFrom || '-' }}
                    </el-col>
                    <el-col
                        :span="6"
                        class="all-padding-16"
                        style="background-color: #f9f9f9; border: 1px solid var(--border-color)"
                    >开发者</el-col
                    >
                    <el-col :span="6" class="all-padding-16"  style="border: 1px solid var(--border-color)">
                        {{ row.developer || '-' }}
                    </el-col>
                </el-row>
            </div>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
