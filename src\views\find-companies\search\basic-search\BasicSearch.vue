<script setup lang="ts">
import { onMounted, ref } from 'vue'
import SearchInputGroup from './components/SearchInputGroup.vue'
import commonData from '@/js/common-data'
import SearchFilter from './components/SearchFilter.vue'
import SearchTable from './components/SearchTable.vue'
import { useRoute } from 'vue-router'

const scopeRef = ref(commonData.searchTagTypes[0].val)
const keyword = ref('')
const externalParams = ref<Record<string, string>>({})
const route = useRoute()
const { params } = route

onMounted(() => {
    const { keyword: _keyword, tagTypeVal } = params
    let temp = { ...externalParams.value }
    if (typeof tagTypeVal === 'string' && tagTypeVal) {
        scopeRef.value = tagTypeVal
        temp = {
            ...temp,
            scope: tagTypeVal,
        }
    }
    if (typeof _keyword === 'string' && _keyword) {
        keyword.value = _keyword
        temp = {
            ...temp,
            keyword: _keyword,
        }
    }
    if (Object.keys(temp).length) {
        externalParams.value = temp
    }
})

const onMatchTypechange = (v: string, keyword: string) => {
    externalParams.value = {
        ...externalParams.value,
        matchType: v,
        keyword: keyword,
    }
}

const onSearchKeyword = (v: string) => {
    const params = {
        ...externalParams.value,
        keyword: v,
        scope: scopeRef.value,
    }

    console.log('params', params)
    externalParams.value = params
}

const onKeywordChange = (v: string) => {
    keyword.value = v
}
</script>

<template>
    <div class="flex flex-column gap-16">
        <div class="flex flex-column back-color-white border-radius-4 all-padding-16 gap-20">
            <el-tabs v-model="scopeRef">
                <el-tab-pane v-for="tag in commonData.searchTagTypes" :label="tag.label" :name="tag.val" :key="tag.val">
                    <SearchInputGroup
                        :defaultValue="keyword"
                        :placeholder="tag.plac"
                        :onMatchTypechange="onMatchTypechange"
                        :onSearch="onSearchKeyword"
                        :onKeywordChange="onKeywordChange"
                        :matchType="externalParams.matchType"
                    />
                </el-tab-pane>
            </el-tabs>
            <SearchFilter />
        </div>
        <div class="back-color-white oh all-padding-16">
            <SearchTable :keyword="keyword" :scope="scopeRef" :externalParams="externalParams" />
        </div>
    </div>
</template>

<style scoped></style>
