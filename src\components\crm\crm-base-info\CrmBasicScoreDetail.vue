<template>
    <el-dialog v-model="showDialog" width="850px" @close="closeCreditScoreDetail" title="企业基础信用得分对比详情">
        <div class="all-padding-16">
            <div class="display-flex top-bottom-center tb-padding-13" style="backgroundColor: #F5FAFE">
                <div class="l-padding-10" style="width: 24%">评分大类</div>
                <div class="display-flex top-bottom-center" style="width: 76%">
                    <div style="width: 43%">评分子类</div>
                    <div style="width: 23%" class="text-center">子类总分</div>
                    <div style="width: 23%" class="text-center">当前得分</div>
                </div>
            </div>
            <div class="display-flex top-bottom-center"
                 :style="{ backgroundColor: index % 2 == 0 ? '#fff' : '#F5FAFE' }" v-for="(item, index) in contentData"
                 :key="index">
                <div class="l-padding-10" style="width: 24%">{{ item.label }}</div>
                <div style="width: 76%">
                    <div class="display-flex tb-padding-10" v-for="(child, i) in item.children" :key="i">
                        <div style="width: 44%">{{ child.label }}</div>
                        <div style="width: 23%" class="text-center display-flex">
                            <div style="width: 50%; padding-left: 0.24rem">{{ child.totalScore }}</div>
                            <div style="width: 20%">分</div>
                        </div>
                        <div style="width: 23%" class="text-center display-flex">
                            <div style="width: 50%; padding-left: 0.24rem">
                                <div v-if="child.negativeFullScore">
                                    {{ child.value < child.negativeFullScore ? child.negativeFullScore : child.value }}
                                </div>
                                <div v-else>{{ child.value }}</div>
                            </div>
                            <div style="width: 20%">分</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="display-flex top-bottom-center main-color t-padding-16">
                <div class="l-padding-10" style="width: 80%">总分</div>
                <div class="display-flex">
                    <div class="r-padding-20">{{ totalScore || '-' }}</div>
                    <div>分</div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, onMounted, watch, inject, defineEmits } from 'vue'
import type { Ref } from 'vue'
import type { ILeadData } from '@/types/lead'

const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>
import indicatorService from '@/service/indicatorService'

const showDialog: Ref<boolean> = ref(false)


const props = defineProps({

    showCreditScoreDetail: {
        type: Boolean,
        default: false
    },
    totalScore: {
        type: Number,
        default: 0
    }
})

const emits = defineEmits(['update:showCreditScoreDetail'])


const contentData = ref([
    {
        label: '信用评价（30%）',
        value: '',
        children: [
            {
                label: '纳税信用等级 (50%)',
                value: 0,
                totalScore: 15
            },
            {
                label: '企业合规风险 (50%)',
                value: 0,
                negativeFullScore: -15, //最小负分
                totalScore: 15
            },
            {
                label: '减分项: 税收违法事件',
                value: 0,
                totalScore: '- ( )'
            }
        ]
    },
    {
        label: '创新能力评价（20%）',
        value: '',
        children: [
            {
                label: '一类知识产权授权数量 (40%)',
                value: 0,
                totalScore: 8
            },
            {
                label: '已申请二类知识产权总数 (20%)',
                value: 0,
                totalScore: 4
            },
            {
                label: '近2年累计已申请的知识产权数量 (40%)',
                value: 0,
                totalScore: 8
            }
        ]
    },
    {
        label: '发展能力评价（30%）',
        value: '',
        children: [
            {
                label: '经营时长 (60%)',
                value: 0,
                totalScore: 18
            },
            {
                label: '资本实缴比例 (40%)',
                value: 0,
                totalScore: 12
            },
            {
                label: '附加加分项: 融资信息',
                value: 0,
                totalScore: '+ ( )'
            }
        ]
    },
    {
        label: '稳定性评价（20%）',
        value: '',
        children: [
            {
                label: '股权集中度 (50%)',
                value: 0,
                totalScore: 10
            },
            {
                label: '股权结构稳定性 (50%)',
                value: 0,
                totalScore: 10
            }
        ]
    }
])
const getdCreditScoreDetail = () => {
    indicatorService.getIndicatorResult({
        socialCreditCode: crmDetail.value.socialCreditCode,
        tableName: 't_indicator_result',
        type: '企业基本信息评分(基于公开数据)',
        pageNum: 1,
        pageSize: 20
    }).then(res => {
        console.log(res)
        let list = res.records
        if (list.length) {
            contentData.value.forEach(item => {
                item.children.forEach(child => {
                    let r = list.find(li => {
                        return li.indicator === child.label
                    })
                    if (r) {
                        child.value = Number(r.value)
                    } else {
                        child.value = 0
                    }


                })
            })
        }

    })
}

watch(() => props.showCreditScoreDetail, (value) => {
    showDialog.value = value
    getdCreditScoreDetail()
}, { immediate: true })


const closeCreditScoreDetail = () => {
    emits('update:showCreditScoreDetail', false)
}


onMounted(() => {
})

</script>

<style lang='scss' scoped></style>