<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { NOTIFICATION_LIST_TABLE_COLUMNS } from '@/js/table-options'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()

import systemService from '@/service/systemService'
import type { TableInstance } from 'element-plus'
import type { IMessagePageParams, IMessageItem, ISystemMessageItem } from '@/types/message'
import { formatDate2Period } from '@/utils/format'
import MessageDialog from '@/views/usercenter/message/components/MessageDialog.vue'
import eventBus from '@/utils/eventBus'

const poolList = [
    {
        name: '业务通知',
        value: 'business'
    },
    {
        name: '系统公告',
        value:'system'
    }
]
const activeName = ref<string>('business')
let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})
let queryParams = reactive<IMessagePageParams>({
    page: 1,
    pageSize: 20,
})
const tableLoading = ref(false)
const checkIds = ref<number[]>([])

const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const elTabContentRef = ref<HTMLDivElement | null>(null)
const tableListRef = ref<TableInstance>()
const tableAllOptions = ref(NOTIFICATION_LIST_TABLE_COLUMNS.filter(item=>item.key !== 'msgType'))
const selectedData = ref<IMessageItem[] | ISystemMessageItem[]>([])
const tableData=ref<IMessageItem[] | ISystemMessageItem[]>([])
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && elTabContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - elTabContentRef.value.clientHeight - 32 - 16 - 16 - 16 -16 - 16
    }
}
const search = async () => {
    tableLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    // 业务通知
    if (activeName.value === 'business') {
        let messageRes = await systemService.messagePage(queryParams)
        tableLoading.value=false
        const { errCode, data, total } = messageRes
        if (errCode === 0) {
            tableData.value = data || []
            pageInfo.total = total
        }
    } else {
        // 系统公告
        let messageRes = await systemService.messageNoticePage(queryParams)
        tableLoading.value=false
        const { errCode, data, total } = messageRes
        if (errCode === 0) {
            tableData.value = data || []
            pageInfo.total = total
        }
    }
}
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    search()
}
const handleTabChange = () => {
    clearAllSelected()
    pageInfo.page = 1
    pageInfo.pageSize = 20
    pageInfo.total = 0
    if (activeName.value === 'business') {
        tableAllOptions.value = tableAllOptions.value.filter(item => item.key !== 'msgType')
    } else {
        tableAllOptions.value = NOTIFICATION_LIST_TABLE_COLUMNS
    }
    search()
}
const handleSelectionChange = (val: IMessageItem[] | ISystemMessageItem[]) => {
    if (val.length < 1001) {
        selectedData.value = val
        checkIds.value = val.map((i) => {
            return i.msgId
        })
    } else {
        let newRow = val.slice(1000)
        for (let index = 0; index < newRow.length; index++) {
            tableListRef.value!.toggleRowSelection(newRow[index], false)
        }
    }
}
const handleTurnRead = async(val:string | number[], hiddenTips?: boolean) => {
    if (val === 'all') {
        if (activeName.value === 'business') { 
            systemService.messageMarkAllRead().then(() => {
                if (!hiddenTips) {
                    ElMessage.success('已全部标记为已读')
                }
                clearAllSelected()
            })
        } else {
            systemService.messageMarkAllNoticeRead().then(() => {
                if (!hiddenTips) {
                    ElMessage.success('已全部标记为已读')
                }
                clearAllSelected()
            })
        }
    } else {
        if (val.length > 0) {
            if (activeName.value === 'business') {
                // 业务通知
                let res = await systemService.messageMarkMsgRead({
                    msgId: val as number[]
                })
                if (res.errCode === 0) {
                    if (!hiddenTips) {
                        ElMessage.success('已标记为已读')
                    }
                    clearAllSelected()
                } else {
                    ElMessage.error(res.msg)
                }
            } else {
                // 系统公告
                let res = await systemService.messageMarkNoticeRead({
                    msgId: val as number[]
                })
                if (res.errCode === 0) {
                    if (!hiddenTips) {
                        ElMessage.success('已标记为已读')
                    }
                    clearAllSelected()
                } else {
                    ElMessage.error(res.msg)
                }

            }
        } else {
            return ElMessage({
                type: 'warning',
                message: '请选择消息',
            })
        }
        
    }
    search()
    eventBus.$emit('refreshMessageCount')
}
const handleSee = (row: IMessageItem) => {
    const { jumpUrl } = row
    console.log('jumpUrl', jumpUrl)
    if (jumpUrl) {
        const urlPart = jumpUrl.split('?')
        const queryStr = urlPart[1] || ''
        const queryParams = queryStr.split('&').reduce((acc:{[key: string]: string}, param) => {
            const [key, value] = param.split('=')
            acc[key] = value
            return acc
        }, {})
        router.push({
            path: urlPart[0],
            query: queryParams
        })
    }
    handleTurnRead([row.msgId], true)
}
const messageDialog = ref()
const handleOpenMessageDetail = (row: ISystemMessageItem) => {
    messageDialog.value?.showDialog(row)
}
const handleDel = (val: number[]) => {
    if (val.length > 0) {
        ElMessageBox.confirm('是否确认删除', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(async() => {
            let jsonVal = val.join(',')
            if (activeName.value === 'business') {
                let delRes = await systemService.messageRemove({ ids: jsonVal })
                if (delRes.errCode === 0) {
                    ElMessage({
                        type: 'success',
                        message: '删除成功',
                    })
                    clearAllSelected()
                    search()
                }
            } else {
                let delRes = await systemService.messageRemoveNotice({ ids: jsonVal })
                if (delRes.errCode === 0) {
                    ElMessage({
                        type: 'success',
                        message: '删除成功',
                    })
                    clearAllSelected()
                    search()
                }
            }
            
        })
    } else {
        ElMessage({
            type: 'warning',
            message: '请选择需要删除的消息',
        })
    }
}
// 清空用户的选择
const clearAllSelected = () => {
    if (tableListRef.value) {
        tableListRef.value.clearSelection()
    }
}
onMounted(() => {
    const { query } = route
    const { tab } = query || {}
    if (tab) {
        activeName.value = tab as string
    }
    search()
    getTableHeight()
})
</script>

<template>
    <div ref="mainContentRef" class="height-100 oa">
        <div class="b-margin-16 all-padding-16 " style="background-color: #fff;">
            <div ref="elTabContentRef">
                <el-tabs v-model="activeName" @tab-change="handleTabChange()">
                    <el-tab-pane v-for="tab in poolList" :key="tab.value" :label="tab.name" :name="tab.value">
                        <!-- 工具条 -->
                        <div class="display-flex top-bottom-center space-between action-bar">
                            <div class="display-flex top-bottom-center">
                                <div class="choose-content">已选{{ selectedData.length }}</div>
                                <div class="l-margin-16">
                                    <el-button @click="handleTurnRead(checkIds)">标记为已读</el-button>
                                </div>
                                <div class="l-margin-16">
                                    <el-button @click="handleTurnRead('all')">全部标记为已读</el-button>
                                </div>
                                <div class="l-margin-16">
                                    <el-button @click="handleDel(checkIds)">删除</el-button>
                                </div>
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <!-- table -->
            <el-table
                ref="tableListRef"
                :data="tableData"
                v-loading="tableLoading"
                row-key="msgId"
                @selection-change="handleSelectionChange"
                :style="{ 'min-height': tableHeight + 'px' }"
            >
                <el-table-column type="selection" width="55" fixed="left" reserve-selection />
                <el-table-column
                    v-for="columns in tableAllOptions.filter((item) => item.isShow === true)"
                    :key="columns.key"
                    :prop="columns.prop"
                    :label="columns.label"
                    :width="columns.width"
                    :type="columns.type"
                    :fixed="columns.fixed"
                    :sortable="columns.sortable"
                    show-overflow-tooltip
                >
                    <template #default="scope">
                        <!-- 业务通知 -->
                        <template v-if="activeName === 'business'">
                            <div v-if="columns.prop === 'createTime'" :class="scope.row.isRead ? 'color-three-grey' : ''">
                                {{formatDate2Period(scope.row.createTime)  }}
                            </div>
                            <div v-else-if="columns.prop === 'action'" class="display-flex top-bottom-center space-between">
                                <a style="text-decoration: none" class="el-dropdown-link" @click="handleSee(scope.row)">查看</a>
                                <a class="el-dropdown-link" style="text-decoration: none" @click="handleDel([scope.row.msgId])">删除</a>
                            </div>
                            <span v-else :class="scope.row.isRead ? 'color-three-grey' : ''">{{ scope.row[columns.prop] }}</span>
                        </template>
                        <!-- 系统公告 -->
                        <template v-if="activeName === 'system'">
                            <div v-if="columns.prop === 'content'" :class="scope.row.isRead ? 'color-three-grey' : ''">
                                {{ scope.row.title }}
                            </div>
                            <div v-else-if="columns.prop === 'action'" class="display-flex top-bottom-center space-between">
                                <a class="el-dropdown-link" style="text-decoration: none"  @click="handleOpenMessageDetail(scope.row)">查看</a>
                                <a class="el-dropdown-link" style="text-decoration: none" @click="handleDel([scope.row.msgId])">删除</a>
                            </div>
                            <span v-else :class="scope.row.isRead ? 'color-three-grey' : ''">{{ scope.row[columns.prop] }}</span>
                        </template>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <el-affix position="bottom" :offset="16">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
    </div>
    <MessageDialog ref="messageDialog" @refresh="search()" />
</template>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.action-bar {
    color: #303133;
    margin-bottom: 16px;
    .choose-content {
        font-size: 14px;
        color: #a6a6a6;
        margin-right: 16px;
    }
}
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
.el-dropdown-link {
    display: block;
    cursor: pointer;
    color: var(--main-blue-);
    font-size: 16px;
}
</style>
