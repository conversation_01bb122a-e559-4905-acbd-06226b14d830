import type { MenuTreeState, RootState } from '@/types/store'
import { type Module } from 'vuex'
import type { IMenuResponse } from '@/types/menu'
import systemService from '@/service/systemService'
export const getMenuDefaultState = (): MenuTreeState => ({
    menuTree: [],
})

const menuTreeModule: Module<MenuTreeState, RootState> = {
    namespaced: true, // 启用命名空间
    state: () => getMenuDefaultState(),
    getters: {
        menuTree: (state: MenuTreeState) => state.menuTree,
    },
    mutations: {
        SET_MENU_TREE(state: MenuTreeState, menuTree: IMenuResponse[]) {
            state.menuTree = menuTree
        },
    },
    actions: {
        setMenuTree({ commit }, menuTree: IMenuResponse[]) {
            commit('SET_MENU_TREE', menuTree)
        },
        async loadMenuTreeIfNeeded({ state, commit }) {
            if (state.menuTree.length > 0) {
                return state.menuTree
            }
            const result = await systemService.menuLoadTree()
            const { errCode, data } = result || {}
            if (errCode === 0) {
                commit('SET_MENU_TREE', data)
                return data
            }
            return []
        },
    },
}

export default menuTreeModule
