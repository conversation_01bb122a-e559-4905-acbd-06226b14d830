<script lang="ts" setup>
import { ref, onMounted, reactive, watch } from 'vue'
import highRiskPng from '@/assets/images/high-risk.png'
import middletRiskPng from '@/assets/images/middle-risk.png'
import lowRiskPng from '@/assets/images/low-risk.png'
import crmService from '@/service/crmService'
import { parseTime } from '@/utils/parse-time'
import type { IRiskListItem, ICrmRiskRiskListParams } from '@/types/lead'
import { useRouter } from 'vue-router'
const router = useRouter()
const activeName = ref('high')
const tabList = [
    {
        label: '高风险',
        value: 'high',
        src: highRiskPng,
    },
    {
        label: '中风险',
        value: 'middle',
        src: middletRiskPng,
    },
    {
        label: '低风险',
        value: 'low',
        src: lowRiskPng,
    },
]
let queryParams = reactive<ICrmRiskRiskListParams>({
    page: 1,
    pageSize: 5,
    riskLevel: 100,
})
let pageInfo = reactive({
    page: 1,
    pageSize: 5,
    total: 0,
})
const listLoading = ref(true)
const listData = ref<IRiskListItem[]>([])
const getData = async () => {
    listLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    try {
        const res = await crmService.crmRiskRiskList(queryParams)
        const { errCode, data, total } = res
        if (errCode === 0) {
            listData.value = data
            pageInfo.total = total
        } else {
            console.log('获取风险列表失败', res)
        }
        listLoading.value = false
    } catch (error) {
        listLoading.value = false
        console.log('error', error)
    }
}
onMounted(() => {
    getData()
})
watch(
    () => activeName.value,
    (newVal) => {
        if (newVal === 'high') {
            queryParams.riskLevel = 100
            queryParams.page = 1
            queryParams.pageSize = 5
        } else if (newVal === 'middle') {
            queryParams.riskLevel = 50
            queryParams.page = 1
            queryParams.pageSize = 5
        } else {
            queryParams.riskLevel = 1
            queryParams.page = 1
            queryParams.pageSize = 5
        }
        getData()
    }
)
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    getData()
}
const openCompanyDetail = (item:IRiskListItem) => {
    if (window.self === window.top) {
        const routeUrl = router.resolve({ name: 'company-profile', params: { socialCreditCode: item.socialCreditCode } }).href
        window.open(routeUrl, '_blank')
    } else {
        router.push({
            name: 'company-profile',
            params: {
                socialCreditCode: item.socialCreditCode,
            },
        })
    }
}
</script>
<template>
    <div class="width-100 height-100 back-color-white border-radius-8 all-padding-16 display-flex flex-column">
        <div class="font-16 color-black font-weight-500 b-margin-8">风险事件分析</div>
        <div class="flex-1">
            <el-tabs stretch v-model="activeName" style="width: 100%; height: 100%">
                <el-tab-pane v-for="item in tabList" :key="item.value" :name="item.value">
                    <template #label>
                        <div class="display-flex center">
                            <img class="w-16 h-16" :src="item.src" :alt="item.label" />
                            <div class="font-16 l-margin-12">{{ item.label }}</div>
                        </div>
                    </template>
                    <template #default v-if="activeName === item.value">
                        <div class="h-300 width-100" v-loading="listLoading">
                            <div v-if="listData.length>0">
                                <div
                                    class="h-60 lr-padding-16 font-16"
                                    v-for="item in listData"
                                    :key="item.id"
                                >
                                    <div class="display-flex space-between top-bottom-center b-margin-4">
                                        <el-tooltip
                                            effect="light"
                                            placement="top"
                                            :content="item.companyName"
                                        >
                                            <div class="font-16 color-primary pointer width-60 text-ellipsis" @click="openCompanyDetail(item)">{{ item.companyName }}</div>
                                        </el-tooltip>
                                           
                                        <div class="font-16 color-three-grey">
                                            更新时间:{{ parseTime(item.updateTime, '{y}-{m}-{d}') || '-' }}
                                        </div>
                                    </div>
                                    <div v-if="item.riskTypeOverView.length > 0" class="color-two-grey">
                                        新增风险事件：{{ item.riskTypeOverView[0].label }}
                                        <!-- <div v-for="(bb, index) in item.riskTypeOverView" :key="index">
                                        <el-tag
                                            class="pointer "
                                            :type="riskType(bb.label)"
                                        >
                                            {{ bb.label }}×{{ bb.num }}
                                        </el-tag>
                                    </div> -->
                                    </div>
                                    <div v-else class="color-two-grey">新增风险事件：-</div>
                                </div>
                            </div>
                            <div v-else class="width-100 height-100 display-flex center">
                                <div class="w-260 h-260 no-data"></div>
                            </div>
                        </div>
                        <div class="display-flex" style="flex-direction: row-reverse">
                            <el-pagination
                                v-model:currentPage="pageInfo.page"
                                v-model:page-size="pageInfo.pageSize"
                                :total="pageInfo.total"
                                :page-sizes="[20, 40, 60, 100]"
                                layout="total, prev, pager, next"
                                @change="pageChange"
                            />
                        </div>
                    </template>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<style scoped lang="scss">
.no-data {
    background: url('@/assets/images/no-chart-data.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: center;
}
.text-ellipsis{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
