import http from '@/axios'
import type {
    IAuthCaptchaCheckResponse,
    IAuthMobileCodeRequest,
    IAuthMobileRequest,
    IAuthUpdatePasswordRequest,
    IUpdatePasswordRequest,
} from '@/types/auth'
import type { IMenuAddRequest, IMenuSysResponse, IMenuLoadPermissionTreeResponse } from '@/types/menu'
import type {
    IMenuRemoveRequest,
    IOrgAddRequest,
    IOrgAddResponse,
    IOrgEditRequest,
    IOrgEditResponse,
    IOrgRemoveRequest,
    IOrgSortRequest,
    IOrgTreeRequest,
    IOrgTreeResponse,
    IUserListItem,
    IUserListRequest,
} from '@/types/org'
import type { IRoleListRequest, IRoleListResponse } from '@/types/role'
import type {
    IPageUserListResponse,
    IUserAddRequest,
    IUserAddSendCodeRequest,
    IUserDisableRequest,
    IUserEditRequest,
    IUserDetailParams,
    IUserDetailResponse,
    IOrgGetOrgNamesResponse,
    IUserPageListRequest,
    IUserGetUserNameResponse,
    IUserGetUserOrgParams,
    IUserGetUserOrgResponse,
    IExportUserRequest,
} from '@/types/user'
import type {
    ITenantPageParams,
    ITenantPageResponse,
    IAddTenantParams,
    ITenantDisableParams,
    IEditTenantParams,
    ITenantDataSourceParams,
    ITenantListResponse,
    ITenantRequest,
    IGetAicChannelResponse,
    IGetTaxCollectResponse,
} from '@/types/tenant'
import type {
    IAddRoleItem,
    IDataScopeListItem,
    IRoleEditRoleNameParams,
    IRoleResponse,
    IDelRoleParams,
} from '@/types/role'
import type {
    IMessagePageParams,
    IMessagePageResponse,
    IMessageReadResponse,
    IMessageRemoveRequest,
    ISystemMessagePageResponse,
    IMessageUnreadCountParams,
    IMessageDetail
} from '@/types/message'
import type { 
    ITaskListRequest,
    ITaskEnums, 
    ITaskListResponse,
} from '@/types/task'
import type {
    OEMUploadItem,
    IOEMListParams,
    IOEMListResponse,
    OEMDetailParams,
    IOEMDetailResponse,
} from '@/types/OEM'
import type { 
    ISMSTemplateAddOrEditParams,
    ISMSLogPageResponse,
    IPaginationParams,
    ISMSTemplatePageResponse,
} from '@/types/sms'
import type { ICommonResponse } from '@/types/axios'
import type { AxiosResponse } from 'axios'

export default {
    menuLoadTree(): Promise<IMenuSysResponse> {
        return http.get(`/api/zhenqi-system/menu/load-tree`, {
            hideError: true,
        })
    },
    userUpdatePasswordByCode(body: IAuthUpdatePasswordRequest): Promise<IAuthUpdatePasswordRequest> {
        return http.put(`/api/zhenqi-system/user/update-password-by-code`, body, {
            unlessAuth: true,
        })
    },
    userAuthMobile(body: IAuthMobileRequest) {
        return http.put(`/api/zhenqi-system/user/auth-mobile`, body)
    },
    userSendAuthMobileCode(body: IAuthMobileCodeRequest): Promise<IAuthCaptchaCheckResponse> {
        return http.get(`/api/zhenqi-system/user/send-auth-mobile-code`, {
            params: body,
        })
    },
    orgTree(body: IOrgTreeRequest): Promise<IOrgTreeResponse> {
        return http.get(`/api/zhenqi-system/org/tree`, {
            params: body,
            hideError: true,
        })
    },
    userList(body: IUserListRequest): Promise<IUserListItem[]> {
        return http.get(`/api/zhenqi-system/user/list`, {
            params: body,
        })
    },
    orgAdd(body: IOrgAddRequest): Promise<IOrgAddResponse> {
        return http.post(`/api/zhenqi-system/org/add`, body, {
            hideError: true,
        })
    },
    orgEdit(body: IOrgEditRequest): Promise<IOrgEditResponse> {
        return http.put(`/api/zhenqi-system/org/edit`, body, {
            hideError: true,
        })
    },
    orgRemove(body: IOrgRemoveRequest) {
        return http.delete(`/api/zhenqi-system/org/remove`, {
            params: body,
            hideError: true,
        })
    },
    roleListByName(body?: IRoleListRequest): Promise<IRoleListResponse> {
        return http.get(`/api/zhenqi-system/role/list-by-name`, {
            params: body,
            hideError: true,
        })
    },
    userPage(body: IUserPageListRequest): Promise<IPageUserListResponse> {
        return http.get(`/api/zhenqi-system/user/page`, {
            params: body,
            hideError: true,
        })
    },
    tenantPage(data: ITenantPageParams): Promise<ITenantPageResponse> {
        return http.get(`/api/zhenqi-system/tenant/page`, {
            params: data,
            hideError: true,
            repeatCancel: true,
        })
    },

    // 查新全部租户信息
    tenantList(): Promise<ITenantListResponse[]> {
        return http.get(`api/zhenqi-system/tenant/list`)
    },
    // 新增租户
    tenantAdd(data: IAddTenantParams): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-system/tenant/add`, data, {
            hideError: true,
        })
    },
    // 编辑租户
    tenantEdit(body: IEditTenantParams): Promise<ICommonResponse> {
        return http.put(`/api/zhenqi-system/tenant/edit`, body, {
            hideError: true,
        })
    },
    // 禁/启用租户
    tenantDisable(body: ITenantDisableParams) {
        return http.put(`/api/zhenqi-system/tenant/disable`, body, {
            hideError: true,
        })
    },
    // 新增角色
    roleAdd(data: IAddRoleItem): Promise<IRoleResponse> {
        return http.post(`/api/zhenqi-system/role/add`, data, {
            hideError: true,
        })
    },
    // 删除角色
    roleRemove(data: IDelRoleParams): Promise<IRoleResponse> {
        return http.delete(`/api/zhenqi-system/role/remove`, {
            params: data,
            hideError: true,
        })
    },
    // 修改角色名字
    roleEditRoleName(body: IAddRoleItem): Promise<IRoleResponse> {
        return http.put(`/api/zhenqi-system/role/edit-role-name`, body, {
            hideError: true,
        })
    },
    // 修改角色
    roleEdit(body: IRoleEditRoleNameParams): Promise<IRoleResponse> {
        return http.put(`/api/zhenqi-system/role/edit`, body, {
            hideError: true,
        })
    },
    // 数据权限列表
    dataScopeList(): Promise<IDataScopeListItem[]> {
        return http.get(`/api/zhenqi-system/data/scope/list`)
    },
    // 角色权限树
    menuLoadPermissionTree(body:IDelRoleParams): Promise<IMenuLoadPermissionTreeResponse> {
        return http.get(`/api/zhenqi-system/menu/load-permission-tree`, {
            params: body,
            hideError: true
        })
    },
    // 设置财税采集通道 (采集通道配置)
    tenantSetTaxCollectType(body: ITenantDataSourceParams): Promise<IRoleResponse> {
        return http.put(`/api/zhenqi-system/tenant/set-tax-collect-type`, body, {
            hideError: true,
        })
    },
    // 获取财税采集通道 (采集通道配置)
    tenantGetTaxCollectType(body: ITenantRequest): Promise<IGetTaxCollectResponse> {
        return http.get(`/api/zhenqi-system/tenant/get-tax-collect-type`, {
            params: body,
        })
    },
    // 设置工商通道 (数据源配置)
    tenantSetAicChannel(body: ITenantDataSourceParams): Promise<IRoleResponse> {
        return http.put(`/api/zhenqi-system/tenant/set_aic_channel`, body, {
            hideError: true,
        })
    },
    // 获取工商通道 (数据源配置)
    tenantGetAicChannel(body: ITenantRequest): Promise<IGetAicChannelResponse> {
        return http.get(`/api/zhenqi-system/tenant/get_aic_channel`, {
            params: body,
        })
    },
    userAdd(body: IUserAddRequest) {
        return http.post(`/api/zhenqi-system/user/add`, body, {
            hideError: true,
        })
    },
    userEdit(body: IUserEditRequest) {
        return http.put(`/api/zhenqi-system/user/edit`, body, {
            hideError: true,
        })
    },
    userSendAddUserCode(body: IUserAddSendCodeRequest): Promise<ICommonResponse> {
        return http.get(`/api/zhenqi-system/user/send-add-user-code`, {
            params: body,
            hideError: true,
        })
    },
    userDisable(body: IUserDisableRequest): Promise<ICommonResponse> {
        return http.put(`/api/zhenqi-system/user/disable`, body)
    },
    // 查看用户详情
    userDetail(body: IUserDetailParams): Promise<IUserDetailResponse> {
        return http.get(`/api/zhenqi-system/user/detail`, {
            params: body,
            hideError: true,
        })
    },
    // 查看用户组织信息
    userGetUserOrg(body: IUserGetUserOrgParams): Promise<IUserGetUserOrgResponse> {
        return http.get(`/api/zhenqi-system/user/get-user-org`, {
            params: body,
        })
    },

    // 获取组织名称
    orgGetOrgNames(body: IUserDetailParams): Promise<IOrgGetOrgNamesResponse> {
        return http.get(`/api/zhenqi-system/org/get-org-names`, {
            params: body,
            hideError: true,
        })
    },
    userUpdatePassword(body: IUpdatePasswordRequest) {
        return http.put(`/api/zhenqi-system/user/update-password`, body, {
            hideError: true,
        })
    },
    userUpdateMinePassword(body: IUpdatePasswordRequest) {
        return http.put(`/api/zhenqi-system/user/update-mine-password`, body, {
            hideError: true,
        })
    },
    orgSort(body: IOrgSortRequest) {
        return http.put(`/api/zhenqi-system/org/sort`, body, {
            hideError: true,
        })
    },
    // 根据数据权限获取组织
    orgGetOrgByScopeData(scopeKey: string): Promise<IOrgGetOrgNamesResponse> {
        return http.get(`/api/zhenqi-system/org/get-org-by-scope-data?scopeKey=${scopeKey}`, {
            hideError: true,
        })
    },
    // 根据数据权限获取用户
    userGetUserByScopeData(scopeKey: string): Promise<IUserGetUserNameResponse> {
        return http.get(`/api/zhenqi-system/user/get-user-by-scope-data?scopeKey=${scopeKey}`, {
            hideError: true,
        })
    },
    menuAdd(body: IMenuAddRequest) {
        return http.post(`/api/zhenqi-system/menu/add`, body, {
            hideError: true,
        })
    },
    menuRemove(body: IMenuRemoveRequest) {
        return http.delete(`/api/zhenqi-system/menu/remove`, {
            params: body,
            hideError: true,
        })
    },
    menuEdit(body: IMenuAddRequest) {
        return http.put(`/api/zhenqi-system/menu/edit`, body, {
            hideError: true,
        })
    },
    menuTree(): Promise<IMenuSysResponse> {
        return http.get(`/api/zhenqi-system/menu/tree`, {
            hideError: true,
        })
    },
    userExportUser(body: IExportUserRequest): Promise<AxiosResponse<Blob>> {
        return http.post(`/api/zhenqi-system/user/export-user`, body, {
            hideError: true,
            fullRes: true,
            responseType: 'blob',
        })
    },
    // 消息管理-消息分页
    messagePage(body: IMessagePageParams): Promise<IMessagePageResponse> {
        return http.get(`/api/zhenqi-system/message/page`, {
            params: body,
            hideError: true,
        })
    },
    // 消息管理-系统消息分页
    messageNoticePage(body: IMessagePageParams): Promise<ISystemMessagePageResponse> {
        return http.get(`/api/zhenqi-system/message/notice-page`, {
            params: body,
            hideError: true,
        })
    },
    // 消息管理-消息标记已读
    messageMarkMsgRead(body: {msgId: number[]}): Promise<IMessageReadResponse> {
        return http.put(`/api/zhenqi-system/message/mark-msg-read`, body, {
            hideError: true,
        })
    },
    // 消息管理-系统消息标记已读
    messageMarkNoticeRead(body: {msgId: number[]}):Promise<IMessageReadResponse> {
        return http.put(`/api/zhenqi-system/message/mark-notice-read`, body, {
            hideError: true,
        })
    },
    // 消息管理-全部标记已读
    messageMarkAllRead():Promise<IMessageReadResponse> {
        return http.put(`/api/zhenqi-system/message/mark-all-read`)
    },
    // 消息管理-全部消息标记已读
    messageMarkAllMsgRead():Promise<IMessageReadResponse> {
        return http.put(`/api/zhenqi-system/message/mark-all-msg-read`)
    },
    // 消息管理-全部公告标记已读
    messageMarkAllNoticeRead():Promise<IMessageReadResponse> {
        return http.put(`/api/zhenqi-system/message/mark-all-notice-read`)
    },
    // 消息管理-删除消息
    messageRemove(body: IMessageRemoveRequest):Promise<IMessageReadResponse> {
        return http.delete(`/api/zhenqi-system/message/remove`, {
            params: body,
            hideError: true,
        })
    },
    // 消息管理-删除公告消息
    messageRemoveNotice(body: IMessageRemoveRequest):Promise<IMessageReadResponse> {
        return http.delete(`/api/zhenqi-system/message/remove-notice`, {
            params: body,
            hideError: true,
        })
    },
    // 消息管理-未读消息数量
    messageUnreadCount(body?: IMessageUnreadCountParams) {
        return http.get(`/api/zhenqi-system/message/unread-count`, {
            params: body,
            hideError: true,
        })
    },
    // 消息管理-消息详情
    messageDetail(body: { linkNoticeId: number }): Promise<IMessageDetail> {
        return http.get(`/api/zhenqi-system/message/notice-detail`, {
            params: body,
        })
    },
    // 任务管理
    taskEnums():Promise<ITaskEnums>{
        return http.get(`/api/zhenqi-system/task/enums`,{
            hideError: true,
        })
    },
    // 任务列表
    taskList(body: ITaskListRequest):Promise<ITaskListResponse>{
        return http.get(`/api/zhenqi-system/task/list`,{
            params: body,
            hideError: true,
        })
    },
    // OEM列表
    systemOEMList(body:IOEMListParams):Promise<IOEMListResponse>{
        return http.get(`/api/zhenqi-system/oem/list`,{
            params: body,
            hideError: true,
        })
    },
    // OEM删除
    systemOEMRemove(body:{id:string}):Promise<ICommonResponse>{
        return http.delete(`/api/zhenqi-system/oem/remove`,{
            params: body,
            hideError: true,
        })
    },
    // OEM新增
    systemOEMSave(body:OEMUploadItem):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-system/oem/save`, body, {
            hideError: true,
        })
    },
    // OEM编辑
    systemOEMEdit(body:OEMUploadItem):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-system/oem/edit`, body, {
            hideError: true,
        })
    },
    // OEM详情
    systemOEMDetail(body:OEMDetailParams):Promise<IOEMDetailResponse>{
        return http.post(`/api/zhenqi-system/oem/detail`,body,{
            hideError: true,
        })
    },
    // 短信模板列表
    smsTemplatePage(body:IPaginationParams):Promise<ISMSTemplatePageResponse>{
        return http.get(`/api/zhenqi-system/sms/template-page`,{
            params: body,
            hideError: true,
        })
    },
    // 新增短信模板
    smsTemplateAdd(body:ISMSTemplateAddOrEditParams):Promise<ICommonResponse>{
        return http.post(`/api//zhenqi-system/sms/template-add`,body,{
            hideError: true,
        })
    },
    // 编辑短信模板
    smsTemplateUpdate(body:ISMSTemplateAddOrEditParams):Promise<ICommonResponse>{
        return http.put(`/api/zhenqi-system/sms/template-update`,body)
    },
    // 删除短信模板
    smsTemplateDelete(body:{id:string}){
        return http.delete(`/api/zhenqi-system/sms/template-delete`,{
            params: body,
        })
    },
    // 短息发送记录
    smsLogPage(body:IPaginationParams):Promise<ISMSLogPageResponse>{
        return http.get(`/api/zhenqi-system/sms/sms-log-page`,{
            params: body,
            hideError: true,
        })  
    },
}
