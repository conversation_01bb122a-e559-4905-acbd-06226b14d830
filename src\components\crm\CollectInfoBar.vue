<template>
    <div class="display-flex bar-box tb-padding-8 space-around font-14">
        <div class="flex-1 text-center">
            工商司法更新时间 : <span> {{ rePortDate || '尚未采集' }}</span>
            <span class="color-blue pointer l-margin-5" @click="refresh">立即更新</span>
        </div>
        <div class="flex-1 text-center">
            票税数据:
            <span>
                {{
                    crmDetail.invoiceCollectDate && crmDetail.invoiceCollectDate > 0
                        ? moment(crmDetail.invoiceCollectDate).format('YYYY-MM-DD HH:mm:ss')
                        : '尚未授权'
                }}</span
            >
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, inject, watch, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Ref } from 'vue'
import type { ILeadData } from '@/types/lead'

import companyService from '@/service/companyService'
import crmService from '@/service/crmService'
import orderService from '@/service/orderService'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

interface companyInfo {
    socialCreditCode: string
    companyName: string
    leadId?: string
}

watch(
    crmDetail,
    (newVal) => {
        if (newVal.socialCreditCode) {
            getBasicInfo()
        }
    },
    { deep: true }
)

const rePortDate: Ref<string | null> = ref(null)

const getBasicInfo = () => {
    companyService
        .getBasicInfo({
            socialCreditCode: crmDetail.value.socialCreditCode,
        })
        .then((res) => {
            rePortDate.value = res.reportDate
        })
}

const refresh = () => {
    let companyData: companyInfo = {
        socialCreditCode: crmDetail.value.socialCreditCode,
        companyName: crmDetail.value.companyName,
        leadId: crmDetail.value.id,
    }

    orderService.orderCheckEntBuy({ ...companyData, serviceKey: 'xs' }).then((getCompanyBuyStatusInfo) => {
        if (getCompanyBuyStatusInfo.status === '1') {
            //已购买
            confirmRefresh(companyData)
        } else {
            ElMessageBox.confirm('您还未购买该企业线索，是否购买？', '提示', {
                confirmButtonText: '购买',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    orderService.orderBuyLegal({ ...companyData, serviceKey: 'xs' }).then(() => {
                        confirmRefresh(companyData)
                    })
                })
                .catch(() => {})
        }
    })
}

const confirmRefresh = (companyData: companyInfo) => {
    crmService.refresh(companyData).then(() => {
        ElMessage({
            message: '正在更新中，请稍后查看',
            type: 'success',
        })
    })
}

onMounted(() => {
    getBasicInfo()
})
</script>

<style lang="scss" scoped>
.bar-box {
    background-color: rgba(25, 102, 255, 0.06);
}
</style>
