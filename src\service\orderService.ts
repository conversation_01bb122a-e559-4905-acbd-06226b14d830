import http from '@/axios'
import type { ICommonResponse } from '@/types/axios'

import type {
    IServiceOrderResponse,
    IOrderServiceStatisticsParams,
    IOrderBuyLegalResponse,
    IOrderParams,
    IOrderCheckEntBuyResponse,
    IOrderCheckEntBuyParams,
    IOrderCheckEntBuyResponseArr,
    IOrderUsageRecordParams,
    IOrderUsageRecordResponse,
    IOrderTransListParams,
    IOrderLegalStatisticParams,
    IOrderAllocateEquitiesCustomerParams,
    IOrderLegalStatisticResponse,
    IOrderGoodsListParams,
    IOrderGoodsListResponse,
    IOrderAllocateRquitiesParams,
    IOrderServiceStatisticsResItem,
    IOrderInviteRecordParams,
    IOrderInviteRecordList,
} from '@/types/order'
import type { AxiosResponse } from 'axios'

export default {
    orderServiceStatistics(data: IOrderServiceStatisticsParams): Promise<IOrderServiceStatisticsResItem[]> {
        return http.get(`/api/zhenqi-order/order/service-statistics`, {
            params: data,
        })
    },
    // 查询权益列表
    orderServiceOrderPage(data: IOrderTransListParams): Promise<IServiceOrderResponse> {
        return http.get(`/api/zhenqi-order/order/service-order-page`, {
            params: data,
            hideError: true,
        })
    },
    // 查询使用记录
    orderServiceUsagePage(data: IOrderUsageRecordParams): Promise<IOrderUsageRecordResponse> {
        return http.get(`/api/zhenqi-order/order/service-usage-page`, {
            params: data,
            hideError: true,
        })
    },
    orderBuyLegal(data: IOrderParams): Promise<IOrderBuyLegalResponse> {
        return http.post(`/api/zhenqi-order/order/buy-legal`, data)
    },
    orderCheckEntBuy(data: IOrderParams | IOrderCheckEntBuyParams): Promise<IOrderCheckEntBuyResponse | IOrderCheckEntBuyResponseArr> {
        return http.get(`/api/zhenqi-order/order/check-ent-buy`, {
            params: data,
        })
    },
    // 导出权益列表数据
    orderTransExport(data:IOrderTransListParams): Promise<AxiosResponse<Blob>> {
        return http.post(`/api/zhenqi-order/order/trans-export`, data,{
            hideError: true,
            fullRes: true,
            responseType: 'blob',
        })
    },
    // 导出消费明细
    orderUsageExport(data:IOrderTransListParams): Promise<AxiosResponse<Blob>> {
        return http.post(`/api/zhenqi-order/order/usage-export`, data,{
            hideError: true,
            fullRes: true,
            responseType: 'blob',
        })
    },
    // 权益统计列表
    orderLegalStatisticList(data:IOrderLegalStatisticParams): Promise<IOrderLegalStatisticResponse> {
        return http.get(`/api/zhenqi-order/order/legal-statistic-list`, {
            params: data,
            hideError: true,  
        })
    },
    // 分配权益（分配给下级）
    orderAllocateEquitiesCustomer(data:IOrderAllocateEquitiesCustomerParams): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-order/order/allocate-equities-customer`, data)
    },
    // 服务市场（商品列表）
    orderGoodsList(data:IOrderGoodsListParams):Promise<IOrderGoodsListResponse>{
        return http.get('/api/zhenqi-order/order/goods-list',{
            params:data,
            hideError:true
        })
    },
    // 订购服务（购买商品）
    orderAllocateEquitie(data:IOrderAllocateRquitiesParams):Promise<ICommonResponse>{
        return http.post('/api/zhenqi-order/order/allocate-equities',data,{
            hideError:true
        })
    },
    // 邀请管理
    orderInviteNewInviteRecord(data: IOrderInviteRecordParams): Promise<IOrderInviteRecordList> {
        return http.post(`/api/zhenqi-order/invite-new/invite-record`, data, {
            hideError: true,
        })
    },
}
