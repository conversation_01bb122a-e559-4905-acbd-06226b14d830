// table样式
:deep(.el-table thead) {
	font-size: 16px;
	color: #303133;
	font-weight: 500;
}

:deep(.el-table__body-wrapper) {
	font-size: 16px;
	color: #303133;
	font-weight: 400;
}

:deep(.el-table thead .el-table__cell) {
	background-color: #F5F7FA;
	border-bottom: #DEDEDF;
}

:deep(.el-table .el-table__cell) {
	--el-font-size-base: 16px
}

:deep(.el-table thead .el-table-fixed-column--right) {
	background-color: #F5F7FA !important;
	border-bottom: #DEDEDF;
}

:deep(.el-table thead .el-table-fixed-column--right) {
	background-color: #F5F7FA !important;
	border-bottom: #F5F7FA;
}

:deep(.el-table thead .el-table-column--selection) {
	background-color: #F5F7FA !important;
	border-bottom: #F5F7FA;
}

// table样式

// tab样式
:deep(.el-tabs__item) {
	font-size: 16px;
	font-weight: 500;
}

:deep(.el-tabs__item:hover) {
	color: var(--main-blue-);
}

:deep(.el-tabs__item.is-active) {
	color: var(--main-blue-);
}

:deep(.el-tabs__active-bar) {
	background-color: var(--main-blue-);
}

:deep(.el-tabs__header) {
	margin-bottom: 25px;
}

// tab样式

// button
:deep(.el-button--primary.is-link,
	.el-button--primary.is-plain,
	.el-button--primary.is-text) {
	--el-button-border-color: #1966FF !important;
	--el-button-bg-color: #ffffff !important;
}

:deep(.el-tag) {
	--el-tag-font-size: 14px
}

:deep(.el-tag--plain, .el-tag--plain.el-tag--primary) {
	--el-tag-bg-color: none
}

:deep(.el-tag.el-tag--primary) {
	--el-tag-text-color: var(--main-blue-);
	--el-tag-border-color: var(--main-blue-)
}

// el-select
:deep(.el-select__wrapper.is-focused) {
	box-shadow: 0 0 0 1px var(--main-blue-) inset;
}

.el-select-dropdown__item.is-selected {
	color: var(--main-blue-);
}

//el-input
:deep(.el-input__wrapper.is-focus) {
	box-shadow: 0 0 0 1px var(--main-blue-) inset;
}

// 日期选择器
:deep(.el-range-editor.is-active, .el-range-editor.is-active:hover) {
	box-shadow: 0 0 0 1px var(--main-blue-) inset;
}

// 级联选择器
:deep(.el-cascader .el-input.is-focus .el-input__wrapper) {
	box-shadow: 0 0 0 1px var(--main-blue-, var(--main-blue-)) inset;
}

// 分页器
:deep(.el-pager li.is-active) {
	color: var(--main-blue-)
}

:deep(.el-pager li:hover) {
	color: var(--main-blue-)
}

:deep(.el-pagination button:hover) {
	color: var(--main-blue-)
}

// 多选框
:deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
	background-color: var(--main-blue-);
	/* 选中后的背景色 */
	border-color: var(--main-blue-);
	/* 边框颜色（可选） */
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
	background-color: var(--main-blue-);
	/* 选中后的背景色 */
	border-color: var(--main-blue-);
	/* 边框颜色（可选） */
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner::after) {
	border-color: #ffffff;
	/* 勾选图标颜色 */
}