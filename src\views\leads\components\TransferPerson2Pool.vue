<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue'

import type { IGetTabPool, ILeadTransfelParams } from '@/types/lead'
import type { FormInstance, FormRules } from 'element-plus'
import type { IUserAccountInfoUser } from '@/types/user'
import type { RootState } from '@/types/store'

import crmService from '@/service/crmService'
import customService from '@/service/customService'

import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'

const store = useStore<RootState>()
const formRef = ref<FormInstance>()
const props = defineProps<{
    visible: boolean
    checkedIds: string[]
    fromCustomer?:boolean
}>()
const dialogVisible = ref(props.visible)
const getPool = async () => {
    if (props.fromCustomer) { // 客户公海
        let res = await customService.customTabPool(
            {
                isCustomerPool: 1
            }
        )
        poolList.value = res
    } else { //线索池
        let res = await crmService.crmTabPool()
        poolList.value = res
    }
}
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
        getPool()
    }
}, {
    immediate: true
})

const form = reactive({
    targetPoolId: '',
})
const poolList = ref<IGetTabPool[]>([])
const userInfo = ref<IUserAccountInfoUser>()
onMounted(async () => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    userInfo.value = user
    // await getPool()
})
const rules = reactive<FormRules>({
    targetPoolId: [{ required: true, message: props.fromCustomer ? '请选择客户公海':'请选择线索池' , trigger: 'blur' }],
})
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            let obj: ILeadTransfelParams = {
                ids: props.checkedIds,
                transferType: 1,
                targetPoolId: form.targetPoolId
            }
            crmService.crmTransfer(obj).then(() => {
                ElMessage.success('转移成功')
                handleClose()
            })
            emit('update:visible', false)
        } else {
            console.log('form表单效验不通过', fields)
        }
    })
}
const emit = defineEmits(['closeVisible','update:visible'])
const handleClose = (val?: string) => {
    form.targetPoolId = ''
    emit('closeVisible',val)
}
const handleCancel = () => {
    dialogVisible.value = false
}
</script>
<template>
    <el-dialog
        header-class="dialog-custom-header"
        v-model="dialogVisible"
        :title="props.fromCustomer ? '转移至客户公海' : '转移至线索池'"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose('cancel')"
    >
        <el-form class="tenant-form" ref="formRef" :model="form" label-position="top" :rules="rules">
            <!-- 人到线索池 线索池到线索池 -->
            <el-form-item :label="props.fromCustomer ? '客户公海' : '线索池' " prop="targetPoolId">
                <el-select :placeholder="props.fromCustomer ? '请选择客户公海' : '请选择线索池'" v-model="form.targetPoolId">
                    <el-option v-for="pool in poolList" :key="pool.id" :label="pool.name" :value="pool.id"></el-option>
                </el-select>
            </el-form-item>
            <!-- 人到客户公海 客户公海到客户公海 -->
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleCancel()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss">
</style>
