<template>
    <el-table
        :data="tableData"
        v-loading="loading"
        show-overflow-tooltip
        row-key="id"
        header-row-class-name="tableHeader"
        :style="{ 'min-height': height + 'px' }"
    >
        <template v-if="!loading" #empty>
            <div class="display-flex flex-column top-bottom-center">
                <img class="w-260 h-260" src="@/assets/images/customer-public-no-data.png" alt="暂无数据" />
                <div class="font-first-title-unactive color-two-grey b-margin-16">暂无数据</div>
            </div>
        </template>
        <el-table-column v-if="!props.isDialog" label="办税小号" prop="telX"></el-table-column>
        <el-table-column v-if="!props.isDialog" label="办税员号码" prop="telB"></el-table-column>
        <el-table-column label="企业名称" prop="entName"></el-table-column>
        <el-table-column label="企业税号" prop="socialCreditCode"></el-table-column>
        <el-table-column label="绑定时间" prop="createTime"></el-table-column>
        <el-table-column v-if="isAdmin && !props.isDialog" label="所属租户" prop="tenantName"></el-table-column>
        <el-table-column label="操作" width="200px">
            <template #default="scope">
                <el-button type="text" @click="unbind(scope.row)">解绑</el-button>
            </template>
        </el-table-column>
    </el-table>
    <el-dialog
        title="添加办税员"
        v-model="showAddDialog"
        width="500px"
    >
        <div>
            <div class="font-14 color-black">请用法人/财务负责人身份扫码登录</div>
            <div class="ewmstyle t-margin-12 flex-center flex-column border-radius-8 border gap-8" >
                <div class="qr-container" v-loading="ewmLoading">
                    <div class="scan-frame">
                        <div class="scan-corner scan-corner-tl"></div>
                        <div class="scan-corner scan-corner-tr"></div>
                        <div class="scan-corner scan-corner-bl"></div>
                        <div class="scan-corner scan-corner-br"></div>
                        <vue-qr
                            class="qr-code"
                            ref="qrcode"
                            :title="collectUrl"
                            :text="collectUrl"
                            :size="120"
                            :margin="0"
                        ></vue-qr>
                    </div>
                </div>
                <span>失效时间：{{ endTime || '' }}</span>
            </div>
        </div>
    </el-dialog>
    
</template>

<script lang="ts" setup>
import { ref, defineProps, watch, computed } from 'vue'
import type { IBindCompanyTableDataItem } from '@/types/worknumber'
import { ElMessage, ElMessageBox } from 'element-plus'
import tamService from '@/service/tamService'
import { useStore } from 'vuex'


const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})
const props = defineProps<{
    tableDataList: IBindCompanyTableDataItem[]
    tableLoading: boolean
    tableHeight: number
    refresh:() => void
    isDialog:boolean
}>()
const tableData = ref<IBindCompanyTableDataItem[]>()
watch(() => props.tableDataList, (newVal) => {
    // console.log('newVal',newVal)
    tableData.value = newVal
}, { immediate: true })
const loading = ref(false)
watch(() => props.tableLoading, (newVal) => {
    loading.value = newVal
}, { immediate: true })
const height = ref(400)
watch(() => props.tableHeight, (newVal) => {
    height.value = newVal
},{ immediate: true })
const unbind = (row:IBindCompanyTableDataItem) => {
    console.log(row)
    ElMessageBox.confirm('是否确认解绑?',
        '提示',
        {
            confirmButtonText: '解绑',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {
        tamService.unbindEnterprise({id:row.id,socialCreditCode:row.socialCreditCode}).then((response) => {
            console.log('unbind', response)
            if(response.success){
                ElMessage.success('解绑成功')
                props.refresh()
            }else{
                ElMessage.error(response.errMsg)
            }
        })
    })
}
const ewmLoading = ref(false)
const showAddDialog = ref(false)
const collectUrl = ref('')
const endTime = ref('')

</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';

</style>
