<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'

const lifeCycle = [
    {
        label: '初创期',
        num: '',
        prop: '20',
        color: 'primary',
        bgCo: '#2B83FD',
    },
    {
        label: '成长期',
        num: '',
        prop: '20',
        color: 'lightblue',
        bgCo: '#60CDF4',
    },
    {
        label: '成熟期',
        num: '',
        prop: '30',
        bgCo: '#01D9B9',
    },
    {
        label: '衰落期',
        num: '',
        prop: '30',
        bgCo: '#CADEEB',
    },
]
</script>
<template>
    <div class="smzq">
        <div class="b-margin-24">
            <ModuleTitle title="生命周期分布"></ModuleTitle>
        </div>
        <div class="display-flex margin-top-12 life-box">
            <view v-for="(life,index) in lifeCycle" :key="index" :style="{ width: life.prop + '%' }">
                <view :style="{ backgroundColor: life.bgCo }" class="border-right-white life-item font-white">-</view>
                <view :style="{ color: life.bgCo }" class="font-bold margin-top-4">
                    {{ life.label }}
                </view>
            </view>
        </div>
    </div>
</template>
<style scoped lang="scss">
.life-box {
	height: 100%;
	width: 100%;
	align-items: stretch;
}
.border-right-white {
    border-right: 1px solid var(--color-white-);
}
.font-white {
	color: var(--border-color);
}
.font-bold {
	font-weight: bold;
}
.smzq {
    width: 100%;
    padding: 16px;
    background-color: #fff;
    .smzq-echart {
        width: 100%;
        height: 230px;
        background-color: plum;
    }
    .life-item {
        height: 80%;
        flex-grow: 1;
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
    }
}
</style>
