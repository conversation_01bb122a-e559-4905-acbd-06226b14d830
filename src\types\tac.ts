// 声明根状态类型
export interface IResProps {
    code: number
    data: { id: string }
}

export interface ITacProps {
    destroyWindow: () => void
    reloadCaptcha: () => void
}

export interface IBtnProps {
    el?: null
    tac?: { destroyWindow: () => void }
}

export interface IConfig {
    requestCaptchaDataUrl: string
    validCaptchaUrl: string
    bindEl: string
    validSuccess: (res: IResProps, e?: null, tac?: ITacProps) => void
    validFail: (res: IResProps, c?: null, tac?: ITacProps) => void
    btnRefreshFun: (el?: null, tac?: ITacProps) => void
    btnCloseFun: (el?: null, tac?: ITacProps) => void
}
