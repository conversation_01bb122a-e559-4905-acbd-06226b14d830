<script lang="ts" setup>
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { computed, inject } from 'vue'
import authService from '@/service/authService'
import userService from '@/service/userService'
import OrgProfile from './components/org/OrgProfile.vue'

const store = useStore<RootState>()

const reload = inject<() => void>('reload', () => {})

const props = defineProps<{
    hideSwitcher: () => void
}>()

const orgs = computed(() => {
    const { account } = store.state.user || {}
    const { orgs } = account || {}
    return orgs
})

const handleOrgClick = (orgId: string) => {
    if (!orgId) return

    authService.switchOrg({ orgId, grant_type: 'switch_org' }).then((data) => {
        store.dispatch('auth/loginSuccess', data)
        userService.userGetAccountInfo().then((account) => {
            store.dispatch('user/setAccountInfo', { ...account })
        })

        reload()

        props.hideSwitcher()
    })
}
</script>

<template>
    <div class="flex width-100 flex flex-column gap-4">
        <div class="flex flex-column all-padding-16 gap-12">
            <div class="font-14 color-three-grey">组织</div>
            <div class="flex flex-column gap-12 max-height-468 oa">
                <OrgProfile
                    v-for="(org, index) in orgs"
                    :key="index"
                    :name="org.name"
                    :id="org.id"
                    :tags="[]"
                    :click="handleOrgClick"
                />
            </div>
        </div>
        <div class="split-line"></div>
        <div class="all-padding-12">
            <div class="flex top-bottom-center left-right-center pointer" @click="props.hideSwitcher">返回</div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
