<script lang="ts" setup>
import outboundService from '@/service/outboundService'
import type {
    IAutoDialerTaskCallContentItem,
    IAutoDialerTaskCallDetailItem,
    IAutoDialerTaskDetailListItem,
} from '@/types/autoDialer'
import { ElMessage } from 'element-plus'
import { ref, watch } from 'vue'
import BUIDINGPNG from '@/assets/images/chat-box/building.png'
import USERPNG from '@/assets/images/chat-box/user.png'

// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================
const props = defineProps<{
    record: IAutoDialerTaskCallDetailItem
    taskInfo: IAutoDialerTaskDetailListItem
}>()
// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const list = ref<IAutoDialerTaskCallContentItem[] | null>(null)

// 状态管理
const isPlaying = ref(false)
const isPaused = ref(false)

const audio = ref<HTMLAudioElement | null>(null)
// const count = ref(0)
// const state = reactive({
//   key: 'value'
// })

// ====================== Methods ======================
// const methodName = () => {
//   // Implementation
// }
const getData = () => {
    // if (!props.taskInfo) return
    outboundService
        .taskCallContent({
            audioRecordCode: props.record.audioRecordCode,
            tenantId: props.taskInfo.tenantId,
        })
        .then((res) => {
            console.log(res)
            const { errCode, data } = res
            console.log(res)
            if (errCode === 0) {
                list.value = data
            } else {
                list.value = []
            }
        })
        .catch(() => {
            list.value = []
        })
}

// ====================== Methods ======================
// 播放音频流
const getAudioStream = async () => {
    outboundService
        .taskCallDownloadRecordingFile({
            audioRecordCode: props.record.audioRecordCode,
            tenantId: props.taskInfo.tenantId,
        })
        .then(async (res) => {
            const contentType = res.headers.get('Content-Type')
            if (contentType?.includes('application/json')) {
                ElMessage.error('音频文件加载失败')
            } else {
                const blob = new Blob([res.data], { type: 'audio/mpeg' })
                const objectUrl = URL.createObjectURL(blob)
                audio.value = new Audio(objectUrl)

                audio.value.addEventListener('ended', () => {
                    isPlaying.value = false
                    isPaused.value = false
                    if (audio.value) {
                        audio.value.currentTime = 0 // 重置到开头
                    }
                })

                playAudio()
            }
        })
        .catch(() => {})
}

// 播放音频
const playAudio = (): void => {
    if (audio.value) {
        audio.value.play()
        isPlaying.value = true
        isPaused.value = false
    } else {
        getAudioStream()
    }
}

// 暂停播放
const pauseAudio = (): void => {
    if (audio.value && !audio.value.paused) {
        audio.value.pause()
        isPaused.value = true
    }
}

// 继续播放
// const resumeAudio = (): void => {
//     if (audio.value && isPaused.value) {
//         audio.value.play()
//         isPaused.value = false
//     }
// }

// 停止播放并重置进度
// const stopAudio = (): void => {
//     if (audio.value) {
//         audio.value.pause()
//         audio.value.currentTime = 0
//         isPaused.value = false
//     }
// }

// ====================== Watchers ======================
// watch(someRef, (newVal) => {
//   // Side effect
// }, { immediate: true })
watch(
    () => props.record,
    (newVal) => {
        console.log('record changed to:', newVal)
        if (newVal) {
            getData()
            audio.value = null
        }
    },
    { immediate: true }
)

// ====================== Lifecycle Hooks ======================
// onMounted(() => {
//     console.log('onMounted123123')
//     getData()
// })
</script>

<template>
    <div class="flex flex-column back-color-four-blue flex-1 border-radius-8">
        <div class="flex flex-row gap-8 top-bottom-center all-padding-24">
            <div>通话录音</div>
            <el-icon class="color-blue pointer" :size="16">
                <VideoPlay v-show="!isPlaying" @click="playAudio" />
                <VideoPause v-show="isPlaying && !isPaused" @click="pauseAudio" />
                <VideoPlay v-show="isPlaying && isPaused" @click="playAudio" />
            </el-icon>
        </div>
        <div class="split-line"></div>
        <div class="flex flex-column gap-24 all-padding-24">
            <template v-if="list && list.length === 0">
                <el-empty description="暂无通话记录" />
            </template>
            <template v-for="record in list" :key="record.questionId">
                <div class="flex flex-row gap-8 top-bottom-center" v-if="record.type === '0'">
                    <div class="height-100">
                        <div class="h-60 w-60 border-radius-60">
                            <img :src="BUIDINGPNG" alt="avatar" />
                        </div>
                    </div>
                    <div class="flex flex-column chat-content gap-2">
                        <!-- <div class="font-16 color-three-grey">2012-102-102</div> -->
                        <div class="border-radius-4 all-padding-12 chat-box">
                            <div class="color-white">
                                {{ record.content }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-row gap-8 justify-flex-end top-bottom-center" v-if="record.type === '1'">
                    <div class="flex flex-column chat-content gap-2">
                        <!-- <div class="flex font-16 color-three-grey justify-flex-end">2012-102-102</div> -->
                        <div class="border-radius-4 all-padding-12 chat-box">
                            <div class="color-white">
                                {{ record.content }}
                            </div>
                        </div>
                    </div>
                    <div class="h-60 w-60 border-radius-60">
                        <img :src="USERPNG" alt="avatar" />
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.chat-box {
    background: linear-gradient(
        132.6deg,
        rgba(204, 221, 255, 0.01) 0%,
        rgba(30, 103, 250, 0.55) 0%,
        rgba(54, 121, 255, 1) 100%
    );
}
.chat-content {
    max-width: 60%;
    word-wrap: break-word;
}

@media screen and (max-width: 1100px) {
}
</style>
