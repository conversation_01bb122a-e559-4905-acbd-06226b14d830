'use strict'

/**
 * @desc 解决浮点数运算精度问题（加减乘除），避免例如：
 * 2.3 + 2.4 = 4.699999999999999
 * 1.0 - 0.9 = 0.09999999999999998
 */

let _boundaryCheckingState = false

/**
 * strip(0.09999999999999998) -> 0.1
 * 修正浮点误差
 */
export function strip(num: number, precision: number = 12): number {
    return +parseFloat(num.toPrecision(precision))
}

/**
 * 获取数字小数位长度（包含科学计数法）
 */
export function digitLength(num: number): number {
    const eSplit = num.toString().split(/[eE]/)
    const len = (eSplit[0].split('.')[1] || '').length - +(eSplit[1] || 0)
    return len > 0 ? len : 0
}

/**
 * 小数转整数，支持科学计数
 */
export function float2Fixed(num: number): number {
    if (num.toString().indexOf('e') === -1) {
        return Number(num.toString().replace('.', ''))
    }
    const dLen = digitLength(num)
    return dLen > 0 ? strip(num * Math.pow(10, dLen)) : num
}

/**
 * 检查数字是否超出安全范围
 */
export function checkBoundary(num: number): void {
    if (_boundaryCheckingState) {
        if (num > Number.MAX_SAFE_INTEGER || num < Number.MIN_SAFE_INTEGER) {
            console.warn(`${num} is beyond boundary when transfer to integer, results may not be accurate`)
        }
    }
}

/**
 * 精确乘法
 */
export function times(num1: number, num2: number, ...others: number[]): number {
    if (others.length > 0) {
        return times(times(num1, num2), others[0], ...others.slice(1))
    }
    const num1Changed = float2Fixed(num1)
    const num2Changed = float2Fixed(num2)
    const baseNum = digitLength(num1) + digitLength(num2)
    const leftValue = num1Changed * num2Changed
    checkBoundary(leftValue)
    return leftValue / Math.pow(10, baseNum)
}

/**
 * 精确加法
 */
export function plus(num1: number, num2: number, ...others: number[]): number {
    if (others.length > 0) {
        return plus(plus(num1, num2), others[0], ...others.slice(1))
    }
    const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)))
    return (times(num1, baseNum) + times(num2, baseNum)) / baseNum
}

/**
 * 精确减法
 */
export function minus(num1: number, num2: number, ...others: number[]): number {
    if (others.length > 0) {
        return minus(minus(num1, num2), others[0], ...others.slice(1))
    }
    const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)))
    return (times(num1, baseNum) - times(num2, baseNum)) / baseNum
}

/**
 * 精确除法
 */
export function divide(num1: number, num2: number, ...others: number[]): number {
    if (others.length > 0) {
        return divide(divide(num1, num2), others[0], ...others.slice(1))
    }

    const num1Changed = float2Fixed(num1)
    const num2Changed = float2Fixed(num2)

    checkBoundary(num1Changed)
    checkBoundary(num2Changed)

    // 修正 10 ** -4 → 0.00009999999999999999 的情况
    return times(num1Changed / num2Changed, strip(Math.pow(10, digitLength(num2) - digitLength(num1))))
}

/**
 * 精确四舍五入
 */
export function round(num: number, ratio: number): number {
    const base = Math.pow(10, ratio)
    return divide(Math.round(times(num, base)), base)
}

/**
 * 是否开启安全范围检查
 */
export function enableBoundaryChecking(flag: boolean = false): void {
    _boundaryCheckingState = flag
}

const _default = {
    strip,
    plus,
    minus,
    times,
    divide,
    round,
    digitLength,
    float2Fixed,
    enableBoundaryChecking,
}

export default _default
