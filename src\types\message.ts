import type { IPaginationResponse } from './axios'
import type { IAllRecord } from './record'

export interface IMessageListRequest {
    page: number
    pageSize: number
    userId: string
    endDate: string
    isRead: boolean
    msgCode: string
    startDate: string
}

export interface IMessageItem{
    content: string
    createTime: string
    extras: string
    intent: string
    isRead: boolean
    jumpUrl: string
    msgCode: number
    msgId: number
    msgType: string
    title: string
}
export interface ISystemMessageItem{
    appid: string
    content: string
    createTime: string
    isRead: boolean
    linkNoticeId: number
    msgId: number
    msgType: string
    title: string
}
export interface IMessageListResponse extends IPaginationResponse{
    data?: IMessageItem[]
}

export interface IMessagePageParams extends IAllRecord{
    page: number
    pageSize: number
    endDate?: string
    startDate?: string
    isRead?: boolean
    msgCode?: string
}
export interface IMessagePageResponse{
    code: string
    errCode: number
    page: number
    pageSize: number
    total: number
    data?: IMessageItem[]
}

export interface ISystemMessagePageResponse{
    code: string
    errCode: number
    page: number
    pageSize: number
    total: number
    data?: ISystemMessageItem[]
}

export interface IMessageReadResponse{
    code: string
	data: object
	errCode: number
	msg: string
	state: string
}

export interface IMessageRemoveRequest extends IAllRecord {
    ids: string
}

export interface IMessageUnreadCountParams extends IAllRecord {
    msgCode?: string
    sysMsgCode?: string
}

export interface IMessageDetail {
    appid: string
    channelIds: string[]
    content: string
    id: number
    msgCode: string
    msgType: number
    status: number
    summary: string
    title: string
}