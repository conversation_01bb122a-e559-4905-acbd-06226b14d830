<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import type { ModelRes } from '@/types/home'
import { computed } from 'vue'

const props = defineProps<{
    data: ModelRes[]
}>()

const transformedData = computed(() => {
    return props.data.map((item) => {
        const key = Object.keys(item)[0] // 获取对象的键名（例如 "企业技术中心"）
        const indexName = item[key].indexName.split('-')[0]
        return {
            ...item[key], // 扩展原有的属性
            title: key, // 添加新的 title 属性，值为原对象的键名
            indexName: indexName,
        }
    })
})
</script>
<template>
    <div class="kjxqy">
        <ModuleTitle style="margin-bottom: 24px" title="科技型企业"></ModuleTitle>
        <div class="kjxqy-header font-second-title">
            <div class="width-40">企业类型</div>
            <div class="width-20 text-center">企业总数</div>
            <div class="width-40 text-center">本年度净增数</div>
        </div>
        <div class="kjxqy-content" v-for="(item, index) in transformedData" :key="index">
            <div class="width-40">{{ item.indexName }}</div>
            <div class="width-20 text-center" style="font-weight: 700;">
                {{ item.indexValue ? Number(item.indexValue) : '-' }}
            </div>
            <div class="width-40 text-center" style=" font-weight: 700;">{{ item.num || '-' }}</div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.kjxqy {
    min-width: 350px;
    height: 100%;
    box-sizing: border-box;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    .kjxqy-header {
        padding: 0 16px;
        display: flex;
        justify-content: space-between;
        color: var(--two-grey);
        margin-bottom: 16px;
    }
    .kjxqy-content {
        font-size: 14px;
        color: var(--main-black);
        padding: 0 16px;
        height: 73px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background:
            linear-gradient(0deg, rgba(248, 249, 253, 1), rgba(248, 249, 253, 1)),
            linear-gradient(90deg, rgb(238, 241, 249) 0%, rgb(229, 232, 247) 100%);
        border-radius: 4px;
        margin-bottom: 12px;
        // .content-title{
        //     width: 40%;
        //     overflow: hidden;
        //     text-overflow: ellipsis; // 当内容超出容器时，显示为省略号（...）
        //     white-space: nowrap;
        // }
    }
    .kjxqy-content:last-child {
        margin-bottom: 0px;
    }
}
</style>
