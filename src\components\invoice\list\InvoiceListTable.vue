<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { downloadZipFile } from '@/utils/download'
import type { ICollectInvoiceFilter, ICollectInvoiceItem } from '@/types/invoice'
import reportService from '@/service/reportService'
import { minus } from '@/utils/number-precision'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    filterParams?: ICollectInvoiceFilter
    requestId: string
}>()

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})

// ====================== Refs & Reactive State ======================
const list = ref<ICollectInvoiceItem[]>([])
const loading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 20,
    total: 0,
})
const multipleSelection = ref<ICollectInvoiceItem[]>([])
const exporting = ref(false)

// ====================== Methods ======================
const getData = () => {
    loading.value = true
    reportService
        .collectGetInvoiceInfo({
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            requestId: props.requestId,
            ...props.filterParams,
        })
        .then((res) => {
            loading.value = false
            const { errCode, data, total } = res
            if (errCode === 0) {
                list.value = data
                pageInfo.value.total = total
            } else {
                list.value = []
            }
        })
        .catch(() => {
            loading.value = false
            list.value = []
            pageInfo.value.total = 0
        })
}

const exportRecord = () => {
    if (multipleSelection.value.length === 0) {
        return ElMessage.warning('请选择要导出的记录')
    }

    if (multipleSelection.value.length > 100) {
        return ElMessage.warning('单次导出上限100条')
    }

    const ids = [...new Set(multipleSelection.value.map((item) => item.digitalNumber))]

    if (exporting.value) return

    doExport(ids, props.requestId)
}

const handleSelectionChange = (val: ICollectInvoiceItem[]) => {
    multipleSelection.value = val
}

const dealAmount = (minuend: number, subtrahend: number) => {
    return minus(minuend, subtrahend)
}

const dealScopeStr = (scope: string) => {
    if (scope === '1') return '进项发票'
    if (scope === '2') return '销项发票'
    return '-'
}

const toDownload = (item: ICollectInvoiceItem) => {
    const { digitalNumber } = item || {}
    if (!digitalNumber) return ElMessage.warning('未知的发票号码')

    doExport([digitalNumber], props.requestId)
}

const doExport = (ids: string[], requestId: string) => {
    if (!ids) return ElMessage.warning('未知的发票号码')

    reportService
        .collectDownloadInvoiceFile({
            invoiceIds: ids,
            requestId: requestId,
        })
        .then(async (res) => {
            if (res instanceof Blob) {
                const { type } = res
                if (type === 'application/zip') {
                    const fileName = `${Date.now()}_${user.value?.nickname}_发票导出.zip`
                    downloadZipFile(res, fileName)
                } else {
                    ElMessage.error('发票下载失败，请稍后再试')
                }
            } else {
                const { errCode, errMsg } = res
                if (errCode === 0) {
                    ElMessage.success(errMsg)
                } else {
                    ElMessage.error(errMsg)
                }
            }
        })
        .catch((error) => {
            ElMessage.error(error)
        })
}

const selectable = (row: ICollectInvoiceItem) => {
    return !!row.pdfUrl
}

const dealInvoiceNumber = (row: ICollectInvoiceItem) => {
    const { digitalNumber, invoiceNumber } = row || {}
    return digitalNumber || invoiceNumber || '-'
}

// ====================== Watchers ======================
watch(
    () => props.filterParams,
    (value) => {
        if (value) {
            pageInfo.value.page = 1
            getData()
        }
    },
    {
        deep: true,
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getData()
})
</script>

<template>
    <div class="flex flex-column height-100 invoice-list-table">
        <div class="flex flex-1 flex-column" style="overflow: hidden">
            <div class="flex flex-row gap-16 space-between tb-padding-16">
                <div class="flex flex-row gap-16 top-bottom-center">
                    <div class="flex flex-row gap-8 top-bottom-center">
                        <div class="font-14">
                            已选
                            <span class="color-blue lr-padding-2 font-weight-600">{{ multipleSelection.length }}</span>
                            个
                        </div>
                    </div>
                    <div class="flex flex-row">
                        <el-dropdown placement="bottom-start">
                            <el-button :loading="exporting" :disabled="exporting">
                                导出
                                <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="exportRecord()">导出所选</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </div>

            <el-table
                :data="list"
                style="width: 100%"
                v-loading="loading"
                @selection-change="handleSelectionChange"
                show-overflow-tooltip
                row-key="uuid"
            >
                <el-table-column type="selection" width="55" fixed="left" reserve-selection :selectable="selectable" />
                <el-table-column prop="scope" label="发票流向" width="90">
                    <template #default="scope">
                        {{ dealScopeStr(scope.row.scope) }}
                    </template>
                </el-table-column>
                <el-table-column prop="invoiceTypesName" label="发票类型" width="240" />
                <el-table-column prop="invoiceStatusCodeStr" label="开票类型" width="130" />
                <el-table-column prop="invoiceNumber" label="发票号码" width="240">
                    <template #default="scope">
                        {{ dealInvoiceNumber(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column prop="purchaseName" label="购方名称" width="240" />
                <el-table-column prop="purchaseTaxNumber" label="购方税号" width="210" />
                <el-table-column prop="distributorName" label="销方名称" width="240" />
                <el-table-column prop="distributorTaxNumber" label="销方税号" width="210" />
                <el-table-column prop="phoneNumber" label="合计金额" width="100">
                    <template #default="scope">
                        {{ dealAmount(scope.row.totalAmount, scope.row.taxAmount) }}
                    </template>
                </el-table-column>
                <el-table-column prop="taxAmount" label="合计税额" width="100" />
                <el-table-column prop="totalAmount" label="价税合计" width="100" />
                <el-table-column prop="invoiceDate" label="开票日期" width="110" />
                <el-table-column prop="remarks" label="备注" width="100" />
                <el-table-column label="操作" fixed="right" width="100" align="left">
                    <template #default="scope">
                        <a class="pointer color-blue" @click="toDownload(scope.row)" v-if="scope.row.pdfUrl">
                            下载发票
                        </a>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-affix position="bottom" :offset="0">
            <div class="display-flex top-bottom-center all-padding-18 justify-flex-end back-color-white">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="getData"
                    @current-change="getData"
                />
            </div>
        </el-affix>
    </div>
</template>

<style lang="scss" scoped>
.invoice-list-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
