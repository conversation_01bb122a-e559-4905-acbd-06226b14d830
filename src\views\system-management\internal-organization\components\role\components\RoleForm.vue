<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { IAddRoleItem, IRoleItem, IRoleEditRoleNameParams } from '@/types/role'

import systemService from '@/service/systemService'
import { ElMessage } from 'element-plus'

const props = defineProps<{
    visible: boolean
    from: string
    editItem: IRoleItem
}>()
const formRef = ref<FormInstance>()
const form = ref<IAddRoleItem>({})
const rules = reactive<FormRules<IAddRoleItem>>({
    roleName: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
})
const formVisible = ref(props.visible)
watch(() => props.visible, (newVal) => {
    formVisible.value = newVal
})
watch(
    () => props.editItem,
    (newVal) => {
        const { roleName, id } = newVal
        if (roleName && id) {
            form.value.roleName = props.editItem.roleName
            form.value.id = props.editItem.id
        }
    }
)
const emit = defineEmits(['closeVisible'])
const handleClose = () => {
    form.value = {}
    emit('closeVisible')
}
const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    handleClose()
}
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (props.from === 'add') {
                let addRes = await systemService.roleAdd(form.value)
                if (addRes.success === true) {
                    ElMessage.success('角色添加成功')
                } else {
                    ElMessage.error('操作失败')
                }
            } else {
                let editRes = await systemService.roleEditRoleName(form.value as IRoleEditRoleNameParams)
                if (editRes.success === true) {
                    ElMessage.success('角色修改成功')
                } else {
                    ElMessage.error('操作失败')
                }
            }
            handleClose()
        } else {
            console.log('form表单效验不通过', fields)
        }
    })
}
</script>
<template>
    <el-dialog
        v-model="formVisible"
        :title="props.from === 'add' ? '新增' : '修改'"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()"
    >
        <el-form 
            class="tenant-form"
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="top"
            :hide-required-asterisk="true">
            <el-form-item label="角色名称" prop="roleName">
                <el-input v-model.trim="form.roleName" placeholder="请输入角色名称" />
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="resetForm(formRef)">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
