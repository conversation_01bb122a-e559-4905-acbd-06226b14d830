<script lang="ts" setup>
import type { IAutoDialerTaskCallDetailItem, IAutoDialerTaskDetailListItem } from '@/types/autoDialer'
import moment from 'moment'
import { computed } from 'vue'

// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================
const props = defineProps<{
    record: IAutoDialerTaskCallDetailItem
    taskInfo?: IAutoDialerTaskDetailListItem
}>()

const customerList = computed(() => {
    return [
        {
            label: '客户姓名',
            value: props.taskInfo?.name || '-',
        },
        {
            label: '客户号码',
            value: props.record.cellphone || '-',
        },
        {
            label: '企业名称',
            value: props.taskInfo?.companyName || '-',
        },
        {
            label: '身份',
            value: props.taskInfo?.tags || '-',
        },
        {
            label: '机器人话术',
            value: props.record.robotName || '-',
        },
    ]
})

const callInfoList = computed(() => {
    return [
        {
            label: '通话时长',
            value: props.record.callDurationSec || '-',
        },
        {
            label: '通话时间',
            value: props.record.callStartTime ? moment(props.record.callStartTime).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
    ]
})

const callTagList = computed(() => {
    return [
        {
            label: '意向标签',
            value: props.record.aiTagName || '-',
        },
    ]
})
//
// const emit = defineEmits<{
//   (e: 'eventName', payload: SomeType): void
// }>()

// ====================== Store & Computed Values ======================
// const store = useSomeStore()
// const computedValue = computed(() => store.value)

// ====================== Refs & Reactive State ======================
// const state = reactive({
//   key: 'value'
// })

// ====================== Methods ======================
// const methodName = () => {
//   // Implementation
// }

// ====================== Watchers ======================
// watch(someRef, (newVal) => {
//   // Side effect
// }, { immediate: true })

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-column all-padding-24 gap-16">
        <div class="flex flex-column gap-16">
            <div class="flex flex-row top-bottom-center gap-8">
                <div class="w-6 h-16 back-color-blue border-radius-16"></div>
                <div class="font-18 color-black">客户信息</div>
            </div>
            <div
                class="flex flex-row top-bottom-center space-between l-padding-14"
                v-for="(item, index) in customerList"
                :key="index"
            >
                <div class="height-100 mw-100">
                    <div class="color-three-grey font-16">{{ item.label }}</div>
                </div>
                <div class="flex flex-1 color-black font-16 width-100 text-right flex-end">{{ item.value }}</div>
            </div>
        </div>
        <div class="split-line"></div>
        <div class="flex flex-column gap-16">
            <div class="flex flex-row top-bottom-center gap-8">
                <div class="w-6 h-16 back-color-blue border-radius-16"></div>
                <div class="font-18 color-black">通话信息</div>
            </div>
            <div
                class="flex flex-row top-bottom-center space-between l-padding-14"
                v-for="(item, index) in callInfoList"
                :key="index"
            >
                <div class="height-100 mw-100">
                    <div class="color-three-grey font-16">{{ item.label }}</div>
                </div>
                <div class="flex flex-1 color-black font-16 width-100 text-right flex-end">{{ item.value }}</div>
            </div>
        </div>
        <div class="split-line"></div>
        <div class="flex flex-column gap-16">
            <div class="flex flex-row top-bottom-center gap-8">
                <div class="w-6 h-16 back-color-blue border-radius-16"></div>
                <div class="font-18 color-black">对话标签</div>
            </div>
            <div
                class="flex flex-row top-bottom-center space-between l-padding-14"
                v-for="(item, index) in callTagList"
                :key="index"
            >
                <div class="height-100 mw-100">
                    <div class="color-three-grey font-16">{{ item.label }}</div>
                </div>
                <div class="flex flex-1 color-black font-16 width-100 text-right flex-end">{{ item.value }}</div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
