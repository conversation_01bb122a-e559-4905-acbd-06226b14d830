export interface BarChartOption {
    title?: echarts.TitleComponentOption
    tooltip?: echarts.TooltipComponentOption
    legend?: echarts.LegendComponentOption
    xAxis?: echarts.XAXisComponentOption | echarts.XAXisComponentOption[]
    yAxis?: echarts.YAXisComponentOption | echarts.YAXisComponentOption[]
    series?: echarts.SeriesOption
}

export interface PieChartOption {
    series: echarts.PieSeriesOption[]
    tooltip?: echarts.TooltipComponentOption
    legend?: echarts.LegendComponentOption
    title?: echarts.TitleComponentOption
}

export interface LineChartOption {
    series: echarts.LineSeriesOption | echarts.LineSeriesOption[]
    xAxis?: echarts.XAXisComponentOption | echarts.XAXisComponentOption[]
    yAxis?: echarts.YAXisComponentOption | echarts.YAXisComponentOption[]
    tooltip?: echarts.TooltipComponentOption
    legend?: echarts.LegendComponentOption
}