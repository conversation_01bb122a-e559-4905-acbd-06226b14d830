<script lang="ts" setup>
import ORGPNG from '@/assets/images/header/org.png'
// import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue'

const props = defineProps<{
    name: string
    id: string
    tags?: string[]
    showText?: boolean
    click?: (id: string) => void
    hideSwitch?: boolean
}>()

// const orgTagsRef = ref<HTMLElement | null>(null) // 可能是 HTMLElement，也可能是 null
// const tags = ref(props.tags)

// 每个元素之间的间隙（gap），4px
// const gap = 4

// 检查是否有标签超出父容器宽度
// const checkOverflow = () => {
//     nextTick(() => {
//         if (!orgTagsRef.value) return

//         const container = orgTagsRef.value
//         const parent = container.parentElement

//         if (!parent) return // 如果父级元素不存在，直接返回

//         let totalWidth = 0 // 累计所有元素的宽度
//         let overflowIndex = -1 // 超出容器的第一个元素的索引

//         // 遍历所有标签并检查它们的宽度
//         const children = Array.from(container.children) as HTMLElement[]
//         for (let i = 0; i < children.length; i++) {
//             totalWidth += children[i].offsetWidth + gap // 累加每个元素的宽度和间隙

//             // 判断是否超出容器宽度
//             if (totalWidth > parent.clientWidth && overflowIndex === -1) {
//                 overflowIndex = i // 记录第一个超出容器的元素索引
//                 break // 一旦找到超出容器的元素，跳出循环
//             }
//         }

//         if (overflowIndex !== -1) {
//             if (tags.value && tags.value?.length > 1) {
//                 tags.value = tags.value.slice(0, overflowIndex).concat('...')
//             }
//         }
//     })
// }

const handleClick = () => {
    props.click?.(props.id)
}

// onMounted(() => {
//     checkOverflow()
//     window.addEventListener('resize', checkOverflow) // 窗口大小变化时重新检查
// })

// // 在组件销毁之前移除事件监听
// onBeforeUnmount(() => {
//     window.removeEventListener('resize', checkOverflow) // 清除事件监听
// })
</script>

<template>
    <div
        :class="{ pointer: click, 'back-card-bg--hover': click }"
        class="border-tag flex flex-row top-bottom-center lr-padding-16 tb-padding-8 border-radius-4 gap-8 maxw-320"
        @click="handleClick"
    >
        <div class="flex top-bottom-center left-right-center w-40 h-40">
            <img :src="ORGPNG" alt="org" class="width-100" />
        </div>
        <div class="flex-1 oh">
            <div class="font-16 lh-16 color-black text-ellipsis text-nowrap">
                {{ name }}
            </div>
            <!-- <div ref="orgTagsRef" class="flex flex-row gap-4 text-nowrap">
                <div
                    class="org-tag"
                    :class="{ 'boder-none': tag === '...' }"
                    v-for="(tag, index) in tags ?? []"
                    :key="index"
                >
                    {{ tag }}
                </div>
            </div> -->
        </div>
        <div class="flex flex-row top-bottom-center justify-flex-end gap-8 max-width-52 pointer" v-if="!hideSwitch">
            <div class="font-14 lh-14" v-if="showText">切换</div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
