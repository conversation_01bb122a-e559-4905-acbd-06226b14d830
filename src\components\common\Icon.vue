<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
    icon: string // 图标名称，例如 "icon-a-huaban30"
    color?: string // 颜色，可选
    size?: string // 图标大小，可选
}>()

const svgStyle = computed(() => ({
    fill: props.color || 'inherit',
    fontSize: props.size || '1em',
}))
</script>

<template>
    <svg class="icon" aria-hidden="true" :style="svgStyle">
        <use :xlink:href="`#${icon}`"></use>
    </svg>
</template>

<style scoped>
.icon {
    width: 1em;
    height: 1em;
    /* vertical-align: middle; */
}
</style>
