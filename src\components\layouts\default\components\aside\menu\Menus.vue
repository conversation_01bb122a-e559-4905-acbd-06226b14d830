<script lang="ts" setup>
import type { IMenuResponse } from '@/types/menu'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const props = defineProps<{
    menu: IMenuResponse
    collapse: boolean
}>()

const open = (menu: IMenuResponse) => {
    router.push(menu.url)
}

const currentRoute = computed(() => {
    return router.currentRoute.value.name
})

const isSubmenu = (menu: IMenuResponse) => {
    return menu.type === 2 && menu.icon === ''
}

const isCurrentRoute = (menuId: string) => {
    return currentRoute.value === menuId
}

const matchedRoute = (menuId: string) => {
    const arr = router.currentRoute.value.matched
    if (menuId === 'index') {
        return arr[1].name === menuId
    }

    if (Array.isArray(arr) && arr.length >= 2) {
        return arr[0].name === menuId
    }
    return false
}
</script>

<template>
    <el-menu-item
        :index="props.menu.menuId"
        v-if="props.menu.type === 2"
        @click="open(props.menu)"
        class="cust-menu cust-menu-item"
        :class="{
            'cust-menu-item-sub': isSubmenu(props.menu),
        }"
    >
        <div
            style="width: 100%"
            :class="{
                'lr-padding-8': !isSubmenu(props.menu),
            }"
        >
            <div
                class="cust-menu-item-label flex h-40 top-bottom-center border-radius-4"
                :class="{
                    'back-color-menu-bg': isCurrentRoute(props.menu.menuId),
                    'back-color-main--hover': !isCurrentRoute(props.menu.menuId),
                    'l-padding-12': !isSubmenu(props.menu),
                    'l-padding-41': isSubmenu(props.menu),
                }"
            >
                <el-icon v-if="props.menu.icon">
                    <Icon
                        :icon="matchedRoute(props.menu.menuId) ? props.menu.icon + '_bod' : props.menu.icon"
                        size="18"
                    />
                </el-icon>
                <div v-if="!collapse || isSubmenu(props.menu)">{{ props.menu.name }}</div>
            </div>
        </div>
    </el-menu-item>
    <el-sub-menu
        :index="props.menu.menuId"
        v-if="props.menu.type === 1"
        class="cust-sub-menu"
        popper-class="menu-side-cust-popper"
    >
        <template #title>
            <el-icon>
                <Icon :icon="matchedRoute(props.menu.menuId) ? props.menu.icon + '_bod' : props.menu.icon" size="18" />
            </el-icon>
            <span>{{ props.menu.name }}</span>
        </template>
        <Menus :menu="menu" v-for="menu in props.menu.children" :key="menu.id" :collapse="collapse" />
    </el-sub-menu>
</template>

<style lang="scss" scoped>
.cust-menu .el-menu-vertical {
    border: none;
}
.cust-menu .el-menu-vertical:not(.el-menu--collapse) {
    width: 200px;
}

.cust-menu-item {
    padding-left: 0px !important;
    padding-right: 0px !important;
    height: 40px;
    margin-bottom: 8px;
}

.el-menu-item:hover {
    --el-menu-hover-bg-color: '';
}

.cust-menu .el-sub-menu,
.is-active :deep(.el-sub-menu__title) {
    color: var(--main-blue-);
}

.cust-sub-menu {
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 8px;
}

.cust-sub-menu :deep(.el-sub-menu__title) {
    padding-left: 12px !important;
    height: 40px;
    border-radius: 4px;
}

.cust-sub-menu :deep(.el-sub-menu__title):hover {
    background-color: var(--main-bg);
}

.cust-sub-menu :deep(ul) {
    padding-top: 8px;
}
</style>
