<script lang="tsx" setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const toRouter = () => {
    router.push('/')
}
</script>

<template>
    <div class="flex top-bottom-center gap-4 pointer back-btn" @click="toRouter">
        <div class="border-radius-16 w-16 h-16 flex-center">
            <Icon icon="icon-a-huaban12" class="fill-blue" />
        </div>
        <div class="font-header-label color-blue">返回首页</div>
    </div>
</template>

<style lang="scss" scoped>
.back-btn {
    border: 1px solid var(--main-blue-);
    padding: 4px 10px;
    border-radius: 4px;
    background-color: var(--main-blue-op2-);
}
</style>
