<script lang="ts" setup>
import { computed, onBeforeMount, onMounted, reactive, ref, watch } from 'vue'
import { LabeledItem, Section, TimeRange } from './components'
import commonData from '@/js/common-data'
import type {
    IAutoDialerListItem,
    IAutoDialerRobotListItem,
    IAutoDialerStrategyListItem,
    IAutoDialerTaskSaveRequest,
    ICreateTaskForm,
} from '@/types/autoDialer'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import outboundService from '@/service/outboundService'
import { debounce } from 'lodash'
import orderService from '@/service/orderService'
const env = import.meta.env.VITE_APP_ENV

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    crmId: string[]
    onClose: () => void
    currentTask?: IAutoDialerListItem
    refreshAfterEdit?: () => void
}>()

// ====================== Store & Computed Values ======================
const selectedNumber = computed(() => {
    if (props.crmId) {
        return props.crmId.length
    } else {
        return '-'
    }
})

const selectedPhoneNumbers = computed(() => {
    let total = 0
    if (contactType.value.includes(1)) {
        total = total + contactTagCount.value.recommend
    }

    if (contactType.value.includes(2)) {
        total = total + contactTagCount.value.other
    }

    return total
})

const isBalanceEnough = computed(() => {
    return selectedPhoneNumbers.value <= Number(balance.value)
})

// ====================== Refs & Reactive State ======================
const loading = ref(false)
const contactType = ref([1])
const taskType = ref('1')
const addPolicy = ref(false)
const setExpireData = ref(false)
const formRef = ref<FormInstance>()
const taskQueryLoading = ref(false)
const taskList = ref<IAutoDialerListItem[]>([])
const strategyList = ref<IAutoDialerStrategyListItem[]>([])
const robotList = ref<IAutoDialerRobotListItem[]>([])
const contactTagCount = ref({
    total: 0,
    recommend: 0,
    other: 0,
})
const balance = ref(0)
const requiredLoading = ref(true)
const isOverflow = ref(false)
const limit = env !== 'production' ? 200 : 2000

const form = ref<ICreateTaskForm>({
    taskName: '',
    taskDesc: '',
    strategyId: null,
    strategyName: '',
    outboundDate: '',
    outboundExpireDate: '',
    weekRange: [],
    recallStatus: true,
    outboundTimeInterval: '',
    robotId: '',
    taskId: null,
    allowRecallStatus: [],
    maxRecallTimes: 2,
    recallPeriodMin: 10,
})

const requestParmas = ref<IAutoDialerTaskSaveRequest>({
    allowRecallStatus: '',
    dialogTaskId: '',
    dialogTaskName: '',
    maxRecallTimes: 1,
    outboundCircleType: 1,
    outboundCircleValue: '',
    outboundDate: '',
    outboundExpireDate: '',
    outboundTimeInterval: '',
    recallPeriodMin: 2,
    recallStatus: 1,
    robotId: '',
    robotName: '',
    strategyName: '',
    taskDesc: '',
    crmId: [],
    taskName: '',
    tagType: [],
})

const formRules = reactive<FormRules<typeof form>>({
    taskName: [
        {
            required: true,
            message: '请输入任务名称',
            trigger: 'blur',
        },
        {
            max: 50,
            message: '最大可输入50个字符',
            trigger: 'change',
        },
    ],
    taskDesc: [
        {
            max: 100,
            message: '最大可输入100个字符',
            trigger: 'change',
        },
    ],
    strategyId: [
        {
            required: true,
            message: '请选择外呼策略',
            trigger: 'change',
        },
    ],
    strategyName: [
        {
            required: true,
            message: '请输入外呼策略名称',
            trigger: 'blur',
        },
        {
            max: 30,
            message: '最大可输入30个字符',
            trigger: 'change',
        },
    ],
    outboundDate: [
        {
            required: true,
            message: '请选择外呼日期',
            trigger: 'change',
        },
    ],
    outboundExpireDate: [
        {
            required: true,
            message: '请选择外呼过期日期',
            trigger: 'change',
        },
    ],
    weekRange: [
        {
            validator: (_, value, callback) => {
                // 条件1：weekRange 必填
                if (!value || value.length === 0) {
                    return callback(new Error('请选择外呼时间段'))
                }

                // 条件2：检查 outboundTimeInterval 是否为空（假设这个值存在其他地方）
                if (!form.value.outboundTimeInterval) {
                    return callback(new Error('请选择外呼时间段')) // 可以自定义消息
                }

                // 两个条件都通过
                callback()
            },
            trigger: 'change',
        },
    ],
    recallStatus: [
        {
            required: true,
            message: '请选择重呼状态',
            trigger: 'change',
        },
    ],
    outboundTimeInterval: [
        {
            required: true,
            message: '请选择外呼时间段',
            trigger: 'change',
        },
    ],
    robotId: [
        {
            required: true,
            message: '请选择机器人话术',
            trigger: 'change',
        },
    ],
    taskId: [
        {
            required: true,
            message: '请选择已有任务',
            trigger: 'change',
        },
    ],
    maxRecallTimes: [
        {
            required: true,
            message: '请输入重呼次数',
            trigger: 'change',
        },
    ],
    recallPeriodMin: [
        {
            required: true,
            message: '请输入重呼间隔',
            trigger: 'change',
        },
    ],
    allowRecallStatus: [
        {
            required: true,
            message: '请选择通话状态',
            trigger: 'change',
        },
    ],
})

const isEdit = computed(() => {
    return !!props.currentTask || taskType.value === '2'
})

const validate = () => {
    let flag = true
    if (!isEdit.value) {
        if (!contactType.value || !Array.isArray(contactType.value) || contactType.value.length === 0) {
            ElMessage.warning('请勾选本次外呼联系人')
            flag = false
        }
    }
    return flag
}

// ====================== Methods ======================
const submit = (formEl: FormInstance | undefined) => {
    if (!formEl) return

    if (!validate()) return

    formEl.validate().then((valid) => {
        if (valid && !isEdit.value) {
            ElMessageBox.confirm(
                `是否确认创建【${form.value.taskName}】？系统将拨打${selectedPhoneNumbers.value}个号码，同时锁定${selectedPhoneNumbers.value}个智能外呼权益。`,
                '确认',
                {
                    confirmButtonText: '确认',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            )
                .then(() => {
                    saveTask()
                })
                .catch(() => { })
        }

        if (valid && isEdit.value) {
            editTask()
        }
    })
}

const saveTask = () => {
    loading.value = true
    outboundService
        .taskSave(formatRequestParams())
        .then((res) => {
            loading.value = false
            const { errCode, errMsg } = res
            if (errCode === 0) {
                props.onClose()
                ElMessage.success('任务创建成功')
            } else {
                ElMessage.error(errMsg || '任务创建失败')
            }
        })
        .catch(() => {
            loading.value = false
            ElMessage.error('系统错误，任务创建失败')
        })
}

const editTask = () => {
    loading.value = true
    outboundService
        .taskEdit(formatRequestParams())
        .then((res) => {
            loading.value = false
            const { errCode, errMsg } = res
            if (errCode === 0) {
                props.onClose()
                if (props.refreshAfterEdit) props.refreshAfterEdit()
                ElMessage.success('任务修改成功')
            } else {
                ElMessage.error(errMsg || '任务修改失败')
            }
        })
        .catch(() => {
            loading.value = false
            ElMessage.error('系统错误，任务修改失败')
        })
}

const handleClose = () => {
    props.onClose()
}

const formatRequestParams = (): IAutoDialerTaskSaveRequest => {
    const params: IAutoDialerTaskSaveRequest = {
        ...requestParmas.value,
        taskName: form.value.taskName,
        taskDesc: form.value.taskDesc,
        outboundDate: form.value.outboundDate,
        outboundExpireDate: form.value.outboundExpireDate,
        recallStatus: form.value.recallStatus ? 1 : 0,
        outboundTimeInterval: form.value.outboundTimeInterval,
        robotId: form.value.robotId,
        taskId: form.value.taskId,
        allowRecallStatus: form.value.allowRecallStatus.join(','),
        maxRecallTimes: form.value.maxRecallTimes,
        recallPeriodMin: form.value.recallPeriodMin,
        outboundCircleValue: form.value.weekRange.sort().join(','),
        needEdit: true
    }

    if (addPolicy.value) {
        delete params.strategyId
    }

    if (!setExpireData.value) {
        params.outboundExpireDate = ''
    }

    if (!isEdit.value) {
        params.crmId = props.crmId || []
        params.tagType = contactType.value
        params.strategyId = form.value.strategyId
        params.strategyName = form.value.strategyName

    }

    if (isEdit.value) {
        console.log(props.currentTask)
        if (props.currentTask) {
            params.taskId = props.currentTask?.id
            params.taskCode = props.currentTask?.taskCode
            delete params.tagType
            delete params.crmId
        } else {
            params.taskId = form.value.taskId
            params.taskCode = form.value.taskCode
            params.crmId = props.crmId
            params.tagType = contactType.value
        }
        params.needEdit = false


    }

    // if(params.recallStatus === 0) {
    //     params.
    // }

    formatRebot(params)
    return params
}

const formatRebot = (params: IAutoDialerTaskSaveRequest) => {
    const engineId = form.value.robotId

    for (let index = 0; index < robotList.value.length; index++) {
        const robot = robotList.value[index]
        for (let index = 0; index < robot.engineInfoList.length; index++) {
            const engineInfo = robot.engineInfoList[index]
            if (engineInfo.engineId === engineId) {
                params.robotId = robot.robotId
                params.robotName = robot.robotName
                params.dialogTaskId = engineInfo.engineId
                params.dialogTaskName = engineInfo.engineName
            }
        }
    }
}

const taskSearch = (v: string) => {
    if (!v && taskList.value.length > 0) return
    if (!v) {
        getTaskList(v)
    } else {
        taskQueryLoading.value = true
        onSearch(v)
    }
}

const fetchPolicyList = () => {
    outboundService
        .strategyList()
        .then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                strategyList.value = data
            } else {
                strategyList.value = []
            }
        })
        .catch(() => {
            strategyList.value = []
        })
}

const fetchRobotList = () => {
    outboundService
        .robotList()
        .then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                robotList.value = data
            } else {
                robotList.value = []
            }
        })
        .catch(() => {
            robotList.value = []
        })
}

const getTaskList = (keyword: string) => {
    taskQueryLoading.value = true
    outboundService
        .taskPage({ taskName: keyword, page: 1, pageSize: 20, status: '1,2,3,5,6,7' })
        .then((res) => {
            taskQueryLoading.value = false
            const { errCode, data } = res
            if (errCode === 0) {
                taskList.value = data
            } else {
                taskList.value = []
            }
        })
        .catch(() => {
            taskQueryLoading.value = false
            taskList.value = []
        })
}

const onSearch = debounce((v: string) => {
    getTaskList(v)
}, 300)

const handleTimeIntervalChange = () => {
    // 手动触发 weekRange 的校验
    formRef.value?.validateField('weekRange')
}

const taskIdChange = (v: number) => {
    const target = taskList.value.find((e) => e.id === v)
    if (!target) return
    form.value.taskName = target.taskName

    if (target.outboundExpireDate) {
        setExpireData.value = true
    } else {
        setExpireData.value = false
    }

    form.value.allowRecallStatus = target.allowRecallStatus.split(',').map(Number)
    form.value.outboundDate = target.outboundDate
    form.value.outboundExpireDate = target.outboundExpireDate
    form.value.weekRange = target.outboundCircleValue.split(',').map(Number)
    form.value.recallStatus = target.recallStatus === 1
    form.value.outboundTimeInterval = target.outboundTimeInterval
    form.value.robotId = target.dialogTaskId
    form.value.maxRecallTimes = target.maxRecallTimes
    form.value.recallPeriodMin = target.recallPeriodMin
    form.value.strategyName = target.strategyName
    form.value.taskCode = target.taskCode
    form.value.taskId = target.id
}

const handleCurrentTaskInfo = () => {
    if (!props.currentTask) return

    form.value = {
        ...form.value,
        allowRecallStatus: props.currentTask.allowRecallStatus.split(',').map(Number),
        taskName: props.currentTask.taskName,
        taskDesc: props.currentTask.taskDesc,
        strategyId: null,
        strategyName: '',
        outboundDate: props.currentTask.outboundDate,
        outboundExpireDate: props.currentTask.outboundExpireDate,
        weekRange: props.currentTask.outboundCircleValue.split(',').map(Number),
        recallStatus: props.currentTask.recallStatus === 1,
        outboundTimeInterval: props.currentTask.outboundTimeInterval,
        robotId: props.currentTask.dialogTaskId,
        maxRecallTimes: props.currentTask.maxRecallTimes,
        recallPeriodMin: props.currentTask.recallPeriodMin,
    }
}

const getRecommendCount = () => {
    if (!props.crmId) return
    return outboundService.taskQueryContactTagCount({ crmId: props.crmId }).then((res) => {
        const { errCode, data } = res
        if (errCode === 0) {
            contactTagCount.value = data
        }
    })
}

const onStrategyChange = (v: number) => {
    console.log(v)
    const target = strategyList.value.find((e) => e.id === v)
    if (!target) return

    if (target.outboundExpireDate) {
        setExpireData.value = true
    } else {
        setExpireData.value = false
    }

    form.value.allowRecallStatus = target.allowRecallStatus.split(',').map(Number)
    form.value.outboundDate = target.outboundDate
    form.value.outboundExpireDate = target.outboundExpireDate
    form.value.weekRange = target.outboundCircleValue.split(',').map(Number)
    form.value.recallStatus = target.recallStatus === 1
    form.value.outboundTimeInterval = target.outboundTimeInterval
    form.value.robotId = target.dialogTaskId
    form.value.maxRecallTimes = target.maxRecallTimes
    form.value.recallPeriodMin = target.recallPeriodMin
}

const getOrderServiceStatistics = () => {
    return orderService
        .orderServiceStatistics({ serviceKeys: 'znwh' })
        .then((res) => {
            if (res && Array.isArray(res) && res.length > 0) {
                balance.value = res[0].num || 0
            } else {
                balance.value = 0
            }
        })
        .catch(() => {
            balance.value = 0
        })
}

const loadRequiredInfo = async () => {
    if (!isEdit.value) {
        await getOrderServiceStatistics()
        await getRecommendCount()
    }

    requiredLoading.value = false
}



const deleteStrategy = (id: number) => {
    ElMessageBox.confirm(
        '是否确认删除该策略?',
        '确认',
        {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            outboundService.strategyDelete(id).then(() => {
                ElMessage.success('删除成功')
                fetchPolicyList()
            })


        })
        .catch(() => {

        })

}

// ====================== Watchers ======================
watch(
    () => form.value,
    (val) => {
        console.log(val)
    }
)

watch(
    () => form.value.outboundTimeInterval,
    () => {
        handleTimeIntervalChange()
    }
)

watch(
    () => selectedPhoneNumbers.value,
    (val) => {
        if (val > limit) {
            isOverflow.value = true
        } else {
            isOverflow.value = false
        }
    }
)

// ====================== Lifecycle Hooks ======================
onBeforeMount(() => {
    loadRequiredInfo()
    handleCurrentTaskInfo()
})
onMounted(() => {
    fetchPolicyList()
    fetchRobotList()
})
</script>

<template>
    <div v-if="requiredLoading">
        <el-skeleton :rows="5" animated />
    </div>
    <div class="flex flex-column gap-24" v-if="!requiredLoading">
        <div class="flex flex-column gap-8">
            <el-alert title="当前勾选号码数已超智能外呼权益额度，暂时无法使用，请联系管理员充值" type="warning" v-if="!isBalanceEnough"
                      :closable="false" />
            <el-alert :title="`单次外呼任务，选中号码不可超过${limit}个`" type="warning" v-if="isOverflow" :closable="false" />
        </div>
        <div class="flex flex-column gap-16" v-if="!currentTask">
            <div class="font-18 font-weight-500">
                剩余智能外呼权益： <span>{{ balance }}</span>
            </div>
            <div>
                <div class="font-16 color-two-grey lh-24">费用说明：</div>
                <div class="font-16 color-two-grey lh-24">
                    当前已选 {{ selectedPhoneNumbers }} 个号码，任务创建后将锁定
                    {{ selectedPhoneNumbers }} 个外呼权益额度，任务结束后系统将根据接听数量进行扣减。
                </div>
            </div>
        </div>
        <Section title="配置本次外呼联系人" v-if="!currentTask" :tooltip="true">
            <div class="flex flex-column gap-8">
                <div class="color-two-grey">
                    当前已勾选
                    <span class="color-black lr-padding-4 font-weight-500">{{ selectedNumber }}</span>
                    家企业，其中推荐号码数量为
                    <span class="color-black lr-padding-4 font-weight-500">{{ contactTagCount.recommend }}</span>
                    个
                </div>
                <div class="flex flex-row gap-40">
                    <el-checkbox-group v-model="contactType" style="width: 100%">
                        <el-checkbox :value="1" label="选择推荐" size="large" />
                        <el-checkbox :value="2" label="选择其他" size="large" />
                    </el-checkbox-group>
                </div>
            </div>
            <template #tooltip>
                <div class="w-245">
                    <div>号码拨打优先级说明：</div>
                    <div>1、按照法人>高管>其他号码优先级顺序，若有接通将不再为您继续拨打剩余号码。</div>
                    <div>2、在“其他”选项中，每家企业我们仅为您优选排名第一的手机号进行拨打</div>
                    <div>预扣减说明:</div>
                    <div>
                        为了保障后续操作顺利进行，系统将从您的账户中预扣部分权益。如果客户号码未能成功拨通，系统将在后续自动返还对应的权益值
                    </div>
                </div>
            </template>
        </Section>
        <Section title="配置本次外呼任务策略">
            <el-form ref="formRef" :model="form" :rules="formRules" class="task-form">
                <div class="flex flex-column gap-20">
                    <LabeledItem label="任务名称" :required="true">
                        <div class="flex flex-1 flex-column">
                            <el-form-item prop="taskType" v-if="!currentTask">
                                <el-radio-group v-model="taskType">
                                    <el-radio value="1" size="large">创建新任务</el-radio>
                                    <el-radio value="2" size="large">已有任务</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <div>
                                <el-form-item prop="taskId" v-if="taskType === '2'">
                                    <el-select v-model="form.taskId" placeholder="请输入外呼任务名称搜索" size="large"
                                               style="width: 480px" filterable remote :remote-method="taskSearch"
                                               :loading="taskQueryLoading" @change="taskIdChange">
                                        <el-option v-for="item in taskList" :key="item.id" :label="item.taskName"
                                                   :value="item.id" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item prop="taskName" v-if="taskType === '1'">
                                    <el-input v-model="form.taskName" style="width: 480px; height: 44px"
                                              placeholder="请填写外呼任务名称" size="large" />
                                </el-form-item>
                            </div>
                        </div>
                    </LabeledItem>
                    <LabeledItem label="任务描述">
                        <div class="flex flex-1 flex-column gap-16">
                            <el-form-item prop="taskDesc">
                                <el-input v-model="form.taskDesc" style="width: 480px" :rows="4" type="textarea"
                                          placeholder="请填写任务描述" size="large" :disabled="taskType === '2'" />
                            </el-form-item>
                        </div>
                    </LabeledItem>
                    <LabeledItem label="外呼策略" :required="true" v-if="!isEdit">
                        <div class="flex flex-1 flex-column gap-16 w-480">
                            <div class="flex flex-row gap-16 top-bottom-center">
                                <div class="flex flex-1">
                                    <el-form-item prop="strategyId" class="width-100"
                                                  v-if="!addPolicy && !isEdit && taskType !== '2'">
                                        <el-select v-model="form.strategyId" placeholder="请选择外呼策略" size="large"
                                                   style="height: 44px" no-data-text="暂无外呼策略，请新增策略" @change="onStrategyChange">
                                            <el-option v-for="item in strategyList" :key="item.id"
                                                       :label="item.strategyName" :value="item.id">
                                                <div class="display-flex top-bottom-center space-between">
                                                    <div>
                                                        {{ item.strategyName }}
                                                    </div>
                                                    <div @click.stop="deleteStrategy(item.id)">
                                                        <el-icon>
                                                            <Delete />
                                                        </el-icon>
                                                    </div>
                                                </div>
                                            </el-option>
                                        </el-select>
                                    </el-form-item>

                                    <el-form-item prop="strategyName" class="width-100"
                                                  v-if="addPolicy || isEdit || taskType == '2'">
                                        <el-input v-model="form.strategyName" style="width: 100%; height: 44px"
                                                  placeholder="请填写外呼策略名称" size="large" :disabled="taskType === '2'" />
                                    </el-form-item>
                                </div>
                                <div class="pointer color-blue noselect" @click="addPolicy = !addPolicy"
                                     v-if="!isEdit && taskType !== '2'">
                                    {{ addPolicy ? '取消新增' : '新增策略' }}
                                </div>
                            </div>
                        </div>
                    </LabeledItem>
                    <LabeledItem label="外呼启动时间" :required="true">
                        <div class="flex flex-1 flex-column gap-16">
                            <div class="flex flex-row gap-16 top-bottom-center">
                                <div class="flex flex-1">
                                    <el-form-item prop="outboundDate" class="width-100">
                                        <el-date-picker v-model="form.outboundDate" style="width: 100%; height: 44px"
                                                        type="datetime" placeholder="请选择启动时间" format="YYYY-MM-DD HH:mm"
                                                        value-format="YYYY-MM-DD HH:mm" :disabled="taskType === '2'" />
                                    </el-form-item>
                                </div>
                                <div class="pointer noselect flex flex-row gap-4 top-bottom-center"
                                     v-if="taskType !== '2'">
                                    <div class="cust-check-box" :class="{
                                        'back-color-blue': setExpireData,
                                        'cust-check-box-checked': setExpireData,
                                    }" @click="setExpireData = !setExpireData">
                                        <el-icon class="color-white" :size="12">
                                            <Check />
                                        </el-icon>
                                    </div>
                                    <div>设置外呼结束时间</div>
                                </div>
                            </div>
                        </div>
                    </LabeledItem>
                    <LabeledItem label="外呼结束时间" :required="true" v-if="setExpireData">
                        <div class="flex flex-1 flex-column gap-16">
                            <div class="flex flex-row gap-16 top-bottom-center">
                                <div class="flex flex-1">
                                    <el-form-item prop="outboundExpireDate" class="width-100">
                                        <el-date-picker v-model="form.outboundExpireDate"
                                                        style="width: 100%; height: 44px" type="date" placeholder="请选择结束日期"
                                                        format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                                        :disabled="taskType === '2'" />
                                    </el-form-item>
                                </div>
                                <!-- 占位用 -->
                                <div class="noselect flex flex-row gap-4 top-bottom-center" style="visibility: hidden">
                                    <div class="cust-check-box" @click="setExpireData = !setExpireData">
                                        <el-icon class="color-white" :size="12">
                                            <Check />
                                        </el-icon>
                                    </div>
                                    <div>设置外呼结束时间</div>
                                </div>
                            </div>
                        </div>
                    </LabeledItem>
                    <LabeledItem label="外呼时间段" :required="true">
                        <div class="flex flex-1 flex-column left-right-center">
                            <el-form-item prop="weekRange">
                                <div class="flex flex-row gap-40 weekRange w-480">
                                    <el-checkbox-group v-model="form.weekRange" style="width: 100%"
                                                       :disabled="taskType === '2'">
                                        <el-checkbox v-for="item in commonData.weekRange" :key="item.value"
                                                     :label="item.label" :value="item.value" size="large" />
                                    </el-checkbox-group>
                                </div>
                                <div class="w-480">
                                    <TimeRange v-model="form.outboundTimeInterval" :disabled="taskType === '2'" />
                                </div>
                            </el-form-item>
                        </div>
                    </LabeledItem>
                    <LabeledItem label="机器人话术" :required="true">
                        <div class="flex flex-1 flex-column gap-16">
                            <el-form-item prop="robotId">
                                <el-select v-model="form.robotId" placeholder="请选择机器人话术" size="large"
                                           style="width: 480px" :disabled="taskType === '2'">
                                    <el-option v-for="{ engineId, engineName, robotName } in robotList.flatMap((r) =>
                                        r.engineInfoList.map((e) => ({
                                            engineId: e.engineId,
                                            engineName: e.engineName,
                                            robotName: r.robotName,
                                        }))
                                    )" :key="engineId" :label="`${robotName}-${engineName}`" :value="engineId">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                    </LabeledItem>
                    <LabeledItem label="自动重呼" :required="true">
                        <div class="flex flex-column gap-16 w-480 recall">
                            <el-form-item prop="recallStatus">
                                <el-switch v-model="form.recallStatus" size="large"
                                           :active-text="form.recallStatus ? '开启' : '未开启'" :disabled="taskType === '2'" />
                            </el-form-item>
                            <div class="all-padding-16 back-color-four-blue border-radius-8" v-if="form.recallStatus">
                                <div class="flex flex-column gap-12">
                                    <LabeledItem label="选择通话状态" size="small" :required="true" position="top">
                                        <div class="flex flex-1">
                                            <el-form-item prop="allowRecallStatus">
                                                <el-checkbox-group v-model="form.allowRecallStatus" style="width: 100%"
                                                                   :disabled="taskType === '2'">
                                                    <el-checkbox v-for="item in commonData.recallStatusDicts"
                                                                 :key="item.value" :label="item.label" :value="item.value"
                                                                 size="large" />
                                                </el-checkbox-group>
                                            </el-form-item>
                                        </div>
                                    </LabeledItem>
                                    <LabeledItem label="重呼次数" size="small" :required="true">
                                        <div class="flex flex-1">
                                            <el-form-item prop="maxRecallTimes" class="width-100">
                                                <el-select v-model="form.maxRecallTimes" placeholder="" size="large"
                                                           style="height: 44px" :disabled="taskType === '2'">
                                                    <el-option v-for="item in commonData.recallTimesDicts"
                                                               :key="item.value" :label="item.label" :value="item.value" />
                                                </el-select>
                                            </el-form-item>
                                        </div>
                                    </LabeledItem>

                                    <LabeledItem label="重呼间隔" size="small" :required="true">
                                        <div class="flex flex-1">
                                            <el-form-item prop="recallPeriodMin" class="width-100 recall-period-min">
                                                <el-input-number v-model="form.recallPeriodMin" :min="1" :max="1440"
                                                                 style="height: 44px; width: 100%" :controls="false"
                                                                 :disabled="taskType === '2'">
                                                    <template #suffix>
                                                        <span>分钟</span>
                                                    </template>
                                                </el-input-number>
                                            </el-form-item>
                                        </div>
                                    </LabeledItem>
                                </div>
                            </div>
                        </div>
                    </LabeledItem>
                    <div class="color-three-grey" v-if="!isEdit">提示：如需编辑外呼任务请前往外呼任务列表里操作</div>
                </div>
            </el-form>
        </Section>
        <div class="text-center all-padding-12">
            <el-button class="w-124 h-44" @click="handleClose">取消</el-button>
            <el-button type="primary" class="w-124 h-44" @click="submit(formRef)" :loading="loading"
                       :disabled="isOverflow || !isBalanceEnough">
                {{ isEdit ? '保存' : '提交' }}
            </el-button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.el-checkbox.el-checkbox--large {
    height: 44px;
}

.el-radio.el-radio--large {
    height: 44px;
}

.weekRange .el-checkbox {
    margin-right: 16px;
}

:deep(.el-select--large .el-select__wrapper) {
    height: 44px;
}

.task-form .el-form-item {
    margin-bottom: 0;
}

.recall :deep(.el-checkbox__label) {
    padding-left: 4px;
}

.recall :deep(.el-checkbox) {
    margin-right: 6px;
}

.recall .recall-period-min :deep(.el-input__inner) {
    text-align: left;
}

.switch-expire-date {
    :deep(.el-checkbox__inner) {
        border-radius: 12px;
    }
}

.cust-check-box {
    width: 16px;
    height: 16px;
    border-radius: 16px;
    border: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.cust-check-box:hover {
    border: 1px solid var(--el-color-primary);
}

.cust-check-box-checked {
    border: 1px solid var(--el-color-primary);
}
</style>
