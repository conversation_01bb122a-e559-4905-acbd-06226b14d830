import http from '@/axios'
import type { IUserAccountInfoResponse, IPageUserListResponse, IUserplatUserPageRequest, IUserplatUserPageResponse } from '@/types/user'

export default {
    userGetAccountInfo(): Promise<IUserAccountInfoResponse> {
        return http.get(`/api/zhenqi-system/user/get-account-info`)
    },
    userListByName(body: { nickname: string }): Promise<IPageUserListResponse> {
        return http.get(`/api/zhenqi-system/user/list-by-name`, {
            params: body,
            hideError: true,
        })
    },
    userplatUserPage(body: IUserplatUserPageRequest): Promise<IUserplatUserPageResponse> {
        return http.get(`/api/zhenqi-system/user/plat-user-page`, {
            params: body,
            hideError: true,
        })
    },
}
