<template>
    <el-popover 
        ref="popoverContent"
        trigger="click"
        width="350"
        :visible="showPop"
        placement="bottom-start"
        @hide="handleHide"
    >
        <template #reference>
            <div class="tag-border pointer lr-padding-16" :class="{'t-padding-8': props.type === 'list', 't-padding-10': props.type === 'detail'}" style="min-height: 40px;" @click="togglePopover">
                <div v-if="tagList && tagList.length && tagList.length > 0" :class="['display-flex', props.type === 'list' ? 'no-wrap' : 'flex-wrap']" >
                    <div class="r-margin-4 b-margin-10" v-for="(tag, index) in tagList" :key="tag.id" v-show="index < (props.type === 'list' ? 2 : tagList.length)">
                        <el-tag 
                            color="#FFFFFF"
                            size="large"
                            :style="{
                                color: tag.color, 
                                borderColor:'#ffffff',
                                backgroundColor: getRgbaColor(tag.color, 0.15),
                                minWidth: '55px',
                            }"
                            @mouseenter="showIcon(index)"
                            @mouseleave="hideIcon(index)"
                        >
                            <div class="flex-center" style="height: 18px;">
                                <span class="tag-tagName pointer"  >{{ tag.tagName }}</span>
                                <el-icon class="l-margin-4" size="16" @click.stop="unBindTag(tag.id)" v-show="tag.isShowIcon"><CircleCloseFilled /></el-icon>
                            </div>
                        </el-tag>
                    </div>
                    <a v-if="tagList.length > 2 && props.type === 'list'" class="font-20 pointer" >...</a>
                </div>
                <div v-else class="font-14 pointer color-primary" >
                    添加标签    
                </div>
            </div>
        </template>
        <div v-ck="handleClose">
            <TagBind 
                ref="tagBindRef"
                v-if="flag === 1" 
                @cancel="handleCancel" 
                @goToAdd="handleGoToAdd" 
                @confirmTags="handleConfirmTags"
                :tagInfos="tagList" 
                :unBindId="unBindId" 
                :leadId="props.crmId"
                :type="props.type"
            ></TagBind>
            <TagAdd v-if="flag === 2" @cancel="handleCancel" @backToBind="handleBackToBind" @addTag="handleAddTag"></TagAdd>
        </div>
    </el-popover>
</template>

<script lang='ts' setup>
import type { ITagInfo, ICrmUpdateParams, ITagAddResponseItem } from '@/types/lead'
import TagBind from './TagBind.vue'
import TagAdd from './TagAdd.vue'
import { onMounted, ref, computed, watch, nextTick } from 'vue'
import { ClickOutside, ElMessage } from 'element-plus'
import crmService from '@/service/crmService'

const getRgbaColor = (color: string, alpha: number) => {
    color = color.replace('#', '')
    const r = parseInt(color.substring(0, 2), 16)
    const g = parseInt(color.substring(2, 4), 16)
    const b = parseInt(color.substring(4, 6), 16)
    // console.log(`rgba(${r}, ${g}, ${b}, ${alpha})`)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

const emit = defineEmits(['refeshList'])
const props = defineProps<{
    tagInfos:ITagInfo[]
    type:string
    crmId:string
}>()

// 监听数组长度变化，不能监听数组的数据变化
const tagInfosLength = computed(() => props.tagInfos.length)
watch(tagInfosLength, () => {
    // console.log('props.tagInfos',props.tagInfos)
    tagList.value = props.tagInfos
})

const bindTagsParams = ref<ICrmUpdateParams>({
    updateTags: true,
    leadId: props.crmId,
})

const tagList = ref<ITagInfo[]>(props.tagInfos)

const vCk = ClickOutside
const flag = ref(1)
const showPop = ref(false)

// 用来控制是否展示关闭按钮
const showIcon= (index: number) => {
    tagList.value[index].isShowIcon = true
}
const hideIcon = (index: number) => {
    tagList.value[index].isShowIcon = false
}

const handleClose = () => {
    showPop.value = false
}
const handleCancel = () => {
    showPop.value = false

}
const tagBindRef = ref()
const togglePopover = () => {
    flag.value = 1
    nextTick(() => {
        // console.log('tagBindRef',tagBindRef.value)
        if (tagBindRef.value) {
            tagBindRef.value.getTagList(1)
        }
    })
    showPop.value = true
}

const handleHide = () => {
    console.log('handleHide')
    tagBindRef.value.resetCheckIds()
}

const handleGoToAdd = () => {
    flag.value = 2
}

const handleBackToBind = () => {
    flag.value = 1
    const tryInit = () => {
        if (tagBindRef.value) {
            tagBindRef.value.getTagList(1)
        } else {
            setTimeout(tryInit, 50)
        }
    }
    tryInit()
}

const bindIds = ref<string[]>([])
const unBindId = ref('')
const unBindTag = (id: string) => {
    showPop.value = false
    console.log('11111',id)
    // 缺少解绑接口 
    bindIds.value = tagList.value.filter(tag => tag.id !== id).map(tag => tag.id)
    console.log('bindIds',bindIds.value)
    bindTagsParams.value.crmTagIds = bindIds.value
    crmService.crmUpdate(bindTagsParams.value).then(() => {
        ElMessage.success('操作成功')
        unBindId.value = id
        // 需要在解绑成功之后，手动处理tagList，否则页面上面不会有变化
        tagList.value = tagList.value.filter(tag => tag.id !== id)
    })
    emit('refeshList')
}

const handleConfirmTags = (tags: ITagInfo[]) => {
    tagList.value = tags
    // console.log('tagList1111111111111111',tagList.value)
    showPop.value = false
    emit('refeshList')
}

const handleAddTag = (tagInfo: ITagAddResponseItem) => {
    // console.log('tagId',tagId)
    bindIds.value = tagList.value.map(tag => tag.id)
    bindIds.value.push(tagInfo.id)
    // console.log('bindIds',bindIds.value)
    bindTagsParams.value.crmTagIds = bindIds.value
    crmService.crmUpdate(bindTagsParams.value).then(() => {
        ElMessage.success('操作成功')
        tagList.value.push(tagInfo)
    }).finally(() => {
        showPop.value = false
        // flag.value = 1
    })
    emit('refeshList')
}

onMounted(() => {
})

</script>

<style lang='scss' scoped>
.tag-tagName {
    display: inline-block;
    max-width: 60px;      
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tag-border:hover{
    border: 1px solid var(--border-color);
}
</style>
