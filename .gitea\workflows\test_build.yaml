name: 测试环境打包
run-name: ${{ gitea.actor }} 执行测试环境打包
on:
  workflow_dispatch:
    inputs:
      node-version:
        description: "选择Node版本"
        required: true
        default: "test"
        type: choice
        options:
          - 20.19.2
jobs:
  test-build:
    runs-on: [runner-1.2]
    env:
      VERSION: 202512221550
      NODE_VERSION: ${{ gitea.event.inputs.node-version }}
      RELEASE_TAG: package
      OSS_UPLOAD_PATH: oss://shuzu-static-dev/xtpt/
    steps:
      - name: 开始执行流水线
        run: |
          echo "➡️ 开始执行流水线-当前版本${VERSION}"
      - name: 安装Node服务
        run: |
          echo "➡️ 开始安装Node服务"
          set -eux
          CACHE_FILE="/runner-cache/node/node-v${NODE_VERSION}-linux-x64.tar.xz"
          if [ -f "$CACHE_FILE" ]; then
            echo "✅ 使用本地缓存安装 Node.js v${NODE_VERSION}"
            tar -xJf "$CACHE_FILE" -C /usr/local --strip-components=1
          else
            echo "⚠️  本地缓存不存在，从网络下载..."
            curl -fsSL "https://npmmirror.com/mirrors/node/v${NODE_VERSION}/node-v${NODE_VERSION}-linux-x64.tar.xz" -o node.tar.xz
            tar -xJf node.tar.xz -C /usr/local --strip-components=1
          fi
          node -v
          npm -v
          echo "✅ 成功安装Node服务"
      - name: 拉取代码
        uses: https://gitee.com/youhongxing/checkout.git@v4
      - name: 安装依赖
        run: |
          echo "➡️ 开始安装项目依赖"
          git branch --show-current
          git log -1
          npm config set registry https://registry.npmmirror.com/
          npm install
          echo "✅ 项目依赖安装成功"
      - name: 项目打包
        run: |
          echo "➡️ 开始项目打包"
          npm run build:test
          echo "✅ 项目打包成功"
      - name: 压缩打包文件
        run: |
          echo "➡️ 开始压缩打包文件-${{ gitea.run_id }}"
          rm -rf ./dist/js/*.js.map
          cd ./dist
          tar -czvf ../${RELEASE_TAG}.tar.gz *
          echo "✅ 压缩打包文件结束"
      - name: 设置OSS参数
        run: |
          echo "➡️ 开始设置OSS参数"
          echo "${{ secrets.OSS_ACCESS_KEY_ID }}"
          ossutil64 config -e oss-cn-shanghai.aliyuncs.com \
            -i "${{ secrets.OSS_ACCESS_KEY_ID }}" \
            -k "${{ secrets.OSS_ACCESS_KEY_SECRET }}"
          echo "✅ 设置OSS参数结束"
      - name: 上传项目压缩包
        run: |
          echo "➡️ 开始上传项目压缩包"
          ossutil64 cp ./${RELEASE_TAG}.tar.gz ${OSS_UPLOAD_PATH} --update
          echo "✅ 上传项目压缩包结束"
      - name: 触发部署流水线
        run: |
          echo "➡️ 触发部署流水线"
          RESPONSE=$(curl --silent --show-error \
            --write-out "HTTP_STATUS:%{http_code}" \
            --header "Content-Type: application/json" \
            --request POST \
            --data "{\"build_tag\":\"${RELEASE_TAG}\"}" \
            "${{ secrets.ALIYUN_WEBHOOK_URL }}")

          HTTP_BODY=$(echo "$RESPONSE" | sed -e 's/HTTP_STATUS\:.*//g')
          HTTP_STATUS=$(echo "$RESPONSE" | tr -d '\n' | sed -e 's/.*HTTP_STATUS://')

          echo "HTTP 状态码: $HTTP_STATUS"
          echo "响应内容: $HTTP_BODY"

          if [[ "$HTTP_STATUS" =~ ^2[0-9]{2}$ ]]; then
            echo "✅ HTTP 请求成功"
            
            # 进一步检查响应体中的成功标志
            if echo "$HTTP_BODY" | grep -q '"successful":true'; then
              echo "✅ 部署流水线触发成功"
            else
              echo "❌ 阿里云返回错误: $HTTP_BODY"
              exit 1  # 使步骤失败
            fi
            
          else
            echo "❌ HTTP 请求失败，状态码: $HTTP_STATUS"
            echo "错误响应: $HTTP_BODY"
            exit 1  # 使步骤失败
          fi

          echo "✅ 触发流水线结束"
