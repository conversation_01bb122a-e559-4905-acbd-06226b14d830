<script lang="ts" setup>
import { computed, inject, onMounted, provide, ref, watch } from 'vue'
import { useStore } from 'vuex'
import aicService from '@/service/aicService'
import CheckboxBtn from '@/components/enterprise/ui/CheckboxBtn.vue'
import RegionMultipleSelect from '@/components/enterprise/region/RegionMultipleSelect.vue'
import { formatRegionLabel } from '@/utils/enterprise/region'
import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams, Region } from '@/types/aic'
import type { RootState } from '@/types/store'
import type { IPushToGlobal } from '@/types/company'

// ====================== Interfaces & Types ======================
interface Option {
    value: string
    label: string
}

export interface ParentMethods {
    postSelectedRegions: (regions: Region[]) => void
}

// ====================== Props & Injections ======================
const props = defineProps<{
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
}>()

const pushToGlobal = inject<(v: IPushToGlobal) => void>('pushToGlobal', () => {})

// ====================== Refs & Reactive State ======================
const selectedList = ref<string[]>([])
const selectedRegions = ref<Region[]>([])
const regionMultipleSelectRef = ref<{
    reset: () => void
}>()
const config = ref<IAicConditionData | null>(null)

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

const isUnlimited = computed(() => {
    const list = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    return list.length === 0
})

const isHaschanged = computed(() => {
    const target = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    return target.length < selectedList.value.length
})

const province = computed(() => {
    if (!config.value) return []
    return convertFirstLevelRegionsToOptions(config.value?.area)
})

// ====================== Methods ======================
/**
 * Gets the first letter of pinyin (uppercase)
 */
function getFirstLetter(pinyin: string): string {
    return pinyin.charAt(0).toUpperCase()
}

function sortRegionsByPinyin(regions: Region[]): Region[] {
    return [...regions].sort((a, b) => {
        const letterA = getFirstLetter(a.pinyin)
        const letterB = getFirstLetter(b.pinyin)
        return letterA.localeCompare(letterB)
    })
}

function convertFirstLevelRegionsToOptions(regions: Region[]): Option[] {
    const sortedRegions = sortRegionsByPinyin(regions)
    return sortedRegions.map((region) => ({
        value: region.value,
        label: region.label,
    }))
}

const setUnlimited = () => {
    selectedList.value = []
    resetParams()
}

const handlePostRegions = (regions: Region[]) => {
    selectedRegions.value = regions
}

const onChange = (regions: string[]) => {
    if (regions.length === 0) {
        resetParams()
    }
    selectedList.value = regions

    if (!config.value) return

    const targets = config.value.area.filter((e) => regions.includes(e.value))
    pushParams(targets.reverse())
}

const pushParams = (regions: Region[]) => {
    const tempArray: INormalFilterParams[] = []

    regions.forEach((region) => {
        const params: INormalFilterParams = {
            label: formatRegionLabel(region),
            value: region.value,
            category: props.data.name,
            categoryKey: props.data.key,
            type: props.data.dataType,
        }
        tempArray.push(params)
    })

    if (!pushToGlobal) return
    pushToGlobal(tempArray)
}

const uncheck = () => {
    const target = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    const arr = target.map((e) => e.value)
    selectedList.value = arr
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
    }
}

const resetParams = () => {
    if (!pushToGlobal) return
    pushToGlobal({ categoryKey: props.data.key })
}

// ====================== Parent Methods ======================
const parentMethods: ParentMethods = {
    postSelectedRegions: (region: Region[]) => handlePostRegions(region),
}

provide<ParentMethods>('parentMethods', parentMethods)

// ====================== Watchers ======================
watch(
    () => isUnlimited.value,
    (value) => {
        if (value) {
            setUnlimited()
        }
    }
)

watch(
    () => isHaschanged.value,
    (value) => {
        if (value) {
            uncheck()
        }
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getStaticConfig()
})
</script>

<template>
    <div class="flex top-bottom-center">
        <div class="flex flex-row">
            <div class="w-112">
                <div class="lh-24 font-16 color-black">{{ props.data.name }}：</div>
            </div>
            <div class="flex flex-row top-bottom-center gap-24 flex-wrap flex-1">
                <CheckboxBtn :onChange="setUnlimited" :checked="isUnlimited" />
                <RegionMultipleSelect
                    ref="regionMultipleSelectRef"
                    :list="province"
                    :onChange="onChange"
                    placeholder="省份"
                    :default-value="selectedList"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
