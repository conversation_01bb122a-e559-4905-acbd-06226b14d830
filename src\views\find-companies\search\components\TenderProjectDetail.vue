<template>
    <div>
        <el-dialog title="项目详情"  width="1200px" v-model="showDialog" @close="closeDialog">
            <div>
                <div v-html="detailHtml"></div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const tenderProjectDetail = ref({
    'errCode': 0,
    'data': '<html><body><div><div>   服务器及国产化软件采购采购公告  </div><div></body></html>',
    'errMsg': ''
})
const detailHtml = ref('')
detailHtml.value = tenderProjectDetail.value.data ? tenderProjectDetail.value.data : ''

const props = defineProps({
    showTenderProjectDetail: {
        type: Boolean,
        required: true,
    },
})

const showDialog = ref(props.showTenderProjectDetail)
const closeDialog = () => {
    emit('update:showTenderProjectDetail', false)
}

watch(
    [() => props.showTenderProjectDetail, showDialog],
    ([newShowTenderProjectDetail, newShowDialog]) => {
        // 同步 props.showTenderProjectDetail 和 showDialog
        showDialog.value = newShowTenderProjectDetail
        // 将 showDialog 的变化传递给父组件
        emit('update:showTenderProjectDetail', newShowDialog)
    }
)

// // 监听props的变化，同步到本地状态
// watch(
//     () => props.showTenderProjectDetail,
//     (newValue) => {
//         showDialog.value = newValue
//     }
// )

// // 监听对话框的显示状态变化，并通过emit将新状态传递给父组件
// watch(
//     showDialog,
//     (newValue) => {
//         emit('update:showTenderProjectDetail', newValue)
//     }
// )

// 定义emit对象
const emit = defineEmits(['update:showTenderProjectDetail'])

</script>

<style lang="scss" scoped>

</style>