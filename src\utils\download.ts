import type { AxiosResponse } from 'axios'

/**
 * 下载文件函数
 * @param {Object} response - 响应对象
 * @param {String} defaultName - 默认文件名（可选）
 */
export const downloadFile = (response: AxiosResponse<Blob>, defaultName: string = 'file.xlsx') => {
    // 从响应头获取文件名，如果没有则使用默认名
    const contentDisposition = response.headers['content-disposition']
    let fileName = defaultName

    if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename=(.*)/)
        if (fileNameMatch && fileNameMatch.length > 1) {
            // 解码URI编码的文件名
            fileName = decodeURIComponent(fileNameMatch[1])
        }
    }

    // 获取响应数据（Blob数据）
    const blob = new Blob([response.data], {
        type: response.headers['content-type'] || 'application/vnd.ms-excel',
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)

    // 触发点击下载
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
}

/**
 * 下载文件函数-非Axios响应
 * @param {Object} response - 响应对象
 * @param {String} defaultName - 默认文件名（可选）
 */
export const downloadZipFile = (response: Blob, defaultName: string = '导出文件.zip') => {
    // 从响应头获取文件名，如果没有则使用默认名
    const fileName = defaultName

    // 获取响应数据（Blob数据）
    const blob = new Blob([response], {
        type: response.type || 'application/zip',
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)

    // 触发点击下载
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
}
