<template>
    <el-row class="margin-top-20" :gutter="10" v-if="!loading">
        <el-col v-for="item in reportList" :key="item.id" :span="span">
            <div
                class="border all-padding-16 b-margin-20 display-flex flex-column report-item border-radius-8 relative"
                :style="{
                    // backgroundImage: `url('../../../assets/report/'${item.key}${item.isBuy ? '-pay.png' : '.png'})`,
                    backgroundImage: `url(${getReportImgUrl(item)})`,
                    borderColor: `${item.isBuy ? 'rgba(50, 100, 246, 0.2)' : ''}`,
                }"
            >
                <div class="font-16 font-600 color-black flex-1">
                    <div>{{ item.name }}</div>
                    <div class="font-14 color-three-grey t-margin-8">{{ item.desc }}</div>
                </div>

                <div class="font-14 font-grey display-flex top-bottom-center btns">
                    <!-- item.search_key == 'fpbg'&&isCollectInvoice 是因为用户先采集了税务报告的发票，此时用户不应再次授权来扣费，可以直接扣费 -->
                    <animation-btn
                        :btnTxt="'下载报告'"
                        :svgUrl="'icon-a-huaban85'"
                        :color="'#3264F6'"
                        :acColor="'#fff'"
                        @click="downloadReport(item)"
                    ></animation-btn>
                    <div v-if="item.key == 'fpbg' || item.key == 'swbg'">
                        <div>
                            <animation-btn
                                :svgUrl="'icon-a-huaban9'"
                                :color="'#3264F6'"
                                :acColor="'#fff'"
                                :btnTxt="'授权票税数据'"
                                @click="handleGetAuthUrl(item)"
                            ></animation-btn>
                        </div>
                    </div>
                </div>
                <div class="absolute is-pay-btn" v-if="item.isBuy">已购</div>
            </div>
        </el-col>
    </el-row>
    <el-skeleton v-else :rows="5" animated />

    <el-dialog v-model="collectDialogVisible" width="700px" v-if="collectDialogVisible" @close="closeCollectDialog">
        <template #header="{ titleId, titleClass }">
            <div class="display-flex space-between padding-top-5 top-bottom-center tb-margin-0">
                <div :id="titleId" :class="titleClass">企业税票授权</div>
                <div class="margin-right-36 font-size-14 pointer">
                    <el-popover :visible="showQrCode" placement="top" :width="220">
                        <div>
                            <vue-qr
                                class="border t-margin-8"
                                ref="qrcode"
                                :title="collectUrl"
                                :text="collectUrl"
                                :size="180"
                            ></vue-qr>
                        </div>
                        <div class="margin-top-16">
                            <el-button type="primary" @click="copyUrl">复制链接发送给客户填写</el-button>
                        </div>
                        <template #reference>
                            <div @click="showScanCode" class="display-flex top-bottom-center pointer gap-10">
                                <el-icon>
                                    <FullScreen />
                                </el-icon>
                                面对面扫码
                            </div>
                        </template>
                    </el-popover>
                </div>
            </div>
        </template>

        <iframe id="myIframe" class="width-100" style="height: 100vh" :src="collectUrl" frameborder="0"></iframe>
    </el-dialog>

    <el-dialog v-model="showBuyDialog" title="请选择使用权益" width="460px" @close="close">
        <template #header="{ titleId }">
            <div class="my-header">
                <div :id="titleId" class="font-bold font-16">请选择使用权益</div>
            </div>
        </template>
        <div v-loading="!dataList.length">
            <div
                class="border-radius-6 border all-padding-16 pointer by-box t-margin-10 display-flex space-between"
                :class="choseService.id == service.id ? 'ck-by-box' : ''"
                @click="choseService = service"
                v-for="service in dataList"
                :key="service.id"
            >
                <div>
                    <div class="font-16 font-bold">{{ service.serviceName }}x{{ service.totalAmount }}</div>
                    <div class="font-four-level t-margin-10">
                        过期时间 : {{ moment(service.endTime).format('YYYY-MM-DD HH:mm:ss') }}
                    </div>
                </div>
                <div class="display-flex top-bottom-center">剩余：{{ service.remainingAmount }}份</div>
            </div>
        </div>
        <div class="t-margin-16 tip-box">
            <div class="color-grey font-14">使用说明</div>
            <div class="t-margin-6 color-grey font-12">
                1.权益自使用后，有效期为1年，到期后自动失效，无法再生成和下载报告；
            </div>
            <div class="t-margin-6 color-grey font-12">
                2.企业发票数据分析报告生成和下载，需要企业先完成发票数据授权
            </div>
            <div class="t-margin-6 color-grey font-12">
                3.企业财税数据分析报告生成和下载，需要企业先完成发票和纳税数据授权
            </div>
            <div class="t-margin-6 color-grey font-12">4.权益使用后不可取消或转让</div>
            <div class="t-margin-6 color-grey font-12">5.若有疑问可咨询您的客户经理</div>
        </div>
        <div class="display-flex t-margin-20" v-if="choseService">
            <el-button type="primary" class="width-100" @click="handleBuy">立即使用</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, inject, getCurrentInstance } from 'vue'
import type { Ref } from 'vue'
import type { ILeadData } from '@/types/lead'
import type { ReportItem } from '@/types/collect'
import type { IServiceOrderResponseItem } from '@/types/order'
import collectService from '@/service/collectService'
import reportService from '@/service/reportService'

import vueQr from 'vue-qr/src/packages/vue-qr.vue'
import orderService from '@/service/orderService'
import { ElLoading, ElMessageBox, ElMessage } from 'element-plus'

import gqbg from '@/assets/report/gqbg.png'
import gqbgPay from '@/assets/report/gqbg-pay.png'
import fpbg from '@/assets/report/fpbg.png'
import fpbgPay from '@/assets/report/fpbg-pay.png'
import swbg from '@/assets/report/swbg.png'
import swbgPay from '@/assets/report/swbg-pay.png'
import crmService from '@/service/crmService'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

const imgList: Record<string, string> = {
    gqbg,
    gqbgPay,
    fpbg,
    fpbgPay,
    swbg,
    swbgPay,
}

const dataList: Ref<IServiceOrderResponseItem[]> = ref([])

const choseService: Ref<IServiceOrderResponseItem> = ref({} as IServiceOrderResponseItem)
const choseXsService: Ref<IServiceOrderResponseItem> = ref({} as IServiceOrderResponseItem)
const getReportImgUrl = (item: ReportItem) => {
    return imgList[item.key + (item.isBuy ? 'Pay' : '')]
}

const reportList: Ref<ReportItem[]> = ref([])
const loading: Ref<boolean> = ref(true)
defineProps({
    span: {
        type: Number,
        default: 12,
    },
})

const getReportList = () => {
    loading.value = true

    collectService
        .getReportList({ socialCreditCode: crmDetail.value.socialCreditCode })
        .then((res) => {
            reportList.value = res
        })
        .finally(() => {
            loading.value = false
        })
}

const getReportUrl = (item: ReportItem) => {
    const loading = ElLoading.service({
        lock: true,
        text: '获取链接中',
        background: 'rgba(0, 0, 0, 0.7)',
    })
    collectService
        .getReportUrl({ socialCreditCode: crmDetail.value.socialCreditCode, deductType: item.key })
        .then((res) => {
            window.open(res.url, '_blank')
        })
        .finally(() => {
            loading.close()
        })
}

const showBuyDialog: Ref<boolean> = ref(false)

const choseReportItem: Ref<ReportItem> = ref({} as ReportItem)
const downloadReport = (item: ReportItem) => {
    if (!item.ableDownload) {
        ElMessage({
            message: '您还未授权相关数据，请授权后点击下载',
            type: 'error',
        })
        return
    }

    choseReportItem.value = item

    if (item.isBuy === true) {
        getReportUrl(item)
    } else {
        orderService
            .orderServiceOrderPage({
                serviceKey: item.key,
                page: 1,
                pageSize: 99,
            })
            .then((res) => {
                let list = res.data
                dataList.value = list
                    .filter((item) => {
                        return item.remainingAmount > 0 && item.status
                    })
                    .sort((a, b) => {
                        return a.endTime - b.endTime
                    })
                if (!dataList.value.length) {
                    ElMessageBox.alert('暂无可使用的权益,请联系400-100-0086', '提示')
                    return
                }
                choseService.value = dataList.value[0]
                showBuyDialog.value = true
            })
    }
}

const close = () => {
    showBuyDialog.value = false
}

const handleBuy = () => {
    orderService
        .orderBuyLegal({
            orderId: choseService.value.serviceOrderId,
            serviceKey: choseReportItem.value.key,
            socialCreditCode: crmDetail.value.socialCreditCode,
            companyName: crmDetail.value.companyName,
        })
        .then(() => {
            showBuyDialog.value = false
            getReportList()
            ElMessage({
                message: '购买成功',
                type: 'success',
            })
        })
}

const collectDialogVisible: Ref<boolean> = ref(false)

const collectUrl: Ref<string> = ref('')

const requestId: Ref<string> = ref('')

let ti = 0

const handleGetAuthUrl = async (item: ReportItem) => {
    showQrCode.value = false

    const confirmOrderUsage = async () => {
        await ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用？', '确认', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        })
        const res = await orderService.orderServiceOrderPage({
            serviceKey: 'xs',
            page: 1,
            pageSize: 99,
            status: 'NORMAL',
        })
        res.data.filter((item) => {
            return item.remainingAmount > 0
        }).sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime())
        console.log(res.data)
        choseXsService.value = res.data[0]
        await orderService.orderBuyLegal({
            orderId: choseXsService.value.serviceOrderId,
            serviceKey: 'xs',
            socialCreditCode: crmDetail.value.socialCreditCode,
            companyName: crmDetail.value.companyName,
        })
        await confirmReportGeneration(item)
    }

    const confirmReportGeneration = async (item: ReportItem) => {
        if (item.isBuy) {
            getAuthUrl(item)
            return
        }

        await ElMessageBox.confirm('本次授权成功后,会扣除一份报告权益,是否确认生成报告链接?', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        })
        getAuthUrl(item)
    }
    await crmService
        .getCrmList({
            companyName: crmDetail.value.companyName,
            page: 1,
            pageSize: 1,
        })
        .then(async (res) => {
            if (!res.data![0].isBuy) {
                await confirmOrderUsage()
            } else {
                await confirmReportGeneration(item)
            }
        })
}

const getAuthUrl = (item: ReportItem) => {
    const loading = ElLoading.service({
        lock: true,
        text: '获取链接中',
        background: 'rgba(0, 0, 0, 0.7)',
    })
    let c = crmDetail.value
    collectService
        .getAuthUrl({
            socialCreditCode: c.socialCreditCode,
            deductType: item.key,
            companyId: c.companyId,
            companyName: c.companyName,
        })
        .then((res) => {
            collectUrl.value = res.url
            requestId.value = res.requestId
            collectDialogVisible.value = true
        })
        .finally(() => {
            loading.close()
        })
}

const showQrCode: Ref<boolean> = ref(false)

const showScanCode = () => {
    showQrCode.value = !showQrCode.value
    if (showQrCode.value) {
        getCollectRes()
        // 获取扫码状态
    } else {
        clearInterval(ti)
    }
}
const getCollectRes = () => {
    ti = setInterval(() => {
        reportService
            .collectPage({
                requestId: requestId.value,
            })
            .then((res) => {
                if (res.data[0].status !== 'WAITING') {
                    //用户已授权成功,预计30分钟授权数据完成,期间请勿登录税局,如登录需再次发起授权!
                    collectDialogVisible.value = false
                    clearInterval(ti)
                }
            })
    }, 3000)
}

const copyUrl = () => {
    const textArea = document.createElement('textarea')

    // 设置内容并添加到文档中
    textArea.value = collectUrl.value.trim()
    document.body.appendChild(textArea)

    // 选中并拷贝内容
    textArea.select()
    document.execCommand('copy')

    // 删除临时的 textarea
    document.body.removeChild(textArea)

    // 可选: 提示用户内容已被复制
    ElMessage({
        message: '复制成功',
        type: 'success',
    })
}
const closeCollectDialog = () => {
    clearInterval(ti)
    collectDialogVisible.value = false
}
onMounted(() => {
    getReportList()
})
</script>

<style lang="scss" scoped>
.report-item {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: right;
    height: 115px;

    .btns {
        height: 24px;
    }

    .is-pay-btn {
        background-color: var(--main-blue-);
        right: 0;
        top: 0;
        color: var(--main-white);
        padding: 5px 10px;
        border-radius: 0 4px 0 4px;
    }
}

.real-price {
    color: var(--color-warning-);
}

.by-box {
    border-width: 2px;
}

.ck-by-box {
    border: 2px solid var(--main-blue-);
}

.by-box:hover {
    border: 2px solid var(--main-blue-);
}

.el-dialog__title {
    font-weight: bold;
}

.el-dialog__header {
    text-align: center;
}

.color-grey {
    color: var(--two-grey);
}

.tip-box {
    line-height: 20px;
}
</style>
