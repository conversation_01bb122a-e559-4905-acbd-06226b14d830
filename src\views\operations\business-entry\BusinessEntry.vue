<template>
    <div style="background-color: #f7f7f7; ">
        <div style=" background-color: #fff; padding: 16px">
            <searchBox
                :searchOptionKey="'BUSINESS_ENTRY_SEARCH_OPTIONS'"
                @updateSearchParams="updateSearchParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <el-table
                ref="tableList"
                :data="tableData"
                style="width: 100%;height: 500px;"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="公司名称" prop="companyName" min-width="180"></el-table-column>
                <el-table-column label="姓名" prop="name"></el-table-column>
                <el-table-column label="公司税号" prop="socialCreditCode"></el-table-column>
                <el-table-column label="资金用途" prop="zjyt"></el-table-column>
                <el-table-column label="申请额度（万元）" prop="sqed" min-width="100"></el-table-column>
                <el-table-column label="手机号" prop="mobile"></el-table-column>
                <el-table-column label="创建时间" prop="createDate">
                    <template #default="scope">
                        {{ scope.row.createDate ? moment(scope.row.createDate).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="创建人" prop="nickname"></el-table-column>
            </el-table>
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix> 
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import searchBox from '@/components/common/SearchBox.vue'
import type { IBusinessEntryListParams, IBusinessEntryListResponseItem } from '@/types/business'
import crmService from '@/service/crmService'
import systemService from '@/service/systemService'


type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}
const searchConfig = ref<CustomConfig>()
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const tableLoading = ref(false)
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

const queryParams = ref<IBusinessEntryListParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
})

const updateSearchParams = (params: IBusinessEntryListParams) =>{
    queryParams.value = params
    search(queryParams.value)
}
const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    search(queryParams.value)
}
const tableData = ref<IBusinessEntryListResponseItem[]>([])
const search = (Params: IBusinessEntryListParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    crmService.crmJrApplyList(Params).then((res) => {
        console.log('crmJrApplyList',res)
        tableData.value = res.data
        totalNum.value = res.total
    }).finally(() => {
        tableLoading.value = false
    })
}

onMounted(() => {
    systemService.userGetUserByScopeData('collect').then(response => {
        searchConfig.value = {
            ...searchConfig.value,
            userId:response.data.map(item => ({ 
                value: item.id,
                label: item.nickname 
            }))
        }
    })
    search(queryParams.value)
})
</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>