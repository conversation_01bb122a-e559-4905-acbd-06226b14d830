<script lang="ts" setup>
import { ref, watch } from 'vue'
import * as echarts from 'echarts'
import type { PieChartOption } from '@/types/echart'

type AllData = {
    [key: string]: string[]
}
const props = defineProps<{
    data: AllData
}>()

const chartRef = ref(null)
const pieOption = ref<PieChartOption>({
    legend: {
        icon: 'rect', // circle 圆形标记
        bottom: '0',
        left: 'center',
        type: 'scroll',
    },
    series: [
        {
            name: '登记状态分布',
            type: 'pie',
            radius: ['40%', '60%'],
            center: ['50%', '45%'], //[水平位置, 垂直位置]
            emphasis: {
                //高亮状态配置
                label: {
                    show: true,
                    fontSize: 14,
                },
            },
            label: {
                show: false,
                position: 'outside', // 标签显示在扇形外部
                formatter: '{d}%', // {b} 表示数据名称，{d} 表示百分比
            },
            labelLine: {
                //标签引导线配置
                show: true,
                // length: 30, // 视觉引导线第一段的长度
                // length2: 100, // 视觉引导线第二段的长度
                smooth: false, // 是否平滑视觉引导线
                lineStyle: {
                    width: 2,
                },
            },
            itemStyle: {
                borderRadius: 8, // 设置圆角
                borderColor: '#fff', // 可以设置边框颜色
                borderWidth: 2, // 设置边框宽度
            },
            data: [],
        },
    ],
    tooltip: {
        trigger: 'item',
    },
})
const setChart = () => {
    const myChart = echarts.init(chartRef.value)
    myChart.setOption(pieOption.value)
}
const showChart = ref(true)
const dealData = (data: AllData) => {
    console.log('showChart', data['1319582581130240'], data['1320350553998336'])
    if (data['1319582581130240'] && data['1320350553998336']) {
        showChart.value = true
        let names = data['1319582581130240']
        let values = data['1320350553998336']
        const result = ref()
        if (typeof names === 'string' && typeof values === 'string') {
            result.value = [{
                name: names,
                value: values
            }]
        } else if (Array.isArray(names) && Array.isArray(values)) {
            result.value = names.map((name, index) => ({
                name: name,
                value: values[index],
            }))
        } else {
            showChart.value = false
            return
        }
        pieOption.value.series[0].data = result
        setChart()
    } else {
        showChart.value = false
    }
}
watch(
    () => props.data,
    (newVal) => {
        dealData(newVal)
    }
)
</script>
<template>
    <div class="width-100 height-100 back-color-white border-radius-8 all-padding-16 display-flex flex-column">
        <div class="font-16 color-black font-weight-500">登记状态分布</div>
        <div class="flex-1">
            <div v-if="showChart" class="width-100 height-100" ref="chartRef"></div>
            <div v-else class="width-100 height-100 display-flex center">
                <div class="w-260 h-260 no-data"></div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.no-data {
    background: url('@/assets/images/no-chart-data.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: center;
}
</style>
