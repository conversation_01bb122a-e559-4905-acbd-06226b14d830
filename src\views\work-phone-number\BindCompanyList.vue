<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7;">
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <div class="all-padding-16">
                <SearchBox
                    :searchOptionKey="searchOptionKey"
                    @updateSearchParams="updateSearchParams"
                    :customConfig="searchConfig"
                >
                </SearchBox>
            </div>
        </div>
        <div ref="tableContentRef" class="l-padding-16 t-padding-16 r-padding-16 table-content" style="background-color: #fff; box-sizing: border-box">
            <div class="back-color-white border-box flex-grow-1">
                <div class="t-margin-16">
                    <BindCompanyTable :tableDataList="tableDataList" :tableLoading="tableLoading" :tableHeight="tableHeight" :refresh="search" :isDialog="isDialog"/>
                    <el-affix target=".table-content" position="bottom" :z-index="2">
                        <div class="pagination-bar">
                            <el-pagination
                                v-model:currentPage="pageInfo.page"
                                v-model:page-size="pageInfo.size"
                                :total="pageInfo.total"
                                :page-sizes="[20, 40, 60, 100]"
                                layout="total, sizes, prev, pager, next, jumper"
                                @change="pageChange"
                            />
                        </div>
                    </el-affix>
                </div>
               
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import SearchBox from '@/components/common/SearchBox.vue'
import { ref, reactive, onMounted,computed } from 'vue'
import BindCompanyTable from './components/BindCompanyTable.vue'
import type { IBindCompanyTableParams, IBindCompanyTableDataItem } from '@/types/worknumber'
import { useFixedActionBar } from '@/js/use-fixed-action-bar'
import systemService from '@/service/systemService'
import { useStore } from 'vuex'
import tamService from '@/service/tamService'

const isDialog = ref(false)
const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})
const tableContentRef=ref<HTMLDivElement | null>(null)
useFixedActionBar(tableContentRef)
const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value ) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}

const pageInfo = reactive({ 
    page: 1, 
    size: 20, 
    total: 0 
})
const updateSearchParams = (params: IBindCompanyTableParams) => {
    queryParams.value = params
    search()
}
const queryParams = ref<IBindCompanyTableParams>({
    page: 1,
    size: 20,
})

const pageChange = (currentPage: number, size: number) => {
    queryParams.value.page = currentPage
    queryParams.value.size = size
    search()
}
const tableDataList = ref<IBindCompanyTableDataItem[]>([])
const tableLoading = ref(false)
const search = () => {
    queryParams.value.page = pageInfo.page
    queryParams.value.size = pageInfo.size
    tableLoading.value = true
    console.log('search')
    tamService.bsyBindEntList(queryParams.value).then((response) => {
        tableDataList.value = response.records
        pageInfo.total = response.total
    }).finally(() => {
        tableLoading.value = false
    })
}
type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const searchConfig = ref<CustomConfig>()
const searchOptionKey = ref('BIND_COMPANY_LIST_OPTIONS')
onMounted(() => {
    getTableHeight()
    search()
    if (isAdmin.value) {
        searchOptionKey.value = 'BIND_COMPANY_LIST_OPTIONS_FORADMIN'
        systemService.tenantList().then((response) => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantIds: response.map((item) => ({
                    label: item.name,
                    value: item.id,
                })),
            }
        })
    }
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>
