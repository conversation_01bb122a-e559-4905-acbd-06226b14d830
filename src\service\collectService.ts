import http from '@/axios'

import type { ReportItem } from '@/types/collect'


export default {
    getReportList(body: { socialCreditCode: string }): Promise<ReportItem[]> {
        return http.get(`/api/zhenqi-report/report/get-report-list`, {
            params: body
        })
    },
    getReportUrl(body: { socialCreditCode: string, deductType: string }): Promise<{ url: string, isBuy: boolean }> {
        return http.get(`/api/zhenqi-report/report/get-report-url`, {
            params: body
        })
    },
    getAuthUrl(body: { companyId: string, companyName: string, deductType: string, socialCreditCode: string }): Promise<{
        url: string
        requestId: string
    }> {
        return http.get(`/api/zhenqi-report/collect/get-auth-url`, {
            params: body
        })
    },
}