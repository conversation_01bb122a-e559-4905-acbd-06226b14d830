<script lang="ts" setup>
import { onMounted, ref, reactive, computed, watch } from 'vue'

import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import homeService from '@/service/homeService'
import aicService from '@/service/aicService'

import type { ModelRes, Model001ResInfo } from '@/types/home'
import type { IAicConditionDataOptionItem } from '@/types/aic'
import type { ITenant } from '@/types/user'
import * as echarts from 'echarts'

// interface IindustryItem {
//     label: string
//     val: string
//     name: string
//     num: string
//     pinyin: string
//     children?: IindustryItem[]
// }
type IListItem = {
    name: string
    value: number | string
}



const chartRef = ref(null)
const props = defineProps<{
    data: ModelRes[]
    tenantInfo: ITenant | undefined
}>()
const hasChartData = ref(true)

const chartData = ref<ModelRes[]>([])

watch(
    () => props.data,
    (newVal) => {
        chartData.value = JSON.parse(JSON.stringify(newVal))
    }
)

const transformedData = computed(() => {
    if (!chartData.value.length) return [] as Model001ResInfo[]
    return chartData.value.map((item) => {
        const [title, value] = Object.entries(item)[0]
        return {
            title,
            ...value,
        } as Model001ResInfo
    })
})

const option = reactive({
    legend: {
        icon: 'rect', // circle 圆形标记
        bottom: '5%',
        left: 'center',
    },
    series: [
        {
            name: '科技资质名单',
            type: 'pie',
            radius: ['40%', '55%'], //环形图（内径，外径）
            avoidLabelOverlap: false, // 是否避免标签重叠
            emphasis: {
                //高亮状态配置
                label: {
                    show: true,
                    fontSize: 14,
                },
            },
            label: {
                show: true,
                position: 'outside', // 标签显示在扇形外部
                formatter: '{d}%', // {b} 表示数据名称，{d} 表示百分比
            },
            labelLine: {
                //标签引导线配置
                show: true,
                // length: 30, // 视觉引导线第一段的长度
                // length2: 100, // 视觉引导线第二段的长度
                smooth: true, // 是否平滑视觉引导线
                lineStyle: {
                    width: 2,
                },
            },
            itemStyle: {
                borderRadius: 8, // 设置圆角
                borderColor: '#fff', // 可以设置边框颜色
                borderWidth: 2, // 设置边框宽度
            },
            data: <IListItem[]>[{ value: '', name: '' }],
        },
    ],
})



const cascaderProps = {
    // 设置显示字段
    label: 'label', // 自定义显示字段为 label
    value: 'value', // 自定义选中值字段为 value
    children: 'children', // 自定义子节点字段
}

const chartListData = ref<IListItem[]>([])
const chooseAreaCode = ref('')
const chooseIndustry = ref('')
const queryParams = reactive({
    code: props.tenantInfo?.areaCode ? props.tenantInfo?.areaCode : '3201',
    roundName: '',
    industryCode: '',
    params: [
        {
            modelCode: 'Model005',
            code: <string[]>[],
            roundName: [],
            industryCode: <string[]>[],
        },
    ],
})
const initChart = () => {
    if (option.series[0].data.some((item) => item.value !== null && item.value !== '')) {
        hasChartData.value = true
    } else {
        hasChartData.value = false
    }
    const chartDom = chartRef.value
    const myChart = echarts.init(chartDom)
    myChart.setOption(option)
}

watch(
    () => transformedData.value,
    (newVal) => {
        chartListData.value = newVal.map((item) => ({
            name: item.indexName,
            value: item.indexValue,
        }))
        option.series[0].data = chartListData.value
        initChart()
    },
    { deep: true }
)

import { useStore } from 'vuex'
const store = useStore()
const staticArea = ref<IAicConditionDataOptionItem[]>([])
const staticIndustry = ref<IAicConditionDataOptionItem[]>([])
const areaOptions = computed(() => {
    if (!staticArea.value.length) return []
  
    const areaCode = props.tenantInfo?.areaCode
    if (!areaCode) return staticArea.value
  
    const city = staticArea.value.find(city => city.value === areaCode.slice(0, 2))
    if (!city?.children?.length) return []
  
    return city.children.filter(town => town.value === areaCode) || []
})

const getStaticData = async() => {
    let staticConfigRes = await aicService.conditionGetData({}) 
    store.dispatch('app/setStaticConfig', staticConfigRes)
    staticArea.value = staticConfigRes.area
    staticIndustry.value = staticConfigRes.industry
}
const changeArea = (e: string[] | undefined) => {
    if (e) {
        chooseAreaCode.value = e[e.length - 1]
    } else {
        chooseAreaCode.value = ''
    }
}
onMounted(async () => {
    getStaticData()
})
const getModel005Data = () => {
    homeService.searchHomeData(queryParams).then((res) => {
        console.log('getModel005Data', res)
        const { Model005 } = res
        if (Model005) chartData.value = Model005
    })
}
watch(
    [chooseAreaCode, chooseIndustry], // 直接传 ref 对象，不需要 .value
    ([newAreaCode, newIndustry]) => {
        queryParams.params[0].code = newAreaCode ? [newAreaCode] : []
        queryParams.params[0].industryCode = newIndustry ? [newIndustry] : []
        getModel005Data()
    }
)
</script>
<template>
    <div class="kjzz">
        <div class="display-flex space-between">
            <ModuleTitle class="b-margin-24 mw-120" title="科技资质名单"></ModuleTitle>
            <div class="display-flex">
                <div class="w-158 r-margin-24">
                    <el-cascader
                        :options="areaOptions"
                        clearable
                        placeholder="地区"
                        :props="cascaderProps"
                        @change="changeArea"
                        popper-class="custom-cascader-dropdown"
                    />
                </div>
                <div class="w-158">
                    <el-select v-model="chooseIndustry" placeholder="全部行业" clearable>
                        <el-option
                            v-for="item in staticIndustry"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
            </div>
        </div>
        <div ref="chartRef" class="flex-1" :class="hasChartData ? '' : 'no-data'"></div>
    </div>
</template>
<style scoped lang="scss">
.kjzz {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    .no-data {
        background: url('@/assets/images/no-chart-data.png');
        background-size: contain;
        background-repeat: no-repeat;
        background-position-x: center;
    }
}
.el-select-dropdown__item.is-selected {
    color: var(--main-blue-);
}
:deep(.el-cascader-node.is-active > .el-cascader-node__label) {
    color: #ff0000;
}
:deep(.el-cascader-node.is-selectable.in-checked-path > .el-cascader-node__label) {
    color: #ff0000;
}
.custom-cascader-dropdown .el-cascader-node.is-active > .el-cascader-node__label {
    color: #ff0000;
}
</style>
