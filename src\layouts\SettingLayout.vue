<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, HeaderContainer, MainContainer } from '@/components/layouts/setting'
</script>

<template>
    <div class="common-layout">
        <el-container class="container">
            <el-header>
                <HeaderContainer />
            </el-header>
            <el-container class="flex-1 oh">
                <AsideContainer />
                <el-main class="back-color-main flex flex-column oh">
                    <MainContainer />
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>

<style lang="scss" scoped>
.container {
    height: 100vh;
}

.aside {
    max-width: 200px;
}

.el-main {
    --el-main-padding: 0;
}

.el-header {
    --el-header-padding: 0;
    --el-header-height: auto;
}
</style>
