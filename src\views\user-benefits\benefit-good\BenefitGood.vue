<template>
    <div style="background-color: #f7f7f7;">
        <div style="background-color: #fff;padding:16px; box-sizing: border-box">
            <el-table
                ref="tableList"
                :data="tableData"
                style="width: 100%;height: 500px;"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="名称" prop="name" />
                <el-table-column label="描述" prop="description" />
                <el-table-column label="规格">
                    <template #default="scope">
                        <div v-if="scope.row.amountSpec.unitCount">
                            {{ scope.row.amountSpec.unitCount }}{{ scope.row.amountSpec.unitName }} 
                        </div>
                        <div v-else>
                            {{ scope.row.timeSpec.duration }}{{ transUnit(scope.row.timeSpec.unit) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100px">
                    <template #default="scope">
                        <div
                            class="pointer"
                            style="color: #1966ff"
                            type="primary"
                            @click="allocate(scope.row)"
                        >
                            订购
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>    
        </div>
    </div>
    <el-dialog 
        title="服务订购"
        v-model="dialogVisible"
        width="500px"
        @close="handleClose"
    >
        <div class="t-margin-4 display-flex flex-column gap-16 font-16 color-two-grey">
            <div>商品名称：
                <span class="color-black">{{ goodsName }}</span>
            </div>
            <div>选择租户</div>
            <el-select
                v-model="tenantId"
                clearable
            >
                <el-option
                    v-for="item in tenantList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <div>购买数量</div>
            <el-input
                v-model="quantity"
                type="number"
                min="1"
                max="999"
                @input="validateQuantity"
            >
            </el-input>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false" style="height: 40px">取 消</el-button>
                <el-button type="primary" @click="confirmAllocate" :loading="confirmLoading" style="height: 40px">
                    确 认
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang='ts' setup>
import { onMounted, ref, reactive } from 'vue'
import orderService from '@/service/orderService'
import type { IOrderGoodsListParams, IOrderGoodsListResponseItem, IOrderAllocateRquitiesParams } from '@/types/order'
import systemService from '@/service/systemService'
import { ElMessage } from 'element-plus'

const tableLoading = ref<boolean>(false)
const tableData = ref<IOrderGoodsListResponseItem[]>([])
const totalNum = ref<number>(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})
const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    search()
}

const transUnit = (unit:string) => {
    return unit === 'YEARS'? '年' : unit === 'MONTHS' ? '月' : '天'
}

const queryParams = ref<IOrderGoodsListParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
})
const dialogVisible = ref(false)
const confirmLoading = ref<boolean>(false)
const goodsId = ref<string>('')
const goodsName = ref<string>('')
const tenantList = ref<{value:string,label:string}[]>([])
const tenantId = ref<string>('')
const quantity = ref<number>(1)
const allocate = (row: IOrderGoodsListResponseItem) => {
    console.log(row)
    dialogVisible.value = true
    goodsName.value = row.name
    goodsId.value = row.goodsId
}
const handleClose = () => {
    tenantId.value = ''
    quantity.value = 1
}

const validateQuantity = (val: number) => {
    if (val < 1) {
        quantity.value = 1
    }
}
const confirmAllocate = () => {
    console.log('confirmAllocate')
    if(!tenantId.value){
        ElMessage.error('请选择租户')
        return
    }
    confirmLoading.value = true
    const params : IOrderAllocateRquitiesParams = {
        goodsId:goodsId.value,
        quantity:quantity.value,
        tenantId:tenantId.value,
    }
    console.log('params',params)
    orderService.orderAllocateEquitie(params).then((res) => {
        console.log(res)
        if(res.success){
            ElMessage.success('订购成功')
        }else{
            ElMessage.error(res.errMsg)
        }
    }).finally(() => {
        dialogVisible.value = false
        confirmLoading.value = false
    })
}
const search = () => {
    tableLoading.value = true
    orderService.orderGoodsList(queryParams.value).then((res) => {
        console.log(res)
        tableData.value = res.data
        totalNum.value = res.total
    }).finally(() => {
        tableLoading.value = false
    })
}

onMounted(() => {
    search() 
    systemService.tenantList().then(response => {
        tenantList.value = response.map(item => ({
            label:item.name,
            value:item.id
        }))
    })
})
    

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>