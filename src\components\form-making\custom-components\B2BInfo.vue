<template>
    <el-descriptions class="margin-top" :column="2" border :label-class-name="'base-info-label'">
        <el-descriptions-item label-class-name="base-info-label" :span="2" min-width="150">
            <template #label>
                <div class="cell-item">经营范围</div>
            </template>
            {{ modelData.bussinessScope || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label" :span="2">
            <template #label>
                <div class="cell-item">主营产品</div>
            </template>
            {{ modelData.bussinessProduct || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label" :span="2">
            <template #label>
                <div class="cell-item">管理体系认证</div>
            </template>
            {{ modelData.mgtCert }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">经营模式 </div>
            </template>
            {{ modelData.bussinessModel || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">代工模式 </div>
            </template>
            {{ modelData.foundryModel || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">员工人数 </div>
            </template>
            {{ modelData.scale || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">厂房面积 </div>
            </template>
            {{ modelData.factoryArea || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">年营业额 </div>
            </template>
            {{ modelData.annualTurnover || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">月产量 </div>
            </template>
            -
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">主要市场 </div>
            </template>
            {{ modelData.businessMarket || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">主要客户群 </div>
            </template>
            {{ modelData.businessClient || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">经营品牌 </div>
            </template>
            {{ modelData.brand || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">质量控制 </div>
            </template>
            {{ modelData.qualityControl || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">年进口额 </div>
            </template>
            -
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">年出口额 </div>
            </template>
            -
        </el-descriptions-item>

    </el-descriptions>

</template>

<script lang='ts' setup>
import { onMounted } from 'vue'

defineProps({
    modelData: {
        type: Object,
        default: () => ({})
    }
})

onMounted(() => {
})

</script>

<style lang='scss' scoped>
:deep(.base-info-label) {
    background: var(--table-header-bg-) !important;
}
</style>