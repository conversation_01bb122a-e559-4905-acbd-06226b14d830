<script lang="ts" setup>
import type { IAutoDialerListItem } from '@/types/autoDialer'
import { ref, watch, computed } from 'vue'
import CreateTask from './CreateTask.vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================
const props = defineProps<{
    visible: boolean
    crmId: string[]
    currentTask?: IAutoDialerListItem
    refreshAfterEdit?: () => void
}>()

const emit = defineEmits(['update:visible'])
// ====================== Store & Computed Values ======================
const store = useStore<RootState>()
const isOpen = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    const { openAiPhone } = tenant || {}
    return openAiPhone
})
// ====================== Refs & Reactive State ======================
const drawerVisible = ref(false)
const openVisible = ref(false)

// ====================== Methods ======================
const handleClose = () => {
    emit('update:visible', false)
}

// ====================== Watchers ======================
watch(
    () => props.visible,
    (val) => {
        if (isOpen.value) {
            drawerVisible.value = val
        } else {
            openVisible.value = val
        }
    }
)

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <el-dialog title="智能外呼" width="60%" v-model="openVisible" @close="handleClose">
        <OpenPage v-if="openVisible" />
    </el-dialog>
    <el-drawer v-model="drawerVisible" title="发起外呼" @close="handleClose" class="call-record-drawer" :size="702">
        <CreateTask
            :crm-id="crmId"
            v-if="drawerVisible"
            :on-close="handleClose"
            :current-task="currentTask"
            :refresh-after-edit="refreshAfterEdit"
        />
    </el-drawer>
</template>

<style lang="scss" scoped></style>
