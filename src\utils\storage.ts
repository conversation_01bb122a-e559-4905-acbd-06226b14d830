export const setItem = (key: string, value: string | object) => {
    try {
        localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
        console.log('Error saving to localStorage', error)
    }
}

export const getItem = (key: string) => {
    try {
        const item = localStorage.getItem(key)
        return item ? JSON.parse(item) : null
    } catch (error) {
        console.log('Error getting data from localStorage', error)
        return null
    }
}

export const removeItem = (key: string) => {
    try {
        localStorage.removeItem(key)
    } catch (error) {
        console.log('Error removing data from localStorage', error)
    }
}

export const clearStorage = () => {
    try {
        localStorage.clear()
    } catch (error) {
        console.log('Error clearing localStorage', error)
    }
}
