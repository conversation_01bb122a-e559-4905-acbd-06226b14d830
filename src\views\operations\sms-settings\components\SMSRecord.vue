<template>
    <el-table
        :data="tableList"
        style="width: 100%;height: 500px;"
        show-overflow-tooltip
        v-loading="tableLoading"
        :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
    >
        <template v-if="!tableLoading" #empty>
            <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                <div class="font-first-title-unactive color-two-grey">暂无数据</div>
            </div>
        </template>
        <el-table-column label="手机号" prop="mobile"/>
        <el-table-column label="发送状态" prop="status">
            <template #default="scope">
                {{ scope.row.status === 1 ? '发送成功' : '发送失败' }}
            </template>
        </el-table-column>
        <el-table-column label="发送失败原因" prop="reason"/>
        <el-table-column label="发送时间" prop="sendDate" >
            <template #default="scope">
                {{ scope.row.sendDate? moment(scope.row.sendDate).format('YYYY-MM-DD HH:mm:ss') : '-' }}
            </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" >
            <template #default="scope">
                {{ scope.row.createTime? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
            </template>
        </el-table-column>
        <el-table-column label="模板名称" prop="templateName"/>
        <!-- <el-table-column
               
            fixed="right"
            label="操作"
            width="100px"
        >
            <template #default="scope" >
                <div class="display-flex gap-10">
                    <div
                        class="pointer"
                        style="color: #1966ff"
                        type="primary"
                        @click="deleteRecord(scope.row.id)"
                    >
                        删除
                    </div>
                </div>
            </template>
        </el-table-column> -->
    </el-table>
    <el-affix position="bottom">
        <div class="pagination-bar">
            <el-pagination
                v-model:currentPage="pageInfo.page"
                v-model:page-size="pageInfo.pageSize"
                :total="pageInfo.total"
                layout="total, sizes, prev, pager, next, jumper"
                @change="pageChange"
            /> 
        </div>
    </el-affix>    
</template>

<script lang='ts' setup>
import { ref, getCurrentInstance, reactive, onMounted } from 'vue'
import type { ISMSLogPageResponseItem, IPaginationParams } from '@/types/sms'
import systemService from '@/service/systemService'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const tableLoading = ref<boolean>(false)
const tableList = ref<ISMSLogPageResponseItem[]>([])
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    search()
}
const queryParams = ref<IPaginationParams>({
    page:1,
    pageSize:20
})
const search = () => {
    tableLoading.value = true
    systemService.smsLogPage(queryParams.value).then((res) => {
        console.log('res',res)
        tableList.value = res.data
        totalNum.value = res.total
        console.log('tableList',tableList.value)
    }).finally(() => {
        tableLoading.value = false
    })
}

onMounted(() => {
    search()
})
</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    margin-top: 16px;
}
</style>