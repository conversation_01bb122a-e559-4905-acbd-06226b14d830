<script lang='ts' setup>
import { reactive, ref, computed, watch, onMounted } from 'vue'
import { getItem } from '@/utils/storage'
import type { ISearchItemType } from '@/types/lead'
import * as allSearchOptions from '@/js/search-options'
import CustomSetting from '@/components/common/CustomSetting.vue'

type CustomConfig = {
    [key: string]: Array<{
        label: string;
        value: string | number;
    }>;
}
const props = defineProps<{
    searchOptionKey: string
    customConfig?: CustomConfig
    defaultValue?: Record<string, boolean | string | number[] | string[]>
    tabType?: string // 标签类型 用以区别是否是 我的XX
}>()
const searchOptions = ref<ISearchItemType[]>([])

watch(() => props.searchOptionKey as keyof typeof allSearchOptions, (newVal) => {
    if (newVal) {
        searchOptions.value = allSearchOptions[newVal]
    }
}, { immediate: true })
watch(() => props.customConfig, (newVal) => {
    console.log('props.customConfig', newVal)
    init()
})

watch(() => props.defaultValue, (newVal) => {
    console.log('searchbox.props.defaultValue', newVal)
    if (newVal && Object.keys(newVal).length > 0) {
        Object.assign(queryParams, newVal)
        search()
    }
})
onMounted(() => {
    console.log('props.defaultValue',props.defaultValue)
    init()
})
const init = () => {
    // 检查是否有本地存储并更新选项options
    const localSearchConfig = getItem(props.searchOptionKey)
    if (localSearchConfig) {
        searchOptions.value = localSearchConfig
    }
    console.log('searchOptions', searchOptions.value)
    // 线索列表/客户列表  我的协作客户tab 我的tab页下去掉【负责人】
    if ((props.tabType === 'mine' || props.tabType === 'with') && (props.searchOptionKey === 'LEAD_SEARCH_OPTIONS' || props.searchOptionKey === 'CUSTOMER_SEARCH_OPTIONS')) {
        searchOptions.value = searchOptions.value.filter(item => item.key !== 'user')
    }
    // 线索列表/客户列表 在全部tab页下增加【所属组织】
    if (props.tabType !== 'all' && (props.searchOptionKey === 'LEAD_SEARCH_OPTIONS' || props.searchOptionKey === 'CUSTOMER_SEARCH_OPTIONS')) {
        searchOptions.value = searchOptions.value.filter(item => item.key !== 'orgIds')
    }

    async function fetchData() {
        if (props.customConfig) {
            const customConfigKeys = Object.keys(props.customConfig)
            customConfigKeys.forEach((key) => {
                const item = searchOptions.value.find((item) => item.key === key)
                if (item) item.options = props.customConfig?.[key] // 使用 ?.[] 安全访问
            })
        }
    }
    fetchData()
}
const expand = ref(false)
const showOptions = computed(() => {
    let searchShowOptions = JSON.parse(JSON.stringify(searchOptions.value)).filter((item) => item.isShow === true)
    if (searchShowOptions.length > 6 && !expand.value) {
        return searchShowOptions.slice(0, 6)
    } else {
        return searchShowOptions
    }
})
const emit = defineEmits(['updateSearchParams'])
// 过滤空属性值
const filterEmptyParams = (queryParams: Record<string, boolean | string | number[] | string[]>) => {
    const result: Record<string, boolean | string | number[] | string[]> = {}
    for (const key in queryParams) {
        console.log('queryParams[key]', key)
        const value = queryParams[key]
        if (value !== '' && value !== null && value !== undefined && JSON.stringify(value) !== JSON.stringify([0, 100])) {
            result[key] = value
        }
        console.log('result', result)
    }
    return result
}

// 处理日期时间,截止日期的时间定位到23:59:59
const handleDateChange = (val: number[]) => {    
    if (val.length === 2) {
        if (typeof (val[1]) === 'number') {
            val[1] += 86399000
        }
    }
}

const queryParams = reactive<Record<string, boolean | string | number[] | string[]>>({})
const marks = { 0: '0', 30: '30', 60: '60', 90: '90' }
const search = () => {
    let filterQueryParams = filterEmptyParams(queryParams)
    console.log('filterQueryParams', filterQueryParams)
    emit('updateSearchParams', filterQueryParams)
}
const clear = () => {
    Object.keys(queryParams).forEach((key) => {
        delete queryParams[key] // 逐个删除属性
    })
    queryParams['basicScore'] = [0, 100]
    if (props.defaultValue) {
        if (Object.keys(props.defaultValue).length > 0) {
            Object.assign(queryParams, props.defaultValue)
        }
    }
    search()
}
const settingVisible = ref(false)


const selectChange = (option: ISearchItemType) => {
    // console.log('queryParams',queryParams[option.key].length, Array.isArray(queryParams[option.key]))
    if (!Array.isArray(queryParams[option.key]) || !option.options) return
    if ((queryParams[option.key] as string[]).length === 0) {
        option.isIndeterminate = false
        option.checkAll = false
    } else if ((queryParams[option.key] as string[]).length === option.options.length) {
        option.isIndeterminate = false
        option.checkAll = true
    } else {
        option.isIndeterminate = true
    }
}
// 处理下拉选择中全选的逻辑
const handleCheckAll = (value: boolean, option: ISearchItemType) => {
    console.log(value, option)
    if (!option.options) return
    if (value) {
        let ids: string[] = option.options.map((i) => {
            return i.value as string
        })
        console.log('ids', ids)
        queryParams[option.key] = ids
    } else {
        queryParams[option.key] = []
    }
}
</script>
<template>
    <div class="search-area">
        <el-row :gutter="30">
            <el-col style="margin-bottom: 16px" :xs="24" :sm="12" :md="8" :lg="8" :xl="8"
                    v-for="(item, index) in showOptions" :key="index">
                <div class="display-flex space-between top-bottom-center">
                    <div class="search-label">{{ item.label }}：</div>
                    <div class="flex-1">
                        <el-input v-if="item.type === 'input'" :placeholder="item.placeholder || '请输入' + item.label"
                                  v-model.trim="queryParams[item.key]" @keypress.enter="search" clearable></el-input>
                        <el-select
                            v-if="item.type === 'select' || item.type === 'multipleSelect' || item.type === 'checkAllMultipleSelect'"
                            clearable
                            :multiple="item.type === 'multipleSelect' || item.type === 'checkAllMultipleSelect'"
                            collapse-tags :placeholder="item.placeholder" v-model="queryParams[item.key]"
                            @change="selectChange(item)">
                            <el-checkbox v-if="item.type === 'checkAllMultipleSelect'" class="select-check-all"
                                         v-model="item.checkAll" :indeterminate="item.isIndeterminate"
                                         @change="(value: boolean) => { handleCheckAll(value, item) }">全部</el-checkbox>
                            <el-option v-for="options in item.options" :key="options.value" :label="options.label"
                                       :value="options.value"></el-option>
                        </el-select>
                        <el-date-picker
                            style="width: 100%; box-sizing: border-box" 
                            v-if="item.type === 'date'"
                            :type="item.type2 ? item.type2 : 'daterange'"
                            unlink-panels
                            :value-format="item.valueFormat || 'x'"
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            v-model="queryParams[item.key]"
                            clearable
                            @change="handleDateChange" />
                        <el-cascader style="width: 100%" v-if="item.type === 'cascader'" :options="item.options"
                                     :props="item.props" collapse-tags clearable v-model="queryParams[item.key]"
                                     :placeholder="item.placeholder" />
                        <el-slider v-if="item.type === 'slider'" range :max="100" :min="0" :marks="marks"
                                   v-model="queryParams[item.key]" />
                    </div>
                </div>
            </el-col>
        </el-row>
        <el-row justify="space-between" class="top-bottom-center">
            <el-col :span="6">
                <div class="display-flex top-bottom-center">
                    <CustomSetting v-if="searchOptions.length > 6" :visible="settingVisible" :from="'search'" :tabType="tabType"
                                   :templateName="props.searchOptionKey" @refreshOptions="init()">
                        <template v-slot:content>
                            <div style="margin-right: 16px" @click="settingVisible = !settingVisible">
                                <Icon style="cursor: pointer" icon="icon-a-xitongguanli" color="var(--main-blue-)">
                                </Icon>
                            </div>
                        </template>
                    </CustomSetting>

                    <div v-if="searchOptions.length > 6 && !expand" @click="expand = true">
                        <Icon style="cursor: pointer" icon="icon-arrow-down-double" size="14" color="var(--main-blue-)">
                        </Icon>
                    </div>
                    <div v-if="searchOptions.length > 6 && expand" @click="expand = false">
                        <Icon style="cursor: pointer" icon="icon-arrow-left-double" size="14" color="var(--main-blue-)">
                        </Icon>
                    </div>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="display-flex" style="justify-content: flex-end">
                    <el-button class="color-black" @click="clear">清空</el-button>
                    <el-button class="back-color-blue" type="primary" @click="search">搜索</el-button>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';

.search-area {
    width: 100%;
    background-color: #fff;

    .search-label {
        font-size: 16px;
        color: var(--main-black-);
        width: 120px;
    }
}

:deep(.el-slider) {
    // padding: 0 12px;
    width: 90%;
    margin: auto;
}

// :deep(.el-slider__button){
//     width: 14px;
//     height: 14px;
// }
.select-check-all {
    width: 100%;
    padding: 0 15px;
    border-bottom: 1px solid #f5f7fa;
}
</style>
