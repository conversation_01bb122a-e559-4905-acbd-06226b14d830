import type { ICommonResponse, IPaginationResponse } from './axios'
import type { IAllRecord } from './record'

export interface IAutoDialerRequest extends IAllRecord {
    taskName?: string
    status?: string
    startTime?: string
    endTime?: string
    page?: number
    pageSize?: number
}

export interface IAutoDialerFilter {
    taskName: string
    status: string
    date: string[]
}

export interface IAutoDialerRespones extends IPaginationResponse {
    data: IAutoDialerListItem[]
}

export interface IAutoDialerListItem {
    tenantId: string
    orgId: string
    orgName: string
    createUser: string
    createUserName: string
    createTime: string
    id: number
    taskCode: string
    taskName: string
    outboundDate: string
    outboundExpireDate: string
    outboundStrategy: number
    outboundLevel: number
    outboundCircleType: number
    outboundCircleValue: string
    outboundTimeInterval: string
    recallStatus: number
    maxRecallTimes: number
    recallPeriodMin: number
    allowRecallStatus: string
    robotId: string
    robotName: string
    dialogTaskId: string
    dialogTaskName: string
    taskStatus: number
    taskType: number
    outboundNo: string
    taskDesc: string
    outboundTotal: number
    connectedCount: number
    missedCount: number
    notDialedCount: number
    companyNum: number
    companySuccessCount: number
    companyFailCount: number
    companyTotalCount: number
    finished: boolean
    stop: boolean
    strategyName: string
}

// 号码列表
export interface IAutoDialerTaskDetailRequest extends IAllRecord {
    taskCode?: string
}

export interface IAutoDialerTaskDetailRespones extends IPaginationResponse {
    data: IAutoDialerTaskDetailListItem[]
}

export interface IAutoDialerTaskDetailListItem {
    tenantId: string
    orgId: string
    orgName: string
    createUserId: string
    createUserName: string
    createTime: string
    id: number
    taskCode: string
    taskName: string
    phoneNumber: string
    name: string
    companyName: string
    socialCreditCode: string
    tags: string
    outboundNo: string
    robotId: string
    robotName: string
    callStartTime: string | null
    callDurationSec: string | null
    callStatus: number
    callStatusName: string
    callTimes: number
    aiTagName: string | null
    callContent: string | null
    crmId: string
    dialogTaskName: string
}

export interface IAutoDialerTaskInfoFilter {
    phoneNumber: string
    status: string[]
    name: string
    date: string[]
}

export interface IAppSaveRequest {
    openAiPhone: boolean
    aiSeatsNum: number
    appid: string
    orgId: string
    privateKey: string
    tenantId: string
    website: string
}

export interface IAppConfigRequest extends IAllRecord {
    tenantId: string
}

export interface IAppConfigItem {
    aiSeatsNum: number
    appid: string
    id: number
    orgId: string
    privateKey: string
    tenantId: string
    website: string
    openAiPhone?: boolean
}
export interface IAppConfigRespones extends ICommonResponse {
    data: IAppConfigItem
}
export interface IAutoDialerTaskDetailFilter {
    phoneNumber: string
    status: string
    companyName: string
    startCallTime: string
    endCallTime: string
    aiTagName: string
    taskName?: string
    date?: string
}

export interface IAutoDialerTaskDetailFilterForm {
    phoneNumber: string
    status: string
    companyName: string
    date: string[]
    aiTagName: string
    taskName?: string
}

export interface IAutoDialerTaskCallDetailRequest extends IAllRecord {
    cellphone: string
    taskCode: string
}

export interface IAutoDialerTaskCallDetailItem {
    aiTagName: string
    askTimes: number
    audioRecordCode: string
    callDurationSec: string
    callEndTime: number
    callId: string
    callStartTime: number
    callStatus: number
    cellphone: string
    dialogTaskName: string
    robotId: string
    robotName: string
    sessionId: string
    taskCode: string
}

export interface IAutoDialerTaskCallDetailResponse extends ICommonResponse {
    data: IAutoDialerTaskCallDetailItem[]
}

export interface IAutoDialerTaskCallContentRequest extends IAllRecord {
    audioRecordCode: string
    tenantId: string
}

export interface IAutoDialerTaskCallContentItem {
    content: string
    questionId: string
    type: string
}

export interface IAutoDialerTaskCallContentResponse extends ICommonResponse {
    data: IAutoDialerTaskCallContentItem[]
}

// /task/detail
export interface IAutoDialerTaskDetailInfoRespones extends ICommonResponse {
    data: IAutoDialerTaskDetailInfoItem
}

export interface IAutoDialerTaskDetailInfoItem extends IAllRecord {
    allowRecallStatus: string
    companyFailCount: number
    companyNum: number
    companySuccessCount: number
    companyTotalCount: number
    connectedCount: number
    createTime: string
    createUser: string
    createUserName: string
    dialogTaskId: string
    dialogTaskName: string
    finished: boolean
    id: number
    maxRecallTimes: number
    missedCount: number
    notDialedCount: number
    orgId: string
    orgName: string
    outboundCircleType: number
    outboundCircleValue: string
    outboundDate: string
    outboundExpireDate: string
    outboundLevel: number
    outboundNo: string
    outboundStrategy: number
    outboundTimeInterval: string
    outboundTotal: number
    recallPeriodMin: number
    recallStatus: number
    robotId: string
    robotName: string
    stop: boolean
    taskCode: string
    taskDesc: string
    taskName: string
    taskStatus: number
    taskType: number
    tenantId: string
}

export interface IAutoDialerDeleteRequest extends IAllRecord {
    taskCode: string
    tenantId: string
}

export interface IAutoDialerTerminationRequest {
    taskCode: string
    tenantId: string
}

export interface IAutoDialerStartRequest {
    taskCode: string
    tenantId: string
}

export interface IAutoDialerPauseRequest {
    taskCode: string
    tenantId: string
}

export interface IAutoDialerTaskExportRequest {
    aiTagName?: string
    companyName?: string
    createUserId?: string
    endCallTime?: string
    ids?: number[]
    nums?: number
    orgId?: string
    phoneNumber?: string
    socialCreditCode?: string
    startCallTime?: string
    status?: string
    taskCode?: string
    taskName?: string
    tenantId?: string
}

export interface IAutoDialerCompanyDetailRequest extends IAllRecord {
    taskCode?: string
}

export interface IAutoDialerTaskSaveRequest {
    allowRecallStatus: string
    dialogTaskId: string
    dialogTaskName: string
    maxRecallTimes: number
    outboundCircleType: number
    outboundCircleValue: string
    outboundDate: string
    outboundExpireDate: string
    outboundTimeInterval: string
    recallPeriodMin: number
    recallStatus: number
    robotId: string
    robotName: string
    strategyId?: number | null
    strategyName: string
    taskDesc: string
    taskName: string
    taskId?: number | null
    tenantId?: string
    tagType?: number[]
    crmId?: string[]
    taskCode?: string
    needEdit?: boolean
}

export interface IAutoDialerTaskSaveTaskDetail {
    companyName?: string
    contacts?: IAutoDialerTaskSaveContact[]
    crmId?: string
    socialCreditCode?: string
}

export interface IAutoDialerTaskSaveContact {
    name?: string
    phoneNumber?: string
    tags?: string
}

export interface IAutoDialerStrategyListItem {
    tenantId: string
    orgId: string
    orgName: string
    createUser: string
    createUserName: string
    createTime: string
    id: number
    strategyName: string
    outboundDate: string
    outboundExpireDate: string
    outboundStrategy: number
    outboundLevel: number
    outboundCircleType: number
    outboundCircleValue: string
    outboundTimeInterval: string
    recallStatus: number
    maxRecallTimes: number
    recallPeriodMin: number
    allowRecallStatus: string
    robotId: string
    robotName: string
    dialogTaskId: string
    dialogTaskName: string
}

export interface IAutoDialerStrategyListResponse extends ICommonResponse {
    data: IAutoDialerStrategyListItem[]
}

export interface ICreateTaskForm {
    taskName: string
    taskDesc: string
    strategyId: number | null
    strategyName: string
    outboundDate: string
    outboundExpireDate: string
    weekRange: number[]
    recallStatus: boolean
    outboundTimeInterval: string
    robotId: string
    taskId: number | null
    allowRecallStatus: number[]
    maxRecallTimes: number
    recallPeriodMin: number
    taskCode?: string
}

export interface IAutoDialerRobotListResponse extends ICommonResponse {
    data: IAutoDialerRobotListItem[]
}

export interface IAutoDialerRobotListItem {
    robotId: string
    robotName: string
    engineInfoList: IAutoDialerEngineInfoListItem[]
}

export interface IAutoDialerEngineInfoListItem {
    engineId: string
    engineName: string
}

export interface IAutoDialerTaskEditRequest {
    allowRecallStatus: string
    dialogTaskId: string
    dialogTaskName: string
    maxRecallTimes: number
    outboundCircleType: number
    outboundCircleValue: string
    outboundDate: string
    outboundExpireDate: string
    outboundTimeInterval: string
    recallPeriodMin: number
    recallStatus: number
    robotId: string
    robotName: string
    strategyId?: number | null
    strategyName: string
    taskDesc: string
    taskName: string
    taskId?: string
    tenantId?: string
    tagType: number[]
    crmId: string[]
}

export interface ITaskQueryContactTagCountRequest {
    crmId: string[]
}

export interface ITaskQueryContactTagCountResponse extends ICommonResponse {
    data: {
        recommend: number
        total: number
        other: number
    }
}
export interface IAutoDialerTaskCompanyFilter {
    callStatus: string
    companyName: string
    startCallTime: string
    endCallTime: string
    aiTagName: string
    taskName?: string
    recallStatus: string
    date?: string
}

export interface IAutoDialerTaskCompanyFilterForm {
    callStatus: string
    companyName: string
    date: string[]
    aiTagName: string
    taskName?: string
    recallStatus: string
}
