import type { IAllRecord } from './record'
import type { ICommonResponse } from './axios'

export interface IMenuSysResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: IMenuResponse[]
}
export interface IMenuLoadPermissionTreeResponse extends ICommonResponse{
    data: IMenuResponse[]
}
export interface IMenuResponse {
    id: string
    parentId: string
    menuId: string
    name: string
    icon: string
    url: string
    sort: number
    type: number
    children: IMenuResponse[] | []
    status: number
    parentName?: string
}

export interface IMenuRoute {
    path: string
    name: string
    component: string
    meta: {
        title: string
        icon: string
        hidden: boolean
        noCache: boolean
        affix: boolean
        activeMenu: string
    }
    children: IMenuRoute[]
}

export interface IAddMenuForm {
    icon: string
    id: string
    menuId: string
    name: string
    parentId: string
    sort: number
    type: number | undefined
    url: string
    status: number
}

export interface IAddFormRef {
    save: () => void
}

export interface IMenuAddRequest {
    icon: string
    id: string
    menuId: string
    name: string
    parentId: string
    sort: number
    type: number
    url: string
    status: number
}

export interface IMenuRemoveRequest extends IAllRecord {
    menuId: string
}
