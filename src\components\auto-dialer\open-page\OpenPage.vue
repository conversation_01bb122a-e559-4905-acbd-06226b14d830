<script lang="ts" setup>
// import MAINPNG from '@/assets/images/auto-dialer/main.png'
// import PNG1 from '@/assets/images/auto-dialer/1.png'
// import PNG2 from '@/assets/images/auto-dialer/2.png'
// import PNG3 from '@/assets/images/auto-dialer/3.png'
// import PHONE from '@/assets/images/auto-dialer/phone.png'
import OPENJPG from '@/assets/images/auto-dialer/open.jpg'
// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================
// const props = defineProps<{
//   propName: string
// }>()
//
// const emit = defineEmits<{
//   (e: 'eventName', payload: SomeType): void
// }>()

// ====================== Store & Computed Values ======================
// const store = useSomeStore()
// const computedValue = computed(() => store.value)

// ====================== Refs & Reactive State ======================
// const list = [
//     {
//         icon: PNG1,
//         title: '提高效率',
//         desc: '自动拨号、高效营销',
//     },
//     {
//         icon: PNG2,
//         title: '降低成本',
//         desc: '覆盖广泛、批量处理',
//     },
//     {
//         icon: PNG3,
//         title: '贴合定制',
//         desc: '定制话术、满足多样需求',
//     },
// ]
// const count = ref(0)
// const state = reactive({
//   key: 'value'
// })

// ====================== Methods ======================
// const methodName = () => {
//   // Implementation
// }

// ====================== Watchers ======================
// watch(someRef, (newVal) => {
//   // Side effect
// }, { immediate: true })

// ====================== Lifecycle Hooks ======================
// onMounted(() => {
//   // Initialization
// })
</script>

<template>
    <div class="back-color-white border-radius-4 all-padding-16 height-100">
        <img :src="OPENJPG" alt="" style="height: 100%; width: 100%" />
        <!-- <div class="flex flex-1 top-bottom-center height-100 flex-row all-padding-100 open-page">
            <div class="flex flex-column mw-300 maxw-440 gap-100 left">
                <div class="flex flex-column gap-16">
                    <div class="font-26 font-weight-500">
                        <span class="color-blue">智能</span>
                        <span class="color-two-orange">外呼</span>
                        <span class="color-blue">介绍</span>
                    </div>
                    <div class="font-20 color-text-grey">
                        通过语音识别与生成技术，能够理解客户语音输入并作出相应回复，实现批量、高效的客户沟通。
                    </div>
                </div>
                <div class="flex flex-column gap-40">
                    <div class="flex flex-row gap-24 top-bottom-center" v-for="(item, index) in list" :key="index">
                        <div class="h-44 w-44">
                            <img :src="item.icon" alt="图标" />
                        </div>
                        <div class="flex flex-column gap-8">
                            <div class="font-20 color-black">{{ item.title }}</div>
                            <div class="font-20 color-text-grey">{{ item.desc }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-column flex-1 oh left-right-center gap-16 right">
                <div class="main-img">
                    <img :src="MAINPNG" alt="main" class="width-100" />
                </div>
                <div class="flex flex-row gap-16 top-bottom-center left-right-center">
                    <div class="w-36 h-36 border-radius-36">
                        <img :src="PHONE" alt="phone" />
                    </div>
                    <div class="font-36">4001000086</div>
                </div>
                <div class="flex left-right-center color-black font-20">当前无外呼权限，请联系客服开通</div>
            </div>
        </div> -->
    </div>
</template>

<style lang="scss" scoped>
.open-page .left {
    max-width: 440px;
}

@media screen and (max-width: 1500px) {
    .open-page {
        zoom: 0.8;
    }
}

@media screen and (max-width: 1200px) {
    .open-page {
        zoom: 0.7;
    }
}

@media screen and (max-width: 1000px) {
    .open-page {
        zoom: 1;
        flex-direction: column;
    }

    .open-page .main-img {
        display: none;
    }

    .open-page .left {
        margin-bottom: 100px;
    }

    .open-page .right {
        justify-content: flex-start;
    }
}
</style>
