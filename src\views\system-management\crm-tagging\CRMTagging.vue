<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7;">
        <div ref="searchContentRef" style="margin-bottom: 16px; background-color: #fff; padding: 16px">
            <searchBox
                :searchOptionKey="'TAG_SEARCH_OPTIONS'"
                @updateSearchParams="updateSearchParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div ref="actionBarContentRef" class="display-flex justify-flex-end b-margin-10">
                <el-button type="primary" v-if="permissionService.isTagMenuAddPermitted()" @click="handleAdd">新增</el-button>
            </div>
            <el-table
                :data="tagList"
                style="width: 100%"
                :height="tableHeight+'px'"
                show-overflow-tooltip
                v-loading="tableLoading"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="标签名称" prop="tagName" style="min-width: 300">
                    <template #default="scope">
                        <el-tag
                            color="#FFFFFF"
                            size="large"
                            :style="{
                                color: scope.row.color, 
                                borderColor:'#ffffff',
                                backgroundColor: getRgbaColor(scope.row.color, 0.15),
                            }"
                        >
                            {{ scope.row.tagName }}
                        </el-tag>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="标签颜色" prop="color" style="min-width: 300">
                    <template #default="scope">
                        <div
                            class="color-icon"
                            :style="{ backgroundColor: scope.row.color }"
                        >
                        </div>
                    </template>
                </el-table-column> -->
                <el-table-column label="创建时间" prop="createTime" style="min-width: 300">
                    <template #default="scope">
                        <div>
                            {{
                                scope.row.createTime
                                    ? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="创建人" prop="nickname" style="min-width: 300"></el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="150px"
                >
                    <template #default="scope">
                        <div class="display-flex gap-10">
                            <a :class="['color-blue', permissionService.isTagMenuEditPermitted() ? 'pointer' : 'not-allow']" @click="permissionService.isTagMenuEditPermitted() && edit(scope.row)">编辑</a>
                            <a :class="['color-red', permissionService.isTagMenuDeletePermitted() ? 'pointer' : 'not-allow']" @click="permissionService.isTagMenuDeletePermitted() && confirmRemove(scope.row)">删除</a>
                        </div>  
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    /> 
                </div>
            </el-affix>    
        </div>
    </div>
    <!-- 新增/编辑弹窗 -->
    <AddTag v-model:visible="addTagDialogVisible" :refresh="getTagList"></AddTag>
    <EditTag v-if="currentRow" v-model:visible="editTagDialogVisible" :editRowData="currentRow" :refresh="getTagList"></EditTag>
    <!-- 删除标签时统计有无绑定企业 -->
    <CountTag v-if="countData && countData.length > 0" v-model:visible="countTagDialogVisible" :countData="countData" :tagId="tagId" :refresh="getTagList"></CountTag>
</template>

<script lang='ts' setup>
import type { IPageInfo, IGetTagList, ITagCountResponseData } from '@/types/lead'
import searchBox from '@/components/common/SearchBox.vue'
import AddTag from './components/AddTag.vue'
import EditTag from './components/EditTag.vue'
import CountTag from './components/CountTag.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance, ref,reactive, onMounted, onBeforeMount} from 'vue'
import crmService from '@/service/crmService'
import systemService from '@/service/systemService'
import permissionService from '@/service/permissionService'

const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
const tableHeight = ref(500)
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - actionBarContentRef.value.clientHeight - 32 - 16 - 16 - 16 - 16 - 16
    }
}
type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}
const searchConfig = ref<CustomConfig>()
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const tableLoading = ref(false)
const currentRow = ref<IGetTagList>()
const tagList = ref<IGetTagList[]>([])
const type = ref('')
const countData = ref<ITagCountResponseData[]>([])
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

const queryParams = ref<IPageInfo>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
})

const getRgbaColor = (color: string, alpha: number) => {
    color = color.replace('#', '')
    const r = parseInt(color.substring(0, 2), 16)
    const g = parseInt(color.substring(2, 4), 16)
    const b = parseInt(color.substring(4, 6), 16)
    // console.log(`rgba(${r}, ${g}, ${b}, ${alpha})`)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getTagList()
}

const getTagList = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.pageSize = pageInfo.pageSize
    crmService.crmTagList(queryParams.value).then((res) => {
        if(res.success){
            // console.log('getTagList',res.data)
            tagList.value = res.data
            pageInfo.total = res.total
        }else{
            ElMessage.error(`系统繁忙,请稍后重试`)
        }
    }).finally(() => {
        tableLoading.value = false
    })
}

const updateSearchParams = (params: IPageInfo) => {
    queryParams.value = params
    getTagList()
}

const addTagDialogVisible = ref(false)
const editTagDialogVisible = ref(false)
const countTagDialogVisible = ref(false)
const dialogTitle = ref('')
const handleAdd = () => {
    type.value = 'add'
    // console.log('handleAdd')
    addTagDialogVisible.value = true
    dialogTitle.value = '新增标签'
}

const remove = (id: string) => {
    crmService.crmTagDelete(id).then ((res) => {
        if (res.success) {
            ElMessage.success('删除成功')
            getTagList()
        }else{
            ElMessage.error(res.errMsg)
        }
    })
}
const tagId = ref('')

const confirmRemove = (row: IGetTagList) => {
    // 新增接口进行统计，查询是否有绑定的企业
    crmService.crmTagCount({tagId:row.id})
        .then ((res) => {
            if(res.success){
                // console.log('res1111',res.data)
                if (res.data && res.data.length === 0 ){
                    ElMessageBox.confirm(`确认删除【${row.tagName}】吗？`,'提示',{
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }).then(() => {
                        remove(row.id)
                    })
                }else{
                    countData.value = res.data
                    // console.log('res2222',countData.value)
                    tagId.value = row.id
                    countTagDialogVisible.value = true
                }
            }else{
                ElMessage.error(res.errMsg)
            }

        })
    // console.log('confirmRemove',row)
}

const edit = (row: IGetTagList) => {
    // console.log('edit',row)
    type.value = 'edit'
    editTagDialogVisible.value = true
    dialogTitle.value = '编辑'
    currentRow.value = row 
}

onBeforeMount(() => {
})

onMounted(() => {
    systemService.userGetUserByScopeData('collect').then(response => {
        searchConfig.value = {
            ...searchConfig.value,
            createUser:response.data.map(item => ({ 
                value: item.id,
                label: item.nickname 
            }))
        }
    })
    getTagList()
    getTableHeight()
})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';
.color-icon{
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
}
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}


</style>