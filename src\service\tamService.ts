import http from '@/axios'
import type { ICommonResponse } from '@/types/axios'
import type {
    IApplyWorkNumberParams,
    IApplyNumberListParams,
    IApplyNumberListTableData,
    IWorkNumberListParams,
    IWorkNumberListTableData,
    IBindWorkNumberParams,
    IBindTaxOfficerParams,
    IBindCompanyParams,
    IGetTaxOfficerInfoParams,
    IGetTaxOfficerInfoData,
    IBindCompanyTableParams,
    IBindCompanyTableData,
    IWorkNumberSMSParams,
    IWorkNumberSMSTableData,
    IWorkCompanyAuthParams,
    IWorkCompanyAuthData,
} from '@/types/worknumber'
   
export default {
    // 申请小号
    applyWorkNumber(data:IApplyWorkNumberParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-tam/work/number/applyWorkNumber`,data,{
            hideError: true,
        })
    },
    // 小号申请订单列表
    applyWorkNumberList(data:IApplyNumberListParams):Promise<IApplyNumberListTableData>{
        return http.post(`/api/zhenqi-tam/work/number/applyWorkNumberList`, data)
    },
    // 小号订单详情/列表
    workNumberList(data:IWorkNumberListParams):Promise<IWorkNumberListTableData>{
        return http.post(`/api/zhenqi-tam/work/number/workNumberList`,data)
    },
    // 小号绑定/解绑/换绑
    workNumberBind(data:IBindWorkNumberParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-tam/work/number/bindNum`,data,{
            hideError: true,
        })
    },
    // 办税员配置
    taxOfficerConfig(data:IBindTaxOfficerParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-tam/work/number/account/save`,data,{
            hideError: true,
        })
    },
    // 绑定企业
    bindEnterprise(data:IBindCompanyParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-tam/work/number/bindBsy`,data,{
            hideError: true,
        })
    },
    // 解绑企业
    unbindEnterprise(data:IBindCompanyParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-tam/work/number/unbindBsy`,data,{
            hideError: true,
        })
    },
    // 查询办税员信息
    getTaxOfficerInfo(data:IGetTaxOfficerInfoParams):Promise<IGetTaxOfficerInfoData>{
        return http.post(`/api/zhenqi-tam/work/number/account/detail`,data,{
            hideError: true,
        })
    },
    // 查询绑定企业列表
    bsyBindEntList(data:IBindCompanyTableParams):Promise<IBindCompanyTableData>{
        return http.post(`/api/zhenqi-tam/work/number/bsyBindEntList`,data)
    },
    // 办税小号短信列表
    WorkNumberSMSList(data:IWorkNumberSMSParams):Promise<IWorkNumberSMSTableData>{
        return http.post(`/api/zhenqi-tam/work/number/smsList`,data)
    },
    // 获取添加办税员链接
    companyAuth(data:IWorkCompanyAuthParams):Promise<IWorkCompanyAuthData>{
        return http.post(`/api/zhenqi-tam/work/number/company/auth`,data)
    },
    // 获取确认办税员链接
    bsyConfirm(data:{bsysjhm:string}):Promise<IWorkCompanyAuthData>{
        return http.post(`/api/zhenqi-tam/auth/bsy/s/confirm`,data)
    },
    // 添加手机号码链接
    addMobile(data:{bsysjhm:string,channelId:string}):Promise<IWorkCompanyAuthData>{
        return http.post(`/api/zhenqi-tam/auth/addMobile`,data)
    },
}