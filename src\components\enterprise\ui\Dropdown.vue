<script lang="ts" setup>
import type { IAicNormalSearchRules } from '@/types/aic'
import { ArrowDownBold, ArrowUpBold } from '@element-plus/icons-vue'
import { ref, watch } from 'vue'

const props = defineProps<{
    label: string
    value: string
    onChange: (value: string) => void
    checked: boolean
    current: IAicNormalSearchRules | null
}>()
const checked = ref(props.checked)
const onCheck = () => {
    checked.value = !checked.value
    props.onChange(props.value)
}

watch(
    () => props.checked,
    (value) => {
        checked.value = value
    }
)

watch(
    () => props.current,
    (value) => {
        const { key } = value || {}
        if (key === props.value) {
            checked.value = true
        } else {
            checked.value = false
        }
    }
)
</script>

<template>
    <div class="flex flex-row left-right-center gap-12 pointer no-select" @click="onCheck">
        <div
            :class="{
                '!color-blue': checked,
            }"
            class="font-14 color-black lh-24"
        >
            {{ props.label }}
        </div>
        <div class="flex flex-row left-right-center top-bottom-center">
            <el-icon
                :size="12"
                class="color-three-grey"
                :class="{
                    '!color-blue': checked,
                }"
            >
                <ArrowDownBold v-show="!checked" />
                <ArrowUpBold v-show="checked" />
            </el-icon>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
