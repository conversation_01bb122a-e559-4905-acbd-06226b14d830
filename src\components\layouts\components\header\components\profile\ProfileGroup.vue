<script lang="ts" setup>
import { CurrentOrg, Footer, Menu, Profile, Tags } from './components/profile-panel'

const props = defineProps<{
    showSwitcher: () => void
}>()
</script>

<template>
    <div class="flex width-100 flex flex-column gap-4">
        <div class="all-padding-16 main">
            <Profile />
            <Tags />
            <Menu />
        </div>

        <CurrentOrg :showSwitcher="props.showSwitcher" />
        <div class="split-line"></div>
        <Footer />
    </div>
</template>

<style lang="scss" scoped>
.main {
    background-image: url(/src/assets/images/header/profile-bg.png);
    background-size: inherit;
    background-position: top;
    background-repeat: no-repeat;
}
</style>
