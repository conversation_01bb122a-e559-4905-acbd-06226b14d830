<template>
    <div class="tool-box">
        <div @click.stop="selectRange" @mouseover="selectFlag = false" @mouseout="selectFlag = true">
            <div class="btn-box circle-btn" v-show="selectFlag"></div>
            <div class="btn-box clear-btn-ac display-flex top-bottom-center left-right-center" v-show="!selectFlag">
                <div>
                    <div>画 圈</div>
                    <div class="t-margin-4">标 记</div>
                </div>
            </div>
        </div>
        <div @click.stop="clearRange(false)" @mouseover="clearFlag = false" @mouseout="clearFlag = true">
            <div class="btn-box clear-btn" v-show="clearFlag"></div>
            <div class="btn-box clear-btn-ac display-flex top-bottom-center left-right-center" v-show="!clearFlag">
                <div>
                    <div>清 除</div>
                    <div class="t-margin-4">定 位</div>
                </div>
            </div>
        </div>
        <div class="back-center-btn" @click="(clearRange(false), backCenter)"></div>
    </div>
    <div id="container"></div>
</template>

<script lang="ts" setup>
import AMapLoader from '@amap/amap-jsapi-loader'
import { ref, onMounted, defineEmits, computed, defineExpose } from 'vue'
import type { Ref } from 'vue'
import '@amap/amap-jsapi-types'
import mapLocation from '@/assets/images/map/map-location.png'

interface marker {
    position: string
    title: string
    icon: string
}

interface markerData {
    lon: number
    lat: number
    companyName: string
}

const emits = defineEmits(['setMapInfo'])

const zoom: Ref<number> = ref(0)

const radius: Ref<number> = ref(2000)

const tm = ref(null)

const markers: Ref<marker[]> = ref([])

const labels: Ref<marker[]> = ref([])

const circles: Ref<marker[]> = ref([])

const model: Ref<string> = ref('normal')

const rangeCenter: Ref<string> = ref('')

const clearFlag: Ref<boolean> = ref(true)

const selectFlag: Ref<boolean> = ref(true)

const scale = computed((): number => {
    let scale = 0
    switch (parseInt(zoom.value)) {
    case 1:
        scale = 10000000
        break
    case 2:
        scale = 5000000
        break
    case 3:
        scale = 2000000
        break
    case 4:
        scale = 1000000
        break
    case 5:
        scale = 500000
        break
    case 6:
        scale = 200000
        break
    case 7:
        scale = 100000
        break
    case 8:
        scale = 50000
        break
    case 9:
        scale = 25000
        break
    case 10:
        scale = 20000
        break
    case 11:
        scale = 10000
        break
    case 12:
        scale = 5000
        break
    case 13:
        scale = 2000
        break
    case 14:
        scale = 1000
        break
    case 15:
        scale = 500
        break
    case 16:
        scale = 200
        break
    case 17:
        scale = 100
        break
    case 18:
        scale = 50
        break
    case 19:
        scale = 20
        break
    default:
        scale = 0
        break
    }
    return scale
})
const mapChange = () => {
    if (model.value !== 'range') {
        getCenter()
    }
}

const clearAll = () => {
    window.map.remove(labels.value)
    window.map.getAllOverlays('marker').forEach((overlay) => {
        window.map.remove(overlay)
    })
}

const addMarkers = (items: markerData[]) => {
    clearAll()
    console.log('addMarkers', items)

    markers.value = []
    items.forEach((item) => {
        let marker = new window.AMap.Marker({
            position: new window.AMap.LngLat(item.lon, item.lat),
            title: item.companyName,
            icon: mapLocation,
        })
        markers.value.push(marker)
    })
    window.map.add(markers.value)
}

const addCircle = (list) => {
    console.log('----=list', list)

    console.log('scale.value', scale.value)
    if (scale.value === 0) {
        return
    }
    circles.value = list.map((item) => {
        let lat = item.center.split(',')[1]
        let lon = item.center.split(',')[0]
        return {
            styleId: 'circle', //指定样式id
            center: new window.AMap.LngLat(lon, lat), //点标记坐标位置
            radius: scale.value / 1700,
            id: item.id,
            name: item.name,
            count: item.count / 10000,
            code: item.code,
        }
    })
    clearAll()
    circles.value.forEach((circle) => {
        // let circleMarker = new window.AMap.CircleMarker({
        //     center: circle.center,
        //     radius: 40,
        //     strokeColor: '#009b9b',
        //     fillColor: '#00c2c2',
        //     fillOpacity: '1',
        //     cursor: 'pointer',
        //     zIndex: idx
        // })
        let text = new window.AMap.Text({
            position: circle.center,
            anchor: 'bottom-center',
            text: circle.name + '<br>约' + circle.count + '万条',
            style: {
                'background-color': '#00c2c2',
                'font-size': '12px',
                color: 'white',
                'text-align': 'center',
                width: '80px',
                height: '80px',
                'border-radius': '80px',
                border: '1px solid #009b9b',
                display: 'flex',
                'align-items': 'center',
                'justify-content': 'center',
            },
            offset: [0, 15],
        })
        console.log(text)
        labels.value.push(text)
        // circleMarker.on('click', circleCk)
        text.on('click', circleCk)
        // circleMarker.setMap(window.map)
        // window.map.add(text)
        text.setMap(window.map)
    })
}
const circleCk = (evt) => {
    console.log('circleCk', evt)

    if (zoom.value > 10) {
        //区级
        window.map.setZoom(13)
    } else if (zoom.value > 6) {
        //市级
        window.map.setZoom(11)
    } else {
        //省级
        window.map.setZoom(7)
    }

    window.map.setCenter(evt.lnglat)
}
const getCenter = async (params = null) => {
    if (tm.value) {
        clearTimeout(tm.value)
    }

    tm.value = setTimeout(async () => {
        let centerLocation = params?.customCenter || window.map.getCenter()
        // this.addMarkers([]);
        addMarkers([])
        zoom.value = window.map.getZoom()

        console.log('zoom.value', zoom.value)

        window.AMap.plugin('AMap.Geocoder', () => {
            var geocoder = new window.AMap.Geocoder()

            var lnglat = [centerLocation.lng, centerLocation.lat]

            console.log('lnglat', lnglat)
            geocoder.getAddress(lnglat, (status: string, result) => {
                console.log('-=-=-', status, result)
                if (status === 'complete' && result.info === 'OK') {
                    let addressInfo = result.regeocode.addressComponent

                    emits('setMapInfo', {
                        location: {
                            ad_info: { adcode: addressInfo.adcode },
                            location: {
                                lng: lnglat[0],
                                lat: lnglat[1],
                            },
                        },
                        zoom: zoom.value,
                        radius: params?.customCenter ? radius.value : null,
                    })
                }
            })
        })
    }, 1000)
}
const backCenter = () => {
    window.map.setZoom(11)
    window.AMap.plugin(['AMap.Geolocation'], () => {
        let geolocation = new window.AMap.Geolocation({
            enableHighAccuracy: true, //是否使用高精度定位，默认:true
            timeout: 10000, //超过10秒后停止定位，默认：无穷大
            maximumAge: 0, //定位结果缓存0毫秒，默认：0
            convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
            showButton: true, //显示定位按钮，默认：true
            buttonPosition: 'RB', //定位按钮停靠位置，默认：'LB'，左下角
            buttonOffset: new window.AMap.Pixel(10, 20), //定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
            showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
            showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
            panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
            zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        })

        geolocation.getCityInfo(
            (
                status: string,
                result: {
                    position: {
                        lat: number
                        lng: number
                    }
                }
            ) => {
                console.log('定位结果', status, result)
                if (status === 'complete') {
                    console.log('定位成功:', result.position)
                    // 将地图中心设置为当前定位
                    window.map.setCenter(result.position)
                    getCenter()
                } else {
                    console.error('定位失败:', result)
                }
            }
        )
    })
}

const initMap = () => {
    if (window.map?.destroy) {
        window.map.destroy()
    }

    AMapLoader.load({
        key: '4fd1eb3675d6bede8f68d11db2292341',
        version: '2.0',
        plugins: ['AMap.ToolBar', 'AMap.Driving', 'AMap.Geocoder', 'AMap.CitySearch', 'AMap.CircleEditor'],
        AMapUI: {
            version: '1.1',
            plugins: [],
        },
        Loca: {
            version: '2.0.0',
        },
    })
        .then((AMap) => {
            window.map = new AMap.Map('container', {
                viewMode: '2D',
                zooms: [3, 20],
                zoom: 12,
            })

            window.map.on('dragend', mapChange)
            window.map.on('zoomend', mapChange)

            backCenter()
        })
        .catch((e) => {
            console.log('地图加载错误', e)
        })
}

const circleRange = ref()

const circleEditor = ref(null)
const clearRange = (flag: boolean) => {
    model.value = 'normal'
    radius.value = 2000
    clearAll()
    window.map.clearMap()
    window.map.off('click', addRange)
    if (circleRange.value) {
        window.map.remove([circleRange.value])
        circleRange.value = null
    }
    if (circleEditor.value) {
        circleEditor.value.close()
        circleEditor.value = null
    }
    if (!flag) {
        backCenter()
    }
}

const circleEditorEvent = () => {
    circleEditor.value._editStyle = {
        strokeColor: '#1966ff', //线颜色
        strokeOpacity: 1, //线透明度
        strokeWeight: 1, //线粗细度
        strokeStyle: 'dashed',
        fillColor: '#1966ff', //填充颜色
        fillOpacity: 0.1, //填充透明度
    }
    circleEditor.value.on('move', (e) => {
        rangeCenter.value = e.lnglat
        getCenter({ customCenter: e.lnglat })
    })
    circleEditor.value.on('adjust', (e) => {
        radius.value = e.radius
        getCenter({ customCenter: rangeCenter.value })
    })
}
const addRange = (e) => {
    console.log('addRange', zoom.value)
    let centerLocation = e.lnglat
    if (zoom.value < 13) {
        window.map.setZoom(13)
    }
    window.map.setCenter(centerLocation)

    rangeCenter.value = centerLocation

    let circleRange = new window.AMap.Circle({
        center: [centerLocation.lng, centerLocation.lat],
        radius: 2000,
        strokeColor: 'rgba(64, 158, 255, 1)', //线颜色
        strokeOpacity: 1, //线透明度
        strokeWeight: 1, //线粗细度
        strokeStyle: 'dashed',
        fillColor: 'rgba(64, 158, 255, 0.1)', //填充颜色
        fillOpacity: 0.2, //填充透明度
    })
    window.map.add(circleRange)
    radius.value = 2000
    getCenter({ customCenter: centerLocation })
    circleEditor.value = new window.AMap.CircleEditor(window.map, circleRange)
    circleEditorEvent()
    circleEditor.value.open()
    window.map.off('click', addRange)
}
const selectRange = () => {
    clearRange(true)
    model.value = 'range'

    window.map.on('click', addRange)
}

defineExpose({
    addCircle,
    model,
    rangeCenter,
    getCenter,
    addMarkers,
})

onMounted(() => {
    initMap()
})
</script>

<style lang="scss" scoped>
#container {
    height: calc(100vh - 100px);
    width: 100%;
    padding: 0px;
    margin: 0px;
    overflow: hidden;
    z-index: 998; // 解决地图查找页面无法点击退出登录
}

.amap-icon {
    width: 25px !important;
    height: 34px !important;
}

.tool-box {
    position: absolute;
    right: 16px;
    top: 120px;
    z-index: 1000;

    > div {
        display: flex;
        list-style: none;
        font-size: 12px;
        align-items: center;
        cursor: pointer;
        width: 50px;
        height: 50px;
        align-items: center;
        border-radius: 8px;
        margin-bottom: 8px;
        justify-content: center;
        background-color: var(--main-white);
        box-shadow: 0px 0px 12px rgba(25, 102, 255, 0.4);
        overflow: hidden;
    }

    // div:hover {
    //     background-size: 45px;
    // }

    .btn-box {
        width: 52px;
        height: 52px;
    }

    .circle-btn {
        background-image: url('@/assets/images/map/location-drawer-circle.png');
        /* 替换为你的图片路径 */
        background-size: 35px;

        background-position: center;
        background-repeat: no-repeat;
    }

    .clear-btn {
        background-image: url('@/assets/images/map/location-clear-circle.png');
        /* 替换为你的图片路径 */
        background-size: 35px;
        background-position: center;
        background-repeat: no-repeat;
    }

    .back-center-btn {
        background-image: url('@/assets/images/map/location-back-center.png');
        /* 替换为你的图片路径 */
        background-size: 35px;
        background-position: center;
        background-repeat: no-repeat;
    }

    .clear-btn-ac {
        color: var(--main-white);
        background-color: var(--main-blue-);
    }
}
</style>
