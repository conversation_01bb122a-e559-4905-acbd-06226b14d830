type TimeInput = string | number | Date
type FormatToken = 'y' | 'm' | 'd' | 'h' | 'i' | 's' | 'a'

/**
 * 解析时间并格式化输出
 * @param input 时间输入（支持 Date、时间戳、日期字符串）
 * @param pattern 格式化模板（默认：'{y}-{m}-{d} {h}:{i}:{s}'）
 * @returns 格式化后的时间字符串，无效输入返回 null
 */
export const parseTime = (input: TimeInput, pattern: string = '{y}-{m}-{d} {h}:{i}:{s}'): string | null => {
    if (!input) return null

    // 解析日期对象 ---------------------------------------------------
    const date = parseDate(input)
    if (!date || isNaN(date.getTime())) return null

    // 格式化逻辑 -----------------------------------------------------
    const formatMap: Record<FormatToken, string | number> = {
        y: date.getFullYear(),
        m: padZero(date.getMonth() + 1),
        d: padZero(date.getDate()),
        h: padZero(date.getHours()),
        i: padZero(date.getMinutes()),
        s: padZero(date.getSeconds()),
        a: ['日', '一', '二', '三', '四', '五', '六'][date.getDay()],
    }

    // 使用预编译正则提升性能
    return pattern.replace(/\{(y|m|d|h|i|s|a)\}/g, (_, key) => String(formatMap[key as FormatToken]))
}

/** 解析不同格式的时间输入 */
const parseDate = (input: TimeInput): Date | null => {
    try {
        if (input instanceof Date) return new Date(input)

        if (typeof input === 'number') {
            // 智能识别秒/毫秒时间戳（阈值 2001-09-09）
            return new Date(input < 1e12 ? input * 1000 : input)
        }

        if (typeof input === 'string') {
            // 统一处理为 ISO 格式提高兼容性
            const isoString = input
                .replace(/(\d{4})-(\d{2})-(\d{2})T?/, '$1/$2/$3 ') // 处理日期部分
                .replace(/\.\d+/, '') // 移除毫秒
                .trim()

            return new Date(isoString)
        }

        return null
    } catch {
        return null
    }
}

/** 补零函数 */
const padZero = (value: number): string => value.toString().padStart(2, '0')

export const getLastYearRange = () => {
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth() // 0-11
    
    // 计算结束年月（上月）
    let endYear = currentYear
    let endMonth = currentMonth - 1
    
    if (endMonth < 0) {
        endYear -= 1
        endMonth = 11
    }
    
    // 计算开始年月（往前推11个月）
    let startYear = endYear
    let startMonth = endMonth - 11
    
    while (startMonth < 0) {
        startYear -= 1
        startMonth += 12
    }
    
    // 格式化函数
    const formatYM = (year: number, month: number) => {
        const monthStr = (month + 1).toString().padStart(2, '0')
        return `${year}-${monthStr}`
    }
    
    return {
        start: formatYM(startYear, startMonth),
        end: formatYM(endYear, endMonth)
    }
}
