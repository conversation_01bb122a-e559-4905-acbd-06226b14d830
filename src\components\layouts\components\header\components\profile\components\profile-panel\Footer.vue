<script lang="ts" setup>
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'

const store = useStore<RootState>()
const logout = async () => {
    store.dispatch('auth/logout')
}
</script>

<template>
    <div class="all-padding-12">
        <div class="flex top-bottom-center left-right-center pointer" @click="logout">退出登录</div>
    </div>
</template>

<style lang="scss" scoped></style>
