import { type RouteRecordRaw } from 'vue-router'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import Home from '@/views/home/<USER>'
import Search from '@/views/find-companies/search/Search.vue'
import BasicSearch from '@/views/find-companies/search/basic-search/BasicSearch.vue'
import MapSearch from '@/views/find-companies/map-search/MapSearch.vue'
import AdvanceSearch from '@/views/find-companies/advance-search/AdvanceSearch.vue'
import CompanyProfile from '@/views/find-companies/company-profile/CompanyProfile.vue'
import LeadPool from '@/views/leads/lead-pool/LeadPool.vue'
import LeadList from '@/views/leads/lead-list/LeadList.vue'
import CustomerPublicPool from '@/views/leads/customer-public-pool/CustomerPublicPool.vue'
import CustomerList from '@/views/leads/customer-list/CustomerList.vue'
import DialingTasks from '@/views/auto-dialer/dialing-tasks/DialingTasks.vue'
import DialingContacts from '@/views/auto-dialer/dialing-contacts/DialingContacts.vue'
import SMSSettings from '@/views/operations/sms-settings/SMSSettings.vue'
import PolicyManagement from '@/views/operations/policy-management/PolicyManagement.vue'
import FinancialProducts from '@/views/operations/financial-products/FinancialProducts.vue'
import RiskMonitoring from '@/views/risk-management/risk-monitoring/RiskMonitoring.vue'
import RiskAlerts from '@/views/risk-management/risk-alerts/RiskAlerts.vue'
import BenefitList from '@/views/user-benefits/benefit-list/BenefitList.vue'
import CollectionRecords from '@/views/user-benefits/collection-records/CollectionRecords.vue'
import InternalOrganization from '@/views/system-management/internal-organization/InternalOrganization.vue'
import CRMTagging from '@/views/system-management/crm-tagging/CRMTagging.vue'
import BusinessSettings from '@/views/system-management/business-settings/BusinessSettings.vue'
import LeadPoolAdmin from '@/views/system-management/lead-pool-admin/LeadPoolAdmin.vue'
import PublicPoolAdmin from '@/views/system-management/public-pool-admin/PublicPoolAdmin.vue'
import TenantManagement from '@/views/system-management/tenant-management/TenantManagement.vue'
import DataChannel from '@/views/system-management/data-channel/DataChannel.vue'
import ProjectClassificationDetail from '@/views/operations/project-classification/EditProjectClassification.vue'
import ProjectAdvanceTemplate from '@/views/operations/project-advance-template/ProjectAdvanceTemplate.vue'
import ProjectClassification from '@/views/operations/project-classification/ProjectClassification.vue'
import BidSearch from '@/views/find-companies/search/bid-search/BidSearch.vue'
import FactorySearch from '@/views/find-companies/search/factory-search/FactorySearch.vue'
import DialingTaskDetail from '@/views/auto-dialer/dialing-task-detail/DialingTaskDetail.vue'
import MenuManagement from '@/views/system-management/menu-management/MenuManagement.vue'
import TaskManagement from '@/views/system-management/task-management/TaskManagement.vue'
import RecentRegSearch from '@/views/find-companies/search/recent-reg-search/RecentRegSearch.vue'
import BenefitManagement from '@/views/user-benefits/benefit-management/BenefitManagement.vue'
import BenefitRecord from '@/views/user-benefits/benefit-record/BenefitRecord.vue'
import OEMManagement from '@/views/system-management/oem-management/OEMManagement.vue'
import UserManagement from '@/views/system-management/user-management/UserManagement.vue'
import BenefitGood from '@/views/user-benefits/benefit-good/BenefitGood.vue'
import AccountManagement from '@/views/system-management/account-management/AccountManagement.vue'
import BusinessEntry from '@/views/operations/business-entry/BusinessEntry.vue'
import ApplyNumberList from '@/views/work-phone-number/ApplyNumberList.vue'
import PhoneList from '@/views/work-phone-number/WorkNumberList.vue'
import BindCompanyList from '@/views/work-phone-number/BindCompanyList.vue'
import WorkNumberSMS from '@/views/work-phone-number/WorkNumberSMS.vue'
import InviteManagement from '@/views/operations/invite-management/InviteManagement.vue'

const clientRoutes: Array<RouteRecordRaw> = [
    {
        path: '/',
        component: DefaultLayout,
        redirect: '/index',
        meta: { rely: true },
        children: [
            {
                path: '/index',
                name: 'index',
                component: Home,
                meta: { requiresAuth: true, title: '首页' },
            },
        ],
    },
    {
        path: '/find-companies',
        redirect: '/find-companies/search',
        name: 'search',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '查找企业' },
        children: [
            {
                path: 'search',
                name: 'search-company',
                component: Search,
                meta: { requiresAuth: true, title: '找企业', fullContainer: true },
            },
            {
                path: 'search/basic/:tagTypeVal?/:keyword?',
                name: 'com-search-company',
                component: BasicSearch,
                meta: { requiresAuth: true, title: '智能搜索', rely: true, defaultActive: 'search-company' },
            },
            {
                path: 'search/advance/:templeteId?/:type?',
                name: 'more-search-company',
                component: AdvanceSearch,
                meta: { requiresAuth: true, title: '高级搜索' },
            },
            {
                path: 'search/map',
                name: 'company-map',
                component: MapSearch,
                meta: { requiresAuth: true, title: '地图搜索', fullContainer: true, hideRouterBread: true },
            },
            {
                path: 'search/bid',
                name: 'bid-search',
                component: BidSearch,
                meta: { requiresAuth: true, title: '搜招标投标', rely: true, defaultActive: 'search-company' },
            },
            {
                path: 'search/factory',
                name: 'factory-search',
                component: FactorySearch,
                meta: { requiresAuth: true, title: '搜工厂', rely: true, defaultActive: 'search-company' },
            },
            {
                path: 'search/recent',
                name: 'recent-search',
                component: RecentRegSearch,
                meta: { requiresAuth: true, title: '最新注册', rely: true, defaultActive: 'search-company' },
            },
        ],
    },
    {
        path: '/find-companies',
        component: DefaultLayout,
        meta: { rely: true },
        children: [
            {
                path: '/company-profile/:socialCreditCode',
                name: 'company-profile',
                component: CompanyProfile,
                meta: { requiresAuth: true, title: '企业详情', rely: true },
            },
        ],
    },
    {
        path: '/leads',
        redirect: '/leads/lead-pool',
        name: 'su-crm',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '客户线索' },
        children: [
            {
                path: 'lead-pool',
                name: 'su-crm-lead-pool',
                component: LeadPool,
                meta: { requiresAuth: true, title: '线索池' },
            },
            {
                path: 'lead-list',
                name: 'su-crm-lead-list',
                component: LeadList,
                meta: { requiresAuth: true, title: '线索列表' },
            },
            {
                path: 'customer-public-pool',
                name: 'crm-customer-poll',
                component: CustomerPublicPool,
                meta: { requiresAuth: true, title: '客户公海' },
            },
            {
                path: 'customer-list',
                name: 'su-crm-customer-list',
                component: CustomerList,
                meta: { requiresAuth: true, title: '客户列表' },
            },
        ],
    },
    {
        path: '/risk-management',
        redirect: '/auto-dialer/risk-monitoring',
        name: 'company-manage',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '风险管理' },
        children: [
            {
                path: 'risk-monitoring',
                name: 'risk-monitor',
                component: RiskMonitoring,
                meta: { requiresAuth: true, title: '风险监控' },
            },
            {
                path: 'risk-alerts',
                name: 'risk-manage',
                component: RiskAlerts,
                meta: { requiresAuth: true, title: '风险列表' },
            },
        ],
    },
    {
        path: '/data-center',
        name: 'data-center',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '数据中心' },
        children: [],
    },
    {
        path: '/auto-dialer',
        redirect: '/auto-dialer/dialing-task',
        name: 'ai-phone',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '智能外呼' },
        children: [
            {
                path: 'dialing-task',
                name: 'ai-phone-task',
                component: DialingTasks,
                meta: { requiresAuth: true, title: '外呼任务' },
            },
            {
                path: 'dialing-contacts',
                name: 'ai-phone-customer',
                component: DialingContacts,
                meta: { requiresAuth: true, title: '外呼客户' },
            },
            {
                path: 'dialing-task/detail',
                name: 'dialing-task-detail',
                component: DialingTaskDetail,
                meta: { requiresAuth: true, title: '任务详情', rely: true, defaultActive: 'ai-phone-task' },
            },
        ],
    },
    {
        path: '/operations',
        redirect: '/operations/policy-management',
        name: 'operate-manage',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '运营管理' },
        children: [
            {
                path: 'sms-settings',
                name: 'sms-model-management',
                component: SMSSettings,
                meta: { requiresAuth: true, title: '短信配置' },
            },
            {
                path: 'policy-management',
                name: 'policy-manage',
                component: PolicyManagement,
                meta: { requiresAuth: true, title: '政策管理' },
            },
            {
                path: 'financial-products',
                name: 'product-manage',
                component: FinancialProducts,
                meta: { requiresAuth: true, title: '金融产品管理' },
            },
            {
                path: 'project-classification',
                name: 'search-model-category',
                component: ProjectClassification,
                meta: { requiresAuth: true, title: '项目分类' },
            },
            {
                path: 'project-classification-detail/:id',
                name: 'search-model-category-detail',
                component: ProjectClassificationDetail,
                meta: { requiresAuth: true, title: '编辑图谱', rely: true },
            },
            {
                path: 'project-advance-template',
                name: 'search-formwork-list',
                component: ProjectAdvanceTemplate,
                meta: { requiresAuth: true, title: '高级搜索模板', rely: true },
            },
            {
                path: 'business-entry',
                name: 'business-entry',
                component: BusinessEntry,
                meta: { requiresAuth: true, title: '业务进件' },
            },
            {
                path: 'invite-management',
                name: 'invite-manage',
                component: InviteManagement,
                meta: { requiresAuth: true, title: '邀请管理' },
            },
        ],
    },
    {
        path: '/user-benefits',
        redirect: '/user-benefits/collection-records',
        name: 'user-overview',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '权益中心' },
        children: [
            {
                path: 'benefit-list',
                name: 'benefit-list',
                component: BenefitList,
                meta: { requiresAuth: true, title: '权益列表' },
            },
            {
                path: 'collection-records',
                name: 'collect-log',
                component: CollectionRecords,
                meta: { requiresAuth: true, title: '采集记录' },
            },
            {
                path: 'benefit-management',
                name: 'benefit-management',
                component: BenefitManagement,
                meta: { requiresAuth: true, title: '权益管理' },
            },
            {
                path: 'benefit-record',
                name: 'benefit-record',
                component: BenefitRecord,
                meta: { requiresAuth: true, title: '消费明细' },
            },
            {
                path: 'benefit-good',
                name: 'benefit-good',
                component: BenefitGood,
                meta: { requiresAuth: true, title: '服务订购' },
            },
        ],
    },
    {
        path: '/system-management',
        redirect: '/system-management/internal-organization',
        name: 'system-management',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '系统管理' },
        children: [
            {
                path: 'data-channel',
                name: 'data-channel',
                component: DataChannel,
                meta: { requiresAuth: true, title: '数据通道' },
            },
            {
                path: 'menu-management',
                name: 'system_menu',
                component: MenuManagement,
                meta: { requiresAuth: true, title: '菜单管理' },
            },
            {
                path: 'internal-organization/:type?/:name?',
                name: 'mail-list',
                component: InternalOrganization,
                meta: { requiresAuth: true, title: '内部组织管理' },
            },
            {
                path: 'crm-tagging',
                name: 'crm-tag',
                component: CRMTagging,
                meta: { requiresAuth: true, title: '企业标签' },
            },
            {
                path: 'business-settings',
                name: 'tenant-settings',
                component: BusinessSettings,
                meta: { requiresAuth: true, title: '业务配置' },
            },
            {
                path: 'lead-pool-admin',
                name: 'lead-pool-management',
                component: LeadPoolAdmin,
                meta: { requiresAuth: true, title: '线索池管理' },
            },
            {
                path: 'public-pool-admin',
                name: 'customer-pool',
                component: PublicPoolAdmin,
                meta: { requiresAuth: true, title: '客户公海管理' },
            },
            {
                path: 'task-management',
                name: 'task-management',
                component: TaskManagement,
                meta: { requiresAuth: true, title: '任务管理' },
            },
            {
                path: 'tenant-management',
                name: 'tenant-management',
                component: TenantManagement,
                meta: { requiresAuth: true, title: '租户管理' },
            },
            {
                path: 'oem-management',
                name: 'su-oem',
                component: OEMManagement,
                meta: { requiresAuth: true, title: 'OEM管理' },
            },
            {
                path: 'user-management',
                name: 'system_user',
                component: UserManagement,
                meta: { requiresAuth: true, title: '用户管理' },
            },
            {
                path: 'account-management',
                name: 'system_account',
                component: AccountManagement,
                meta: { requiresAuth: true, title: '账号管理' },
            },
        ],
    },
    {
        path: '/work-phone-number',
        redirect: '/work-phone-number/apply-list',
        name: 'work-phone-number',
        component: DefaultLayout,
        meta: { requiresAuth: true, title: '办税小号' },
        children: [
            {
                path: 'apply-list',
                name: 'apply-list',
                component: ApplyNumberList,
                meta: { requiresAuth: true, title: '小号申请' },
            },
            {
                path: 'phone-list',
                name: 'phone-list',
                component: PhoneList,
                meta: { requiresAuth: true, title: '办税小号' },
            },
            {
                path: 'bind-company-list',
                name: 'bind-company-list',
                component: BindCompanyList,
                meta: { requiresAuth: true, title: '绑定企业' },
            },
            {
                path: 'work-number-sms',
                name: 'work-number-sms',
                component: WorkNumberSMS,
                meta: { requiresAuth: true, title: '短信记录' },
            },
        ],
    },
]

export default clientRoutes
