<template>
    <div>
        <div class="display-flex align-center"> 
            <div class="color-red font-24">*</div>
            <div class="l-margin-4 font-16">线索池名称</div>
        </div>
        <div class="display-flex t-margin-8 justify-center b-margin-8">
            <el-input v-model="name" autocomplete="off" style="width: 452px" placeholder="请输入线索池名称"></el-input>
        </div>
        <div class="display-flex align-center">
            <div class="color-red font-24">*</div>
            <div class="l-margin-4 font-16">线索池成员</div>
        </div>
        <div >
            <el-form-item class="display-flex t-margin-8 align-center">
                <el-select multiple v-model="users" autocomplete="off" style="width: 452px;" placeholder="请选择线索池成员">
                    <!-- <el-option label="路飞" value="shanghai"></el-option>
                    <el-option label="区域二" value="beijing"></el-option> -->
                    <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
        </div>
        <div class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="confirm" style="background-color: #1966FF;">确 定</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref,onMounted } from 'vue'
import systemService from '@/service/systemService'
import type { IUserListItem } from '@/types/org'

const userList = ref<IUserListItem[]>([])
const getUser = async () => {
    let res = await systemService.userList({tenantId:''})
    userList.value = res
    return userList.value
}

const name = ref('')
const users = ref([])

const emit = defineEmits(['cancel', 'confirm'])

const cancel = () => {
    name.value = ''
    users.value = []
    emit('cancel')
}

const confirm = () => {
    return new Promise((resolve) => {
        emit('confirm', { name: name.value, users: users.value }, resolve)
    }).then(() => {
        name.value = ''
        users.value = []
    })
}

onMounted(async () => {
    const res = await getUser()
    console.log('userList:', res)
})

</script>

<style scoped>
.align-center{
    align-items: center;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  margin-top: 20px;
}
</style>
