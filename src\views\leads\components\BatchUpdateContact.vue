<script lang='ts' setup>
import { ref } from 'vue'
import crmService from '@/service/crmService'
import { ElMessage, ElMessageBox } from 'element-plus'
import orderService from '@/service/orderService'
import type { ILeadData, IRiskListItem } from '@/types/lead'
import type { IOrderCheckEntBuyResponse } from '@/types/order'
const props = defineProps<{
    selectedData: ILeadData[] | IRiskListItem[]
    from?: string
}>()
const emit = defineEmits(['clearAllSelected'])
const hasnotBuyCount=ref(0)

const handleUpdateContact = async() => {
    if (props.selectedData.length < 1) {
        ElMessage({
            type: 'warning',
            message: `请选择需要更新的${props.from ? '数据' : '客户'}`,
        })
    } else {
        // 获取最新的购买状态
        const orderCheckEntBuyParams = (props.selectedData.map(item => item.socialCreditCode).join(','))
        let res = await orderService.orderCheckEntBuy({ socialCreditCode: orderCheckEntBuyParams })
        console.log('购买结果',res)
        if (props.selectedData.length === 1) { 
            const { status } = res as IOrderCheckEntBuyResponse
            ElMessageBox.confirm(
                `已勾选1家企业，${status === '1' ? 0 : 1}家未购买${props.from ? '' : '过联系方式'}，是否更新${props.from ? '数据' : '联系方式'}并扣除线索权益（每家企业在此操作中仅会扣除一次权益，已扣除权益的企业将不再重复扣除）`,
                '提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                })
                .then(async () => {
                    let arr = props.selectedData.map((item) => {
                        return {
                            socialCreditCode: item.socialCreditCode,
                            name: item.name,
                            crmId: item.id,
                        }
                    })
                    let updateContactRes = await crmService.crmUpdateContacts(arr)
                    if (updateContactRes.success === true) {
                        ElMessage({
                            type: 'success',
                            message: `更新${props.from ? '数据' : '联系'}方式成功`,
                        })
                        emit('clearAllSelected')
                    } else {
                        ElMessage({
                            type: 'error',
                            message: updateContactRes.errMsg,
                        })
                    }
                }).catch(() => {
                    console.log('点击取消')
                })
        } else {
            if (Array.isArray(res) && res.length > 0) {
                hasnotBuyCount.value = res.filter(item => item.status !== '1').length
                ElMessageBox.confirm(
                    `已勾选${props.selectedData.length}家企业，${hasnotBuyCount.value}家未购买${props.from ? '' : '过联系方式'}，是否更新${props.from ? '数据' : '联系方式'}并扣除线索权益（每家企业在此操作中仅会扣除一次权益，已扣除权益的企业将不再重复扣除）`,
                    '提示',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }
                ).then(async () => {
                    let arr = props.selectedData.map((item) => {
                        return {
                            socialCreditCode: item.socialCreditCode,
                            name: item.name,
                            crmId: item.id,
                        }
                    })
                    let updateContactRes = await crmService.crmUpdateContacts(arr)
                    if (updateContactRes.success === true) {
                        ElMessage({
                            type: 'success',
                            message:`更新${props.from ? '数据' : '联系'}方式成功`,
                        })
                        emit('clearAllSelected')
                    } else {
                        ElMessage({
                            type: 'error',
                            message: updateContactRes.errMsg,
                        })
                    }
                }).catch(() => {
                    console.log('点击取消')
                })
            }
        }
    }
}
</script>
<template>
    <a class="color-black" @click="handleUpdateContact()">更新{{ from ? '数据' : '联系方式' }}</a>
</template>
<style scoped lang='scss'>
</style>
