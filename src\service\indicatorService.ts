import http from '@/axios'
import type { getCompareIndicatorDataParams, getIndicatorResultParams, getIndicatorResultResponse, getCompareIndicatorDataResponse, IGetRiskEntListResponse, IGetRiskTypeData, getTopTenParams, IGetTopTenResponseItem, getProportionListParams, IGetProportionListResponse } from '@/types/indicator'

export default {
    getCompareIndicatorData(body: getCompareIndicatorDataParams) {
        return http.get(`/api/zhenqi-report/indicator/get-compare-indicator-data`, {
            params: body
        })
    },
    getIndicatorResult(data: getIndicatorResultParams): Promise<getIndicatorResultResponse> {
        return http.post(`/api/indicator/data/getIndicatorResult`, data)
    },

    getDecomposeCompareIndicatorData(body: getCompareIndicatorDataParams): Promise<getCompareIndicatorDataResponse> {
        return http.get(`/api/zhenqi-report/indicator/get-decompose-compare-indicator-data`, {
            params: body
        })
    },
    getCreditLevel(body: {
        socialCreditCode: string
    }) {
        return http.get(`/api/zhenqi-report/indicator/get-credit-level`, {
            params: body
        })
    },
    // 获取风险分类大类数据
    getRiskTypeData(body: { socialCreditCode: string}): Promise<IGetRiskTypeData>{
        return http.get(`/api/zhenqi-report/indicator/get-risk-type-data`, {
            params: body
        })
    },
    // 风险事件列表
    getRiskEntList(body: { socialCreditCode: string}): Promise<IGetRiskEntListResponse> {
        return http.get(`/api/zhenqi-report/indicator/get-risk-ent-list`, {
            params: body,
            hideError: true
        })
    },
    // 获取上下游前十大
    getTopTen(data: getTopTenParams): Promise<IGetTopTenResponseItem[]> {
        return http.post(`/api/zhenqi-report/indicator/get-top-ten`, data)
    },
    // 获取上下游关系企业列表
    getProportionList(data: getProportionListParams): Promise<IGetProportionListResponse> {
        return http.post(`/api/zhenqi-report/indicator/get-proportion-list`, data, {
            hideError: true
        })
    },
}