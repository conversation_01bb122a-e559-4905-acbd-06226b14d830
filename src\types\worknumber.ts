import type { IAllRecord } from '@/types/record'
import type { ICommonResponse, IPaginationResponseForWorkNumber } from './axios'

export interface IApplyWorkNumberParams extends IAllRecord {
    bsxhNum: number //数量
    thirdSign: string //第三方签名
    useType: number //使用类型 0=测试 1=正式使用，企享云默认为1
}

export interface IApplyNumberListParams extends IAllRecord {
    page: number
    size: number
    tenantId?: string
    createTime?: string[]
}

export interface IApplyNumberListTableData extends IPaginationResponseForWorkNumber {
    records: IApplyNumberListTableDataItem[]
}

export interface IApplyNumberListTableDataItem {
    transId: string //订单ID
    bsxhNum:number // 申请数量
    createTime:string //申请时间
    transStatus:number //申请状态
    remark:string //备注
}

export interface IWorkNumberListParams extends IAllRecord {
    page: number
    size: number
    transId?:string //订单ID
}

export interface IWorkNumberListTableData extends IPaginationResponseForWorkNumber {
    records: IWorkNumberListTableDataItem[]
}

export interface IWorkNumberListTableDataItem {
    id:string
    telB:string //办税员号码
    authStatus:string //绑定状态
    telX:string //工作号
    tenantName:string
    channelId:string
    tenantId:string
}

// 绑定工作号
export interface IBindWorkNumberParams extends IAllRecord {
    bindType?:number // 绑定类型 1：绑定 2：解绑 3：变更
    telX?:string //工作号
    telB?:string //办税员手机号
    xhId?:string // 小号ID
}

// 绑定办税员
export interface IBindTaxOfficerParams extends IAllRecord {
    bsyxm: string //办税人员姓名
    bsysjhm: string //办税人员手机号
    sflX: string //办税人员身份
    bsysfzhm: string //办税人员身份证号
    bsymm: string //办税人员密码
    telX: string //工作小号
}

// 绑定/解绑企业
export interface IBindCompanyParams extends IAllRecord {
  bsyName?: string
  telB?: string
  bsySf?: string
  bsySfz?: string
  bsymm?: string
  channelId?: string
  entName?: string
  id?: string
  socialCreditCode?: string
  tenantId?: string
}

// 查询办税员信息
export interface IGetTaxOfficerInfoParams extends IAllRecord {
    telX: string //工作号
    channelId?: string 
}

export interface IGetTaxOfficerInfoData extends ICommonResponse{
    data:{
        bsysjhm:string //办税员手机号
        bsymm:string //办税员密码
        bsyxm:string //办税员姓名
        sflx:string //办税员身份
        bsysfzhm:string //办税员身份证号
        telX:string //工作号
    }
}

export interface IBindCompanyTableParams extends IAllRecord {
    page: number
    size: number
}

export interface IBindCompanyTableData extends IPaginationResponseForWorkNumber {
    records: IBindCompanyTableDataItem[]
}

export interface IBindCompanyTableDataItem {
    id:string
    bsyName:string
    entName:string
    socialCreditCode:string
    bsySf:string
    bsySfz:string
    tenantId:string
    telX:string
    telB:string
    creatTime:string
}

export interface IWorkNumberSMSParams extends IAllRecord {
    page: number
    size: number
}

export interface IWorkNumberSMSTableData extends IPaginationResponseForWorkNumber {
    records: IWorkNumberSMSTableDataItem[]
}

export interface IWorkNumberSMSTableDataItem {
    id:number
    authId:string
    telX:string
    smsContent:string
    smsDate:string
}

export interface IWorkCompanyAuthParams extends IAllRecord {
    nsrsbh: string,
    qymc: string, 
    bsysjhm: string,
    sflx: string,
    tenantId: string
}

export interface IWorkCompanyAuthData extends ICommonResponse{
    authUrl:string
}