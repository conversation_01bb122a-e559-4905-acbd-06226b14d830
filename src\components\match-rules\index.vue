<template>

    <div class="flex-grow-1">
        <div class="high-search-rules-box t-margin-16 l-padding-30 relative" v-if="matchRulesData.length">
            <!-- <el-scrollbar> -->
            <div class="high-search-rules-item">
                <div class="display-flex gap-10 top-bottom-center tb-padding-8 l-padding-10">
                    <div class="text-nowrap">满足</div>
                    <div class="operator-box">
                        <el-select v-model="searchConditions.operator" :disabled="readOnly">
                            <el-option label="全部条件" value="all" />
                            <el-option label="任一条件" value="any" />
                        </el-select>
                    </div>
                </div>

                <div class="t-margin-10 l-padding-60" v-for="(condition, index) in searchConditions.children"
                     :key="condition.prop">
                    <!-- 条件组 -->
                    <template v-if="condition.children && condition.children.length > 0">
                        <ConditionGroup :condition="condition" @updateConditionGroup="(data) => {
                            updateConditionGroup(data, condition)
                        }" :index="index" @removeConditionGroup="removeChildConditionGroup(index)" />
                    </template>
                    <!-- 单个条件 -->
                    <template v-else>
                        <div class="only-node relative">
                            <ConditionItem :conditionItem="condition" @updateCondition="
                                (data) => {
                                    updateCondition(data, 'only', index)
                                }
                            " @removeCondition="removeCondition(index)" />
                        </div>

                    </template>
                </div>
            </div>
            <div class="l-padding-60 " v-if="!readOnly">
                <div class="add-rule-box relative t-padding-10">
                    <el-button @click="
                        addCondition('only')
                    ">添加条件</el-button>
                    <el-button type="primary" @click="
                        addCondition('group')
                    ">添加条件组</el-button>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang='ts' setup>
import { ref, onMounted, defineProps, defineExpose, provide } from 'vue'
import type { Ref } from 'vue'
import { useStore } from 'vuex'

import aicService from '@/service/aicService'

import type { ISearchConditions, IHighSearchRules, ISearchConditions2 } from '@/types/model'

import { ElMessage } from 'element-plus'

import ConditionItem from './ConditionItem.vue'
import ConditionGroup from './ConditionGroup.vue'
const store = useStore()
const cacheMatchRulesData = store.state.app.matchRulesData

const props = defineProps({
    rules: {
        type: Array,
        default: () => {
            return []
        }
    },
    readOnly: {
        type: Boolean,
        default: false
    },
    scoreInfo: {
        type: Object,
        default: () => {
            return {}
        }
    }
})


provide('readOnly', props.readOnly)
provide('scoreInfo', props.scoreInfo)


const matchRulesData: Ref<IHighSearchRules[]> = ref([] as IHighSearchRules[])
const getMatchRulesData = () => {

    console.log('cacheMatchRulesData', cacheMatchRulesData)

    if (cacheMatchRulesData.length) {
        matchRulesData.value = cacheMatchRulesData
    } else {
        Promise.all([aicService.conditionGetInfo({ searchType: 'policyRules' }), aicService.conditionGetData({})]).then(
            ([rulesRes, staticConfigRes]) => {
                matchRulesData.value = rulesRes || []
                store.commit('app/SET_MATCH_RULES_DATA', rulesRes)
                store.commit('app/SET_STATIC_CONFIG', staticConfigRes)
            }
        )
    }
}

const originalCondion = {
    operator: 'all',
    prop: '',
    type: 'branch',
    value: [],
    children: [{
        prop: '',
        type: 'leaf',
        operator: 'equal',
        value: []
    }]
}



const searchConditions: Ref<ISearchConditions> = ref(JSON.parse(JSON.stringify(originalCondion)))


const addCondition = (type: string) => {
    if (searchConditions.value.children) {
        let baseObj = {
            operator: 'equal',
            prop: '',
            value: [],
        }
        let obj: ISearchConditions = {
            ...baseObj,
        }
        if (type === 'group') {
            //条件组
            obj['operator'] = 'all'
            obj['type'] = 'branch'
            obj['children'] = [
                {
                    type: 'leaf',
                    ...baseObj,
                },
            ]
        } else {
            //添加单一条件
            obj['type'] = 'leaf'
        }

        console.log('--adsa', obj)
        searchConditions.value.children.push(obj)
    }
}


const updateConditionGroup = (data: ISearchConditions, condition: ISearchConditions) => {
    console.log(condition)

    condition = data
}

const removeChildConditionGroup = (idx: number) => {
    if (searchConditions.value.children) {
        searchConditions.value.children.splice(idx, 1)
    }
}
const removeCondition = (index: number, conditions: ISearchConditions[] | null = null) => {
    if (conditions) {
        if (conditions.length === 1) {
            ElMessage({
                message: '条件组不可为空',
                type: 'warning',
            })
            return
        }
        conditions.splice(index, 1)
    } else {
        searchConditions.value.children?.splice(index, 1)
    }
}


const updateCondition = (data: ISearchConditions, type: string, index: number, idx: number = 0) => {
    console.log(data)
    let conditions = searchConditions.value.children
    if (!conditions || !conditions.length) {
        return
    }
    if (type === 'group' && Array.isArray(conditions[index].children)) {
        conditions[index].children[idx] = data
    }
    if (type === 'only' && conditions.length) {
        conditions[index] = data as ISearchConditions2
    }
}

const exportRules = () => {
    let findEmptyValueNodes = (arr: ISearchConditions[]) => {
        let result: string[] = []

        // 遍历数组
        arr.forEach(item => {
            // 如果有children，递归查找
            if (item.children && item.children.length > 0) {
                result = result.concat(findEmptyValueNodes(item.children))
            }
            if ((!item.value && item.value !== 0) && item.type !== 'branch') {

                result.push(item.propLabel || '')
            }
        })

        return result
    }
    let res = findEmptyValueNodes([searchConditions.value])

    if (res.length) {
        ElMessage.error(`${res.join('、')}的值为空`)
        return false
    }

    return searchConditions.value
}

defineExpose({ exportRules })


onMounted(() => {
    getMatchRulesData()
    if (props.rules.length) {
        searchConditions.value = props.rules[0] as ISearchConditions
    }
})

</script>

<style lang='scss' scoped>
.operator-box {
    width: 140px;
    --el-fill-color-blank: rgba(246, 248, 250, 1);
}

.group-node:before {
    content: "";
    display: block;
    width: 0;
    height: calc(100% - 30px);
    left: -4px;
    top: 12px;
    position: absolute;
    border-left: 1px dashed var(--three-grey);
}

.group-node::after {
    content: "";
    display: block;
    width: 50px;
    height: 1px;
    left: -60px;
    top: 12px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.group-node .condition-item::before {
    content: "";
    display: block;
    width: 0;
    width: 30px;
    height: 1px;
    left: -40px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.only-node:before {
    content: "";
    display: block;
    width: 50px;
    height: 1px;
    left: -60px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.high-search-rules-box {
    height: calc(100% - 120px);
}

.high-search-rules-box:before {
    content: "";
    display: block;
    width: 1px;
    height: calc(100% - 42px);
    left: 27px;
    top: 18px;
    position: absolute;
    border-left: 1px dashed var(--three-grey);
}

.add-rule-box:before {
    content: "";
    display: block;
    width: 50px;
    height: 1px;
    left: -60px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}
</style>