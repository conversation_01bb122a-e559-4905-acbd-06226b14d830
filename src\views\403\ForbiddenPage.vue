<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import PNG403 from '@/assets/images/auth/403.png'

const router = useRouter()
const route = useRoute()

const errMsg = route.query.msg
const hideBtn = route.query.hideBtn
const toRouter = () => {
    router.push('/')
}
</script>

<template>
    <div class="flex height-100 top-bottom-center center flex flex-column">
        <div>
            <img :src="PNG403" alt="403" class="width-100" />
        </div>
        <div class="font-16 color-text-grey">{{ errMsg || '您无法访问该页面' }}</div>
        <div class="t-margin-40" v-if="!hideBtn">
            <el-button type="primary" @click="toRouter">返回首页</el-button>
        </div>
    </div>
</template>
