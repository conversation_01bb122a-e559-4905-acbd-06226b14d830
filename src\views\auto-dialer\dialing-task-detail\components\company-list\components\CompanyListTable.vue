<script lang="ts" setup>
import outboundService from '@/service/outboundService'
import type {
    IAutoDialerListItem,
    IAutoDialerTaskCompanyFilter,
    IAutoDialerTaskDetailListItem,
    IAutoDialerTaskExportRequest,
} from '@/types/autoDialer'
import moment from 'moment'
import { onMounted, ref, watch } from 'vue'
import CallRecord from '../../call-record/CallRecord.vue'
import { ElMessage } from 'element-plus'
import { downloadFile } from '@/utils/download'
import CreateTaskDrawer from '@/components/auto-dialer/create-task/CreateTaskDrawer.vue'
import permissionService from '@/service/permissionService'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    filterParams?: IAutoDialerTaskCompanyFilter
    taskCode: string
    setCurrentTask?: (task: IAutoDialerListItem) => void
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const list = ref<IAutoDialerTaskDetailListItem[]>([])
const loading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 20,
    total: 0,
})
const currentTask = ref<IAutoDialerTaskDetailListItem>()
const requestParams = ref<IAutoDialerTaskCompanyFilter>({
    callStatus: '',
    companyName: '',
    startCallTime: '',
    endCallTime: '',
    aiTagName: '',
    recallStatus: '',
})
const callRecordVisible = ref(false)
const multipleSelection = ref<IAutoDialerTaskDetailListItem[]>([])
const exporting = ref(false)
const createTaskVisible = ref(false)

// ====================== Methods ======================
const getData = (params: IAutoDialerTaskCompanyFilter) => {
    loading.value = true
    outboundService
        .taskCompanyDetail({
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            taskCode: props.taskCode,
            ...params,
        })
        .then((res) => {
            loading.value = false
            const { errCode, data, total } = res
            if (errCode === 0) {
                list.value = data
                pageInfo.value.total = total
            } else {
                list.value = []
            }
        })
        .catch(() => {
            loading.value = false
            list.value = []
            pageInfo.value.total = 0
        })
}

const toRecordsDetail = (row: IAutoDialerTaskDetailListItem) => {
    currentTask.value = row
    callRecordVisible.value = true
}

const exportRecord = (num?: number) => {
    let params: IAutoDialerTaskExportRequest = {}

    if (!num) {
        if (multipleSelection.value.length === 0) {
            return ElMessage.warning('请选择要导出的记录')
        }
        params.ids = multipleSelection.value.map((item) => item.id)
    } else if (num > 0) {
        params = { ...props.filterParams, nums: num, taskCode: props.taskCode }
    }

    if (exporting.value) return

    outboundService
        .taskCompanyExport(params)
        .then((res) => {
            if (res.data.type === 'application/vnd.ms-excel') {
                exporting.value = true
                downloadFile(res)
                exporting.value = false
            } else {
                ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
            }
        })
        .catch(() => {
            exporting.value = false
            ElMessage.warning('导出失败，请稍后再试')
        })
}

const handleSelectionChange = (val: IAutoDialerTaskDetailListItem[]) => {
    multipleSelection.value = val
}

const creatTask = () => {
    if (multipleSelection.value.length === 0) {
        return ElMessage.warning('请选择要发起外呼的企业')
    }

    if (multipleSelection.value.length > 1000) {
        return ElMessage.warning('企业数量不能超过1000条')
    }
    createTaskVisible.value = true
}

// ====================== Watchers ======================
watch(
    () => props.filterParams,
    (value) => {
        if (value) {
            getData(value)
        }
    },
    {
        deep: true,
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getData(requestParams.value)
})
</script>

<template>
    <div class="flex flex-column height-100 company-list-table">
        <div class="flex flex-1 flex-column" style="overflow: hidden">
            <div class="flex flex-row gap-16 space-between tb-padding-16">
                <div class="flex flex-row gap-16 top-bottom-center">
                    <div class="flex flex-row gap-8 top-bottom-center">
                        <div class="font-14">
                            已选<span class="color-blue lr-padding-2 font-weight-600">{{
                                multipleSelection.length
                            }}</span
                            >个
                        </div>
                    </div>
                    <div class="flex flex-row">
                        <el-dropdown placement="bottom-start">
                            <el-button :loading="exporting" :disabled="exporting">
                                导出
                                <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="exportRecord()">导出所选</el-dropdown-item>
                                    <el-dropdown-item @click="exportRecord(100)">导出前100条</el-dropdown-item>
                                    <el-dropdown-item @click="exportRecord(500)">导出前500条</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
                <div class="flex flex-row gap-16 top-bottom-center">
                    <div class="flex flex-row gap-16" v-if="permissionService.isDialingTaskCreatePermitted()">
                        <el-button type="primary" @click="creatTask"> 智能外呼 </el-button>
                    </div>
                </div>
            </div>

            <el-table
                :data="list"
                style="width: 100%"
                v-loading="loading"
                height="100%"
                @selection-change="handleSelectionChange"
                show-overflow-tooltip
                row-key="id"
            >
                <el-table-column type="selection" width="55" fixed="left" />
                <el-table-column prop="callStartTime" label="接通时间" width="180px">
                    <template #default="scope">
                        {{
                            scope.row.callStartTime
                                ? moment(scope.row.callStartTime).format('YYYY-MM-DD HH:mm:ss')
                                : '-'
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="联系人" width="120px" />
                <el-table-column prop="companyName" label="企业名称" width="250px" />
                <el-table-column prop="tags" label="身份">
                    <template #default="scope">
                        {{ scope.row.tags }}
                    </template>
                </el-table-column>
                <el-table-column prop="phoneNumber" label="接通号码" width="120px" />
                <el-table-column prop="callStatusName" label="接通状态" />

                <el-table-column prop="recallStatusName" label="重呼状态" />
                <el-table-column prop="aiTagName" label="对话标签">
                    <template #default="scope">
                        {{ scope.row.aiTagName || '-' }}
                    </template>
                </el-table-column>

                <el-table-column label="操作" fixed="right" width="100" align="left">
                    <template #default="scope">
                        <a class="pointer color-blue" @click="toRecordsDetail(scope.row)"> 通话详情 </a>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- <div class="h-120 back-color-red"></div> -->
        <el-affix position="bottom" :offset="0">
            <div class="display-flex top-bottom-center all-padding-18 justify-flex-end back-color-white">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="getData"
                    @current-change="getData"
                />
            </div>
        </el-affix>
        <CallRecord v-model:visible="callRecordVisible" :task-info="currentTask" />
        <CreateTaskDrawer v-model:visible="createTaskVisible" :crm-id="multipleSelection.map((e) => e.crmId)" />
    </div>
</template>

<style lang="scss" scoped>
.company-list-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
