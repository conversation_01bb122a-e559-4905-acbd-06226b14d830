<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, watch, inject } from 'vue'
import type { Ref } from 'vue'

import type { ISearchConditions } from '@/types/model'
import ConditionItem from './ConditionItem.vue'

import ConditionGroupCom from './ConditionGroup.vue'
const props = defineProps({
    condition: {
        type: Object,
        default: () => {
            return {}
        },
    },
    index: {
        type: Number,
        default: 0
    }
})


const readOnly = inject('readOnly') as Ref<boolean>


const conditionGroup: Ref<ISearchConditions> = ref(props.condition as ISearchConditions)

const emits = defineEmits(['removeConditionGroup', 'updateConditionGroup'])

watch(() => props.condition, (newVal) => {
    conditionGroup.value = newVal as ISearchConditions
})

const removeCondition = (idx: number) => {
    conditionGroup.value.children = conditionGroup.value.children.filter((item, index) => index !== idx)
}


const updateCondition = (data: ISearchConditions, idx: number = 0) => {
    console.log(data)
    if (conditionGroup.value.children?.[idx]) {
        conditionGroup.value.children[idx] = data
    } else {
        conditionGroup.value.children = []
        conditionGroup.value.children[0] = data
    }

    emits('updateConditionGroup', { data })
}

const addGroupConditionGroup = () => {
    let baseObj = {
        operator: 'all',
        prop: '',
        value: [],
    }
    let obj: ISearchConditions = {
        ...baseObj,
        children: [
            {
                type: 'leaf',
                operator: 'numberEqual',
                prop: '',
                value: []
            },
        ],
        operator: 'all',
        type: 'branch'
    }


    conditionGroup.value.children.push(obj)

}
const addGroupConditionChild = () => {
    let baseObj = {
        operator: 'numberEqual',
        prop: '',
        value: [],
        type: 'leaf',
    }
    conditionGroup.value.children.push(baseObj)
}

const removeConditionGroup = () => {
    emits('removeConditionGroup')
}

const removeChildConditionGroup = (idx: number) => {
    conditionGroup.value.children.splice(idx, 1)
}

onMounted(() => {

})
</script>

<template>
    <div class="l-padding-10 group-node relative t-margin-20">
        <div class="display-flex gap-10 top-bottom-center">
            <div class="text-nowrap">满足</div>
            <div class="operator-box">
                <el-select v-model="conditionGroup.operator" :disabled="readOnly">
                    <el-option label="全部条件" value="all" />
                    <el-option label="任一条件" value="any" />
                </el-select>
            </div>
            <div v-if="!readOnly">
                <el-icon class="pointer" @click="removeConditionGroup()">
                    <Delete />
                </el-icon>
            </div>
        </div>
        <!-- 二级条件 -->
        <div v-for="(childCondition, idx) in condition.children" :key="childCondition.prop"
             class="high-search-rules-item-title l-padding-30">
            <ConditionItem v-if="childCondition.type === 'leaf'" :conditionItem="childCondition" @updateCondition="
                (data) => {
                    updateCondition(data, idx)
                }
            " @removeCondition="removeCondition(idx)" />

            <ConditionGroupCom v-if="childCondition.type === 'branch'" :condition="childCondition" :index="idx"
                               @updateCondition="
                                   (data) => {
                                       updateCondition(data, idx)
                                   }
                               " @removeConditionGroup="removeChildConditionGroup(idx)" />

        </div>
        <div class="l-padding-30" v-if="!readOnly">
            <div class="condition-item-add-group  relative  t-padding-10">
                <el-button @click="
                    addGroupConditionChild()
                ">添加条件</el-button>
                <el-button type="primary" @click="
                    addGroupConditionGroup()
                ">添加条件组</el-button>
            </div>
        </div>
    </div>
</template>



<style lang='scss' scoped>
.operator-box {
    width: 140px;
    --el-fill-color-blank: rgba(246, 248, 250, 1);
}

.group-node:before {
    content: "";
    display: block;
    width: 0;
    height: calc(100% - 30px);
    left: -4px;
    top: 12px;
    position: absolute;
    border-left: 1px dashed var(--three-grey);
}

.group-node::after {
    content: "";
    display: block;
    width: 30px;
    height: 1px;
    left: -40px;
    top: 12px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.group-node .condition-item::before {
    content: "";
    display: block;
    width: 0;
    width: 30px;
    height: 1px;
    left: -40px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.only-node:before {
    content: "";
    display: block;
    width: 50px;
    height: 1px;
    left: -60px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.group-node .condition-item-add-group::before {
    content: "";
    display: block;
    width: 0;
    width: 30px;
    height: 1px;
    left: -40px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}
</style>