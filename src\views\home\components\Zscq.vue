<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import { ref, reactive, computed, watch } from 'vue'
import type { ModelRes, Model001ResInfo } from '@/types/home'

import * as echarts from 'echarts'
const chartRef = ref(null)
const props = defineProps<{
    data: ModelRes[]
}>()

// 转换数据：将 props.data 的嵌套结构扁平化，并添加 title
const transformedData = computed<Model001ResInfo[]>(() => {
    if (!props.data.length) return []

    return props.data.flatMap((item) => {
        return Object.entries(item).map(([title, value]) => ({
            ...value,
            title, // 添加 title 字段（键名）
        }))
    })
})

const option = reactive({
    legend: {
        bottom: '10%',
        left: 'center',
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
                backgroundColor: '#6a7985',
            },
        },
    },
    grid: {
        // 控制图表主体区域的位置和大小
        left: '3%',
        right: '4%',
        top: '10%', // 增加顶部留白
        bottom: '20%', // 增加底部留白
        containLabel: true,
    },
    xAxis: [
        {
            type: 'category',
            boundaryGap: true,
            data: ['2023', '2024', '2025'],
        },
    ],
    yAxis: [
        {
            type: 'value',
        },
    ],
    series: [
        {
            name: '一类知识产权',
            type: 'line',
            emphasis: {
                focus: 'series',
            },
            data: [10, 23, 16],
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        { offset: 0, color: '#1966FF' }, // 起始颜色
                        { offset: 1, color: 'white' }, // 结束颜色
                    ],
                },
            },
            itemStyle: {
                color: '#1966FF', // 线条颜色为粉色
            },
        },
        {
            name: '二类知识产权',
            type: 'line',
            emphasis: {
                focus: 'series',
            },
            data: [10, 13, 11],
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        { offset: 0, color: '#510EF5' }, // 起始颜色
                        { offset: 1, color: 'white' }, // 结束颜色
                    ],
                },
            },
            itemStyle: {
                color: '#510EF5', // 线条颜色为红色
            },
        },
    ],
})

const initChart = () => {
    const chartDom = chartRef.value
    const myChart = echarts.init(chartDom)
    option.series = newSeries.value
    myChart.setOption(option)
}

const newSeries = ref([])

watch(
    () => transformedData.value,
    (newVal) => {
        if (!newVal || !newVal.length) {
            newSeries.value = []
            return
        }
        // 定义颜色配置（可根据需要扩展）
        const colorConfig = [
            { lineColor: '#1966FF', areaStartColor: '#1966FFB3' },
            { lineColor: '#510EF5', areaStartColor: '#510EF5B3' },
        ]
        // 转换为 ECharts 需要的格式
        newSeries.value = newVal.map((item, index) => {
            const data = Object.values(item.map).map((value) => parseFloat(value))
            return {
                name: item.title, // 使用 title 作为系列名称
                type: 'line',
                emphasis: {
                    focus: 'series',
                },
                data: data,
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: colorConfig[index]?.areaStartColor || '#1966FF' },
                            { offset: 1, color: 'white' },
                        ],
                    },
                },
                itemStyle: {
                    color: colorConfig[index]?.lineColor || '#1966FF', // 使用配置的颜色或默认值
                },
            }
        })
        initChart()
    },
    { deep: true, immediate: true }
)
</script>
<template>
    <div class="zzcq">
        <div style="margin-bottom: 24px">
            <ModuleTitle title="知识产权创新"></ModuleTitle>
        </div>

        <div class="zzcq-echart" ref="chartRef"></div>
    </div>
</template>
<style scoped lang="scss">
.zzcq {
    width: 100%;
    padding: 16px;
    background-color: #fff;
    .zzcq-echart {
        width: 100%;
        height: 230px;
    }
}
</style>
