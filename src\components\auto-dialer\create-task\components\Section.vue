<script lang="ts" setup>
import { QuestionFilled } from '@element-plus/icons-vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
defineProps<{
    title: string
    tooltip?: boolean
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================

// ====================== Methods ======================

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-column gap-16">
        <div class="flex flex-row gap-8 top-bottom-center">
            <div class="h-16 w-6 back-color-blue border-radius-4"></div>
            <div class="color-black font-16">{{ title }}</div>
            <el-tooltip class="box-item" effect="light" placement="right-start" v-if="tooltip">
                <el-icon><QuestionFilled class="color-three-grey" :size="16" /></el-icon>
                <template #content>
                    <slot name="tooltip"></slot>
                </template>
            </el-tooltip>
        </div>
        <div class="flex flex-row lr-padding-14 top-bottom-center">
            <slot></slot>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
