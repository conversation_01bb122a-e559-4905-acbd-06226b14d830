<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7; ">
        <div ref="searchContentRef" style=" background-color: #fff; padding: 16px">
            <!-- 搜索公共组件 -->
            <searchBox
                :searchOptionKey="searchOptionsKey"
                @updateSearchParams="updateSearchParams"
                :defaultValue="defaultQueryParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="flex flex-row b-margin-16 space-between">
                <el-dropdown placement="bottom-start">
                    <el-button :loading="exporting" :disabled="exporting">
                        导出
                        <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="exportRecord()">导出全部</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-select
                    v-if="isAdmin"
                    v-model="orderValue"
                    placeholder="请选择排序方式"
                    style="width: 150px"
                    @change="orderChange(orderValue)"
                    clearable
                >
                    <el-option
                        v-for="item in orderOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </div>
            <el-table
                ref="tableList"
                :data="tableData"
                :style="{ 'min-height': tableHeight + 'px' }"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="权益名称" min-width="100" >
                    <template #default="scope">
                        {{ scope.row.serviceName }}
                    </template>
                </el-table-column>
                <el-table-column label="总额度" prop="totalAmount" min-width="50"/>
                <el-table-column label="剩余额度" prop="remainingAmount" min-width="50"/>
                <el-table-column label="状态" prop="status" min-width="50">
                    <template #default="scope">
                        <span v-if="scope.row.status">未过期</span>
                        <span v-else>已过期</span>
                    </template>
                </el-table-column>
                <el-table-column label="购买时间" prop="createTime" min-width=“150” >
                    <template #default="scope">
                        {{ scope.row.createTime? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="到期时间" prop="endTime" min-width=“150” >
                    <template #default="scope">
                        {{ scope.row.endTime? moment(scope.row.endTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </template>
                </el-table-column>
                <el-table-column v-if="isAdmin" label="所属租户">
                    <template #default="scope">
                        {{ scope.row.tenantInfo.name }}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="100">
                    <template #default="scope">
                        <div class="display-flex gap-16">
                            <div
                                :class="!scope.row.status || scope.row.remainingAmount < 1 ? 'not-allow' : 'pointer'"
                                :style="!scope.row.status || scope.row.remainingAmount < 1 ? { color: '#ccc' } : { color: '#1966ff' }"
                                type="primary"
                                @click="!scope.row.status || scope.row.remainingAmount < 1 ? '' : allocate(scope.row)"
                            >
                                权益分配
                            </div>
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="openBenefitDrawer(scope.row)"
                            >
                                消费明细
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>    
        </div>
    </div>
    <el-dialog
        title="权益分配"
        v-model="dialogVisible"
        width="500px"
    >
        <div class="display-flex flex-column gap-16">
            <span class="color-two-grey font-16">已选择权益</span>
            <span class="color-black font-16">{{ currentServiceName }} (有效时间 {{ expireTime }}) </span>
            <span class="color-two-grey font-16">选择对象</span>
            <el-select 
                v-model="selectedTenantId" 
                placeholder="选择分配对象"
                clearable
                size="large"
            >
                <el-option
                    v-for="item in selectedTenant"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"
                >
                </el-option>
            </el-select>
            <span>设置数量</span>
            <el-input-number v-model="benefitQuantity" :min="1" :max="maxQuantity" :step="1" style="width: 122.4px">   
            </el-input-number>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose" style="height: 40px">取 消</el-button>
                <el-button type="primary" @click="confirmAllocate" :loading="confirmLoading" style="height: 40px">
                    确 认
                </el-button>
            </div>
        </template>
    </el-dialog>
    <div>
        <BenefitDrawer v-model:visible="benefitDrawerVisible" :serviceKey="serviceKey" :tenantId="tenantId" :transId="transId" />
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, reactive,getCurrentInstance, nextTick, onBeforeMount } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import searchBox from '@/components/common/SearchBox.vue'
import type { RootState } from '@/types/store'
import orderService from '@/service/orderService'
import systemService from '@/service/systemService'
import type { IOrderTransListParams, IServiceOrderResponseItem } from '@/types/order'
import { ElMessage } from 'element-plus'
import type { ITenantListResponse } from '@/types/tenant'
import BenefitDrawer from '../components/BenefitDrawer.vue'
import { downloadFile } from '@/utils/download'

const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)

// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const isAdmin = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { tenantId } = user || {}
    if (tenantId === '') {
        return true
    } else {
        return false
    }
})

type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}
const searchConfig = ref<CustomConfig>()
const searchOptionsKey = ref('BENEFIT_LIST_SEARCH_OPTIONS')
const route = useRoute()
const tableLoading = ref<boolean>(false)
const exporting = ref(false)
const store = useStore<RootState>()

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

const orderValue = ref('')
const orderOptions = ref([
    {
        value:'remainAmountAsc',
        label:'剩余额度-升序'
    },
    {
        value:'remainAmountDesc',
        label:'剩余额度-降序'
    },
    {
        value:'endEndTimeAsc',
        label:'到期时间-升序'
    },
    {
        value:'endEndTimeDesc',
        label:'到期时间-降序'
    },
])

const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

const orderChange = (orderValue:string) => {
    // console.log(orderValue)
    if(!orderValue){
        delete queryParams.value.sortBy
    }else{
        queryParams.value.sortBy = orderValue
    }
    getBenefitList(queryParams.value)
}

const exportRecord = () => {
    let params = {} as IOrderTransListParams
    orderService.orderTransExport(params)
        .then((res) => {
            if(res.data.type === 'application/vnd.ms-excel'){
                exporting.value = true
                downloadFile(res)
                exporting.value = false
            }
            else{
                ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
            }
        })
        .catch(() => {
            exporting.value = false
            ElMessage.warning('导出失败，请稍后再试')
        })
}

const queryParams = ref<IOrderTransListParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
})

const tableData = ref<IServiceOrderResponseItem[]>([])

const getBenefitList = (Params: IOrderTransListParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    if(orderValue.value){
        Params.sortBy = orderValue.value
    }else{
        delete Params.sortBy   
    }
    orderService.orderServiceOrderPage(Params).then((res) => {
        console.log(res)
        if(res.success){
            pageInfo.total = res.total
            tableData.value = res.data
        }else{
            ElMessage.error('系统错误')
        }
    }).then(() => {
        tableLoading.value = false
    })
}

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getBenefitList(queryParams.value)
}

const updateSearchParams = (params: IOrderTransListParams) =>{
    console.log(params)
    queryParams.value = params
    if(params.status === '1'){
        queryParams.value.status = 'NORMAL'
    }else if(params.status === '2'){
        queryParams.value.status = 'EXPIRED'
    }
    getBenefitList(queryParams.value)
}

const dialogVisible = ref(false)
const currentServiceName = ref('')
const expireTime = ref('')
const selectedTenantId = ref('')
const selectedTenant = ref<ITenantListResponse[]>([])
const benefitQuantity = ref(1)
const maxQuantity = ref()
const confirmLoading = ref(false)
const transId = ref('')
const allocate = (service: IServiceOrderResponseItem) => {
    console.log(service)
    if(service.remainingAmount <= 0){
        ElMessage.error('无可分配权益')
        return
    }
    dialogVisible.value = true
    currentServiceName.value = service.serviceName
    expireTime.value = moment(service.endTime).format('YYYY-MM-DD HH:mm:ss')
    maxQuantity.value = service.remainingAmount
    transId.value = service.transId
    serviceKey.value = service.serviceKey
}

const handleClose = () => {
    dialogVisible.value = false
}

const confirmAllocate = () => {
    if(!selectedTenantId.value){
        ElMessage.error('请选择分配对象')
        return
    }
    confirmLoading.value = true
    const params = {
        quantity: benefitQuantity.value,
        serviceKey:serviceKey.value,
        subordinateTenantId:selectedTenantId.value,
        transId:transId.value
    }
    console.log('params',params)
    orderService.orderAllocateEquitiesCustomer(params).then(() => {
        ElMessage.success('权益分配成功')
    }).finally(() => {
        confirmLoading.value = false
        dialogVisible.value = false
        getBenefitList(queryParams.value)
    })
}

const tenantId = ref('')
const serviceKey = ref('')
const benefitDrawerVisible = ref(false)
const openBenefitDrawer = (service: IServiceOrderResponseItem) => {
    console.log('service',service)
    tenantId.value = service.tenantId    
    serviceKey.value = service.serviceKey
    benefitDrawerVisible.value = true
    transId.value = service.transId
}

const defaultQueryParams = ref<Record<string, boolean | string | number[] | string[]>>({})

onBeforeMount(() => {
    // 可分配的租户
    systemService.tenantList().then(response => {

        console.log(response)
        // console.log(response)
        selectedTenant.value = response
    })

    if(isPlatManager.value){
        searchOptionsKey.value = 'BENEFIT_LIST_SEARCH_OPTIONS_FORADMIN'
        systemService.tenantList().then(response => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantId:response.map(item => ({
                    label:item.name,
                    value:item.id
                }))
            }
        })
    }
    if (route.query && JSON.stringify(route.query) !== '{}') {
        console.log('route.query', route.query)
        nextTick(() => {
            for(const key in route.query){
                defaultQueryParams.value = {
                    [key]: route.query.serviceKey as boolean | string | number[] | string[],
                }
            }
        })  
    } else {
        getBenefitList(queryParams.value)
    }
})

onMounted(() => {
    getTableHeight()
})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>