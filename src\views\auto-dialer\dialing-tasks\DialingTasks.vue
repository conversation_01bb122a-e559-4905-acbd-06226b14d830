<script lang="ts" setup>
import type { IAutoDialerRequest } from '@/types/autoDialer'
import { Table } from './components'
import { computed, ref } from 'vue'
import OpenPage from '@/components/auto-dialer/open-page/OpenPage.vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import SearchBox from '@/components/common/SearchBox.vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()
const isOpen = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    const { openAiPhone } = tenant || {}
    return openAiPhone
})

// ====================== Refs & Reactive State ======================
const filterParams = ref<IAutoDialerRequest>()

// ====================== Methods ======================
const filterQuery = (params: IAutoDialerRequest) => {
    const { date } = params || {}
    if (date && Array.isArray(date) && date.length === 2) {
        params.startTime = date[0] as string
        params.endTime = date[1] as string
        delete params['date']
    }
    filterParams.value = params
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-column gap-16">
        <div class="back-color-white border-radius-4 all-padding-16" v-if="isOpen">
            <!-- <Filter :get-data="filterQuery" /> -->
            <SearchBox :searchOptionKey="'AI_PHONE_TASK_SEARCH_OPTIONS'" @updateSearchParams="filterQuery" />
        </div>
        <div class="back-color-white height-100 oh all-padding-16" v-if="isOpen">
            <Table :filter-params="filterParams" />
        </div>
        <OpenPage v-if="!isOpen" />
    </div>
</template>

<style lang="scss" scoped></style>
