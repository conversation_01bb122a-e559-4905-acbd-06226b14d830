<template>
    <div class="display-flex gap-16 t-margin-10 relative condition-item tb-padding-8 top-bottom-center">
        <el-popover :visible="conditionPopover" ref="popoverRef" placement="right" trigger="click" width="800">
            <template #reference>
                <div @click="showPopover"
                     class="high-search-rules-item-key border-radius-4 pointer display-flex top-bottom-center space-between">
                    <span> {{ conditionData.propLabel || '请选择' }}</span>
                    <span class="display-flex top-bottom-center" v-show="!readOnly">
                        <el-icon>
                            <ArrowDown />
                        </el-icon>
                    </span>
                </div>
            </template>

            <div class="chose-condition-box" v-click-outside="onClickOutside">
                <div class="chose-condition-input b-padding-10">
                    <el-input v-model.trim="searchKey" placeholder="请输入筛选条件关键词" clearable>
                        <template #prefix>
                            <el-icon>
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </div>
                <div class="t-margin-10 display-flex ">
                    <el-scrollbar height="400px">
                        <div class="chose-condition-left r-padding-24 border-box">
                            <div class="chose-condition-category-item tb-padding-12 lr-padding-16 pointer t-margin-16 border-box"
                                 :class="activeCategoryName === category.name ? 'active-chose-condition-category-item' : ''
                                 " v-for="category in cacheMatchRulesData" :key="category.name"
                                 @click="activeCategoryName = category.name">
                                {{ category.name }}
                            </div>
                        </div>
                    </el-scrollbar>
                    <div class="chose-condition-right lr-padding-24 flex-1 tb-padding-16">
                        <el-scrollbar height="400px">
                            <div v-if="searchKey" class="b-margin-10">
                                <span>以下筛选条件中包含</span><span class="color-blue">【{{ searchKey }}】</span>
                            </div>
                            <div class="display-flex gap-16 flex-wrap">
                                <div v-for="condition in searchConditions" :key="condition.prop"
                                     @click="choseCondition(condition)"
                                     class="pointer children-condition-item border-radius-4 text-center lr-padding-8 tb-padding-4 display-flex border top-bottom-center left-right-center">
                                    {{ condition.name }}
                                </div>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
            </div>
        </el-popover>

        <div class="high-search-rules-item-operator" v-if="
            conditionData.prop &&
                conditionData.dataType !== 'select' &&
                conditionData.dataType !== 'multiSelect' &&
                conditionData.dataType !== 'mapped' &&
                conditionData.dataType !== 'area'
        ">
            <el-select v-model="conditionData.operator" :disabled="readOnly" @change="handleOperatorSelectChange">
                <template v-if="conditionData.dataType !== 'date'">
                    <el-option label="等于" value="equal" />
                    <el-option label="不等于" value="notEqual" />
                    <el-option label="小于" value="lessThan" />
                    <el-option label="小于等于" value="lessThanInclusive" />
                    <el-option label="大于" value="greaterThan" />
                    <el-option label="大于等于" value="greaterThanInclusive" />
                    <el-option label="在范围内" value="numberInRange" />
                </template>
                <template v-else>
                    <el-option label="小于天数" value="dayLessThan" />
                    <el-option label="大于天数" value="dayGreaterThan" />
                </template>
            </el-select>
        </div>
        <div class="high-search-rules-item-value" v-if="conditionData.prop">
            <el-form ref="formRef" :model="conditionData" class="demo-ruleForm" :disabled="readOnly">
                <el-form-item prop="value" :rules="[{ required: true, message: '请输入必选项' }]">
                    <el-select class="width-100"
                               v-if="conditionData.dataType === 'select' || conditionData.dataType === 'multiSelect'"
                               v-model="conditionData.value" :multiple="conditionData.dataType === 'multiSelect'"
                               :clearable="false" @change="handleSelectChange">
                        <el-option v-for="item in getConditionEnums()" :key="item.tagValue" :label="item.name"
                                   :value="item.tagValue" />
                    </el-select>
                    <el-input-tag v-model="conditionData.value" v-else-if="conditionData.dataType === 'itemInput'"
                                  :placeholder="'请输入' + conditionData.propLabel" clearable />
                    <div v-else-if="
                        conditionData.dataType === 'number' ||
                            conditionData.dataType === 'dateRangeMultiSelect' ||
                            conditionData.dataType === 'date'
                    ">
                        <div v-if="conditionData.operator === 'numberInRange' && Array.isArray(conditionData.value)"
                             class="display-flex">
                            <div class="flex-1">
                                <el-input v-model.number="conditionData.value[0]" :clearable="false"></el-input>
                            </div>
                            <div class="text-center" style="width: 20px">-</div>
                            <div class="flex-1">
                                <el-input v-model.number="conditionData.value[1]" :clearable="false"></el-input>
                            </div>
                        </div>
                        <el-input-number v-model="conditionData.value" :min="0" :clearable="false"
                                         v-else></el-input-number>
                    </div>
                    <el-cascader ref="cascaderRef" filterable collapse-tags :collapse-tags-tooltip="true"
                                 :options="getConditionEnums()" :props="{ multiple: true, checkStrictly: true }"
                                 v-model="conditionData.value" class="width-100"
                                 v-else-if="conditionData.dataType === 'mapped' || conditionData.dataType === 'area'"
                                 placeholder="" clearable @change="handleCascaderChange" />
                    <span class="l-margin-10">{{ conditionData.unit }}</span>
                </el-form-item>
            </el-form>
        </div>
        <div v-if="showMatchIng && !readOnly">
            <el-select v-model="conditionData.matching" :clearable="false">
                <el-option label="完全匹配" value="all"></el-option>
                <el-option label="部分匹配" value="part"></el-option>
            </el-select>
        </div>
        <div v-if="readOnly && !isDetail" class="display-flex" style="min-width:200px">
            <div class="r-margin-16">当前值：<span class="font-bold">{{ getNowVal(conditionData) }}</span> </div>
            <div>
                <div class="font-12">匹配度: {{ getMatchRatio(conditionData) }}%</div>
                <div class="t-margin-6">
                    <el-progress striped striped-flow :show-text="false" :percentage="getMatchRatio(conditionData)" />
                </div>
            </div>

        </div>
        <div v-if="!readOnly">
            <el-icon class="pointer" @click="removeCondition">
                <Delete />
            </el-icon>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, computed, defineEmits, watch, inject } from 'vue'
import type { ISearchConditions, IHighSearchRuleItem, IHighSearchRules } from '@/types/model'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import { ClickOutside as vClickOutside } from 'element-plus'

const conditionPopover: Ref<boolean> = ref(false)

const store = useStore()
const cacheMatchRulesData = store.state.app.matchRulesData

const popoverRef = ref()

const cascaderRef = ref()
const readOnly = inject('readOnly') as Ref<boolean>

const isDetail = inject('isDetail')

const activeCategoryName: Ref<string> = ref('')

if (cacheMatchRulesData && cacheMatchRulesData.length) {
    activeCategoryName.value = cacheMatchRulesData?.[0].name || ''
}

// console.log('activeCategoryName', activeCategoryName.value)

const props = defineProps({
    conditionItem: {
        type: Object,
        default: () => {
            return {
                operator: 'equal',
                prop: '',
                propLabel: '',
                value: [],
                name: '',
            }
        },
    },
})

const emits = defineEmits(['updateCondition', 'removeCondition'])

const conditionData: Ref<ISearchConditions> = ref({
    operator: 'equal',
    prop: '',
    value: [],
})

// console.log('props.conditionItem', props.conditionItem)

let cloneConditionItem = JSON.parse(JSON.stringify(props.conditionItem))
if (cloneConditionItem.value === false) {
    cloneConditionItem.value = '0'
}
if (cloneConditionItem.value === true) {
    cloneConditionItem.value = '1'
}
conditionData.value = { ...conditionData.value, ...cloneConditionItem }

const searchKey: Ref<string> = ref('')

const searchConditions = computed(() => {
    if (searchKey.value) {
        let list: ISearchConditions[] = []
        let reg = new RegExp(searchKey.value, 'g')
        cacheMatchRulesData.forEach((category: IHighSearchRules) => {
            if (category.children) {
                list = list.concat(
                    category.children
                        .filter((ch) => {
                            if (!ch.name) {
                                return false
                            } else {
                                return reg.test(ch.name)
                            }
                        })
                        .map((ch) => {
                            let operator = ''
                            if (ch.dataType === 'date') {
                                operator = 'dayLessThan'
                            } else if (ch.dataType === 'mapped' || ch.dataType === 'area') {
                                operator = 'equal'
                            } else {
                                operator = 'equal'
                            }
                            return {
                                ...ch,
                                operator,
                                value: [],
                                prop: ch.key,
                                propLabel: ch.name,
                            }
                        })
                )
            }
        })
        return list
    } else {
        return cacheMatchRulesData.find((item: IHighSearchRules) => {
            return item.name === activeCategoryName.value
        }).children
    }
})

const choseCondition = (condition: IHighSearchRuleItem) => {
    console.log(condition)
    conditionData.value.prop = condition.key
    conditionData.value.propLabel = condition.name
    conditionData.value.dataType = condition.dataType
    conditionData.value.unit = condition.unit || ''
    conditionData.value.matching = 'all'
    if (condition.dataType === 'number') {
        conditionData.value.operator = 'equal'
        conditionData.value.value = [0, 0]
    } else if (condition.dataType === 'select') {
        conditionData.value.operator = 'equal'
        conditionData.value.value = condition.enums[0].tagValue
    } else if (condition.dataType === 'multiSelect') {
        conditionData.value.operator = 'inAll'
        conditionData.value.value = []
    } else if (condition.dataType === 'date') {
        conditionData.value.operator = 'dayLessThan'
    } else {
        conditionData.value.value = []
    }
    conditionPopover.value = false
}
const getConditionEnums = () => {
    let enums: Array<{
        name: string
        tagValue: string | number
    }> = []
    cacheMatchRulesData.forEach((category: IHighSearchRules) => {
        if (category.children) {
            let res = category.children.find((item) => {
                return item.key === conditionData.value.prop
            })
            if (res && res.needSearch) {
                enums = store.state.app.staticConfig[res.needSearch]
            } else if (res) {
                enums = res.enums
            }
        }
    })
    return enums
}

const handleSelectChange = () => { }


const showMatchIng = computed(() => {
    let arr = ['lessThan', 'dayLessThan', 'lessThanInclusive', 'greaterThan', 'greaterThanInclusive', 'dayGreaterThan']

    if (arr.find((i) => { return i === conditionData.value.operator })) {
        return true
    } else {
        return false
    }
})

const showPopover = () => {
    if (readOnly) {
        return
    }
    conditionPopover.value = true
}
const handleCascaderChange = () => {
    let nodes = cascaderRef.value?.getCheckedNodes()
    conditionData.value.valueLabel = nodes?.map((item: { value: string; label: string; level: number }) => {
        return `${item.value},${item.label},${item.level}`
    })
}

watch(
    conditionData,
    (nVal) => {
        emits('updateCondition', nVal)
    },
    { deep: true }
)

const removeCondition = () => {
    emits('removeCondition')
}

const onClickOutside = () => {
    conditionPopover.value = false
}

const scoreInfo = inject('scoreInfo') as Record<string, number | string>
const getMatchRatio = (condition: ISearchConditions) => {
    let score = scoreInfo[`${condition.id}_${condition.prop}_score`] as number
    if (!score || !condition.maxScore) {
        return 0
    }
    return ((score / condition.maxScore) * 100).toFixed(2)
}
console.log(scoreInfo)
const getNowVal = (condition: ISearchConditions) => {
    if (scoreInfo[condition.prop + '_label']) {
        return scoreInfo[condition.prop + '_label']
    } else if (scoreInfo[condition.prop] !== '' && scoreInfo[condition.prop] !== null && scoreInfo[condition.prop] !== undefined) {
        return scoreInfo[condition.prop]
    } else {
        return '-'
    }
    // return scoreInfo[condition.prop + '_label'] || scoreInfo[condition.prop] || '-'
}

const handleOperatorSelectChange = (val: string) => {
    if (val === 'numberInRange') {
        conditionData.value.value = [0, 0]
    } else {
        conditionData.value.value = 0
    }
}

onMounted(() => { })
</script>

<style lang="scss" scoped>
.high-search-rules-item-key {
    min-height: 32px;
    box-sizing: border-box;
    padding: 4px 12px;
    width: 300px;
    line-height: 24px;
    color: var(--el-text-color-placeholder);
    display: flex;
    box-shadow: 0 0 0 1px var(--border-color) inset;
}

.high-search-rules-item-operator {
    width: 180px;
}

.high-search-rules-item-value {
    // width: 500px;

    &:deep .el-form-item {
        margin-bottom: 0;
    }
}

.condition-item {
    --el-border-color: #e8e8e8;
    --el-text-color-regular: var(--el-text-color-placeholder);
}

.chose-condition-input {
    border-bottom: 1px solid var(--el-border-color);
}

.chose-condition-left {
    border-right: 1px solid var(--border-color);
    width: 110px;

    .chose-condition-category-item {
        width: 96px;
    }
}

@mixin active-chose-condition-category-item {
    background-color: var(--active-bg-);
    color: var(--main-blue-);
}

.chose-condition-category-item:hover {
    @include active-chose-condition-category-item;
}

.active-chose-condition-category-item {
    @include active-chose-condition-category-item;
}

.children-condition-item {
    width: 130px;
    height: 70px;

    &:hover {
        @include active-chose-condition-category-item;
    }
}
</style>
