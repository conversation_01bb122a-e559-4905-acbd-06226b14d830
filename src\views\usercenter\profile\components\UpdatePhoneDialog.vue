<script lang="ts" setup>
import systemService from '@/service/systemService'
import userService from '@/service/userService'
import type { RootState } from '@/types/store'
import { phoneValidate } from '@/utils/validate'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const emit = defineEmits(['update:visible'])

const props = defineProps<{
    visible: boolean
}>()

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()
const dialogTitle = computed(() => {
    return '更换手机号'
})

// ====================== Refs & Reactive State ======================
const formRef = ref<FormInstance | null>(null)
const dialogVisible = ref(false)
const loading = ref(false)
const form = reactive<{
    mobile: string
    code: string
}>({
    mobile: '',
    code: '',
})
const rules = reactive<FormRules<typeof form>>({
    mobile: [
        { required: true, message: '请输入手机号码', trigger: 'change' },
        {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
        },
    ],
    code: [{ required: true, message: '请输入手机验证码', trigger: 'change' }],
})

const querying = ref(false)
const countdown = ref(0) // 记录倒计时秒数
const timer = ref<ReturnType<typeof setInterval> | null>(null)

// ====================== Methods ======================
const handleClose = () => {
    emit('update:visible', false)
    countdown.value = 0
    timer.value = null
    formRef.value?.resetFields()
    form.mobile = ''
    form.code = ''
}

const save = (formEle: FormInstance | null) => {
    if (loading.value) return
    if (!formEle) return
    loading.value = true
    formEle.validate((valid) => {
        if (valid) {
            systemService
                .userAuthMobile({ ...form })
                .then(() => {
                    ElMessage.success('修改成功')
                    getAccountInfo()
                    handleClose()
                })
                .catch(() => {
                    // ElMessage.error('修改失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

const getCode = async () => {
    const { mobile } = form || {}

    if (!mobile) {
        ElMessage.error('手机号码不能为空')
        return
    }

    if (!phoneValidate(mobile.trim())) {
        ElMessage.error('手机号码格式不正确')
        return
    }

    if (countdown.value > 0 || querying.value) return // 防止重复点击

    querying.value = true
    console.log('请求验证码...')

    systemService
        .userSendAuthMobileCode({
            mobile: mobile.trim(),
        })
        .then(() => {
            console.log('验证码发送成功')
            // 开始倒计时
            countdown.value = 60
            querying.value = false // 取消 loading
            timer.value = setInterval(() => {
                countdown.value--
                if (countdown.value <= 0 && timer.value) {
                    clearInterval(timer.value)
                    timer.value = null
                }
            }, 1000)
        })
        .catch((err) => {
            console.log(err)
            querying.value = false
        })
}

const getAccountInfo = () => {
    userService.userGetAccountInfo().then((account) => {
        console.log('accountaccountaccount')
        console.log(account)
        store.dispatch('user/setAccountInfo', { ...account })
    })
}

// ====================== Watchers ======================
watch(
    () => props.visible,
    (val) => {
        dialogVisible.value = val
    }
)

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div class="update-phone-dialog flex flex-column gap-16">
            <el-form ref="formRef" :rules="rules" :model="form" :hide-required-asterisk="true">
                <el-form-item label="手机号码" label-position="top" prop="mobile">
                    <el-input v-model="form.mobile" clearable placeholder="请输入需要绑定的新号码" />
                </el-form-item>
                <el-form-item label="验证码" label-position="top" prop="code">
                    <div class="flex flex-row width-100 gap-16">
                        <el-input v-model="form.code" clearable placeholder="请输入手机验证码" class="flex flex-1" />
                        <el-button
                            type="primary"
                            :disabled="countdown > 0"
                            :loading="querying"
                            @click="getCode"
                            style="height: 45px"
                            class="w-94"
                        >
                            {{ countdown > 0 ? `${countdown} 秒` : querying ? '' : '获取验证码' }}
                        </el-button>
                    </div>
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose" style="height: 40px">取消</el-button>
                <el-button type="primary" @click="save(formRef)" :loading="loading" style="height: 40px">
                    确认
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
.update-password-dialog :deep(.el-select__wrapper) {
    height: 45px;
    font-size: 14px;
}

.update-phone-dialog :deep(.el-input__inner) {
    height: 45px;
    font-size: 14px;
}

.update-phone-dialog :deep(.el-input__wrapper) {
    padding-top: 0;
    padding-bottom: 0;
}
</style>
