<template>
    <el-form :inline="true" :model="formReport" >
        <el-form-item>
            <div class="flex-column">
                <span>高企报告封面</span>
                <el-upload
                    v-if="!gqCoverUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleGqCoverProgress"
                    :on-success="handleGqCoverSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="gqCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="gqCoverUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[gqCoverUrl] />
                    <el-icon class="close-icon" @click="deleteGQCover"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>税务报告封面</span>
                <el-upload
                    v-if="!swCoverUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleSwCoverProgress"
                    :on-success="handleSwCoverSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="swCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="swCoverUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[swCoverUrl] />
                    <el-icon class="close-icon" @click="deleteSWCover"><Close /></el-icon>
                </div>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="flex-column">
                <span>发票报告封面</span>
                <el-upload
                    v-if="!fpCoverUrl"
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleFpCoverProgress"
                    :on-success="handleFpCoverSuccess"
                    :before-upload="beforePicUpload"
                >
                    <el-icon v-if="fpCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                </el-upload>
                <div v-else class="image-container">
                    <el-image :src="fpCoverUrl" class="avatar" style="height: 100%;" fit="fill" :preview-src-list=[fpCoverUrl] />
                    <el-icon class="close-icon" @click="deleteFPCover"><Close /></el-icon>
                </div>            
            </div>
        </el-form-item>
    </el-form>
</template>

<script lang='ts' setup>
import { ref, watch, nextTick } from 'vue'
import type { UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import type { bgCover } from '@/types/OEM'
import fileService from '@/service/fileService'

const props = defineProps<{
    modelValue: bgCover
    editData: bgCover
}>()
watch(() => props.editData, (newValue) => {
    console.log('newValue',newValue)
    nextTick(() => {
        gqCoverUrl.value = newValue.gqbgCover ? fileService.getFileUrl(newValue.gqbgCover) : ''
        fpCoverUrl.value = newValue.fpbgCover ? fileService.getFileUrl(newValue.fpbgCover) : ''
        swCoverUrl.value = newValue.swbgCover ? fileService.getFileUrl(newValue.swbgCover) : ''
    })
},{immediate: true, deep: true })

const emit = defineEmits<{
    (e: 'update:modelValue', value: bgCover): void
}>()
const formReport = ref<bgCover>(props.modelValue)
watch(formReport, (newValue) => {
    emit('update:modelValue', newValue)
})

// 上传文件接口
const uploadFile = `/api/zhenqi-crm/file/upload-temp`
// 请求头
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}
// ======报告======
// 高企报告封面上传相关
const gqCoverUrl = ref('')
const gqCoverLoading = ref(false)
const handleGqCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    gqCoverLoading.value = true
}
const handleGqCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    gqCoverLoading.value = false
    gqCoverUrl.value = response.data.link
}

// 税务报告封面上传相关
const swCoverUrl = ref('')
const swCoverLoading = ref(false)
const handleSwCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    swCoverLoading.value = true
}
const handleSwCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    swCoverLoading.value = false
    swCoverUrl.value = response.data.link
}

// 发票报告封面上传相关
const fpCoverUrl = ref('')
const fpCoverLoading = ref(false)
const handleFpCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    fpCoverLoading.value = true
}
const handleFpCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    fpCoverLoading.value = false
    fpCoverUrl.value = response.data.link
}

const beforePicUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (rawFile.size / 1024 / 1024 > 5) {
        ElMessage.error('文件大小不能超过5mb!')
        return false
    }
    return true
}

const deleteGQCover = () => {
    gqCoverUrl.value = ''
}

const deleteSWCover = () => {
    swCoverUrl.value = ''
}

const deleteFPCover = () => {
    fpCoverUrl.value = ''
}

// 报告相关
watch([gqCoverUrl, swCoverUrl, fpCoverUrl], () => {
    formReport.value.gqbgCover = gqCoverUrl.value 
    formReport.value.swbgCover = swCoverUrl.value 
    formReport.value.fpbgCover = fpCoverUrl.value
})
</script>

<style lang='scss' scoped>
.close-icon {
  position: absolute;
  right: 0;
  top: 0;
  color: red;
  cursor: pointer;
  display: none;
}

.image-container {
    position: relative;
    width: 50px;
    height: 50px;
}

.image-container:hover .close-icon {
    display: block; 
}
:deep(.el-form-item){
    width: 45% !important;
    height: 60px !important;
}

:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}

:deep(.upload-demo){
    display: flex;
    width: 300px;
}
:deep(.el-upload-list__item){
    width: 180px;
}

:deep(.inline-upload) {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 20px;
}
</style>