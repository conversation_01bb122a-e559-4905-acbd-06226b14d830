//应用相关缓存数据

import type { IAicConditionData, IAicNormalSearchRules } from '@/types/aic'
import type { IHighSearchRules } from '@/types/model'
import type { AppState, RootState } from '@/types/store'
import { type Module } from 'vuex'
export const getAppDefaultState = (): AppState => ({
    hightSearchRulesData: null,
    staticConfig: null,
    normalSearchRulesData: [],
    tenderSearchRulesData: [],
    factorySearchRulesData: [],
    projectSearchRulesData: [],
    matchRulesData: [],
    lastEntSearchRulesData: [],
})

const appModule: Module<AppState, RootState> = {
    namespaced: true,
    state: getAppDefaultState(),
    getters: {
        hightSearchRulesData: (state: AppState) => state.hightSearchRulesData,
        staticConfig: (state: AppState) => state.staticConfig,
        normalSearchRulesData: (state: AppState) => state.normalSearchRulesData,
    },
    mutations: {
        SET_HIGHT_SEARCH_RULES_DATA(state: AppState, hightSearchRulesData: IHighSearchRules[] | null) {
            state.hightSearchRulesData = hightSearchRulesData
        },
        SET_STATIC_CONFIG(state: AppState, staticConfig: IAicConditionData) {
            state.staticConfig = staticConfig
        },
        SET_NORMAL_SEARCH_RULES_DATA(state: AppState, normalSearchRulesData: IAicNormalSearchRules[]) {
            console.log('SET_NORMAL_SEARCH_RULES_DATA')
            state.normalSearchRulesData = normalSearchRulesData
        },
        SET_TENDER_SEARCH_RULES_DATA(state: AppState, tenderSearchRulesData: IAicNormalSearchRules[]) {
            console.log('SET_TENDER_SEARCH_RULES_DATA')
            state.tenderSearchRulesData = tenderSearchRulesData
        },
        SET_FACTORY_SEARCH_RULES_DATA(state: AppState, factorySearchRulesData: IAicNormalSearchRules[]) {
            console.log('SET_FACTORY_SEARCH_RULES_DATA')
            state.factorySearchRulesData = factorySearchRulesData
        },
        SET_PROJECT_SEARCH_RULES_DATA(state: AppState, projectSearchRulesData: IAicNormalSearchRules[]) {
            console.log('SET_PROJECT_SEARCH_RULES_DATA')
            state.projectSearchRulesData = projectSearchRulesData
        },
        SET_MATCH_RULES_DATA(state: AppState, matchRulesData: IAicNormalSearchRules[]) {
            state.matchRulesData = matchRulesData
        },
        SET_LAST_ENT_SEARCH_RULES_DATA(state: AppState, lastEntSearchRulesData: IAicNormalSearchRules[]) {
            console.log('SET_LAST_ENT_SEARCH_RULES_DATA')
            state.lastEntSearchRulesData = lastEntSearchRulesData
        },
    },
    actions: {
        setStaticConfig({ commit }, staticConfigRes: IAicConditionData) {
            commit('SET_STATIC_CONFIG', staticConfigRes)
        },
        setHightSearchRulesData({ commit }, rulesRes: IHighSearchRules[]) {
            commit('SET_HIGHT_SEARCH_RULES_DATA', rulesRes)
        },
        setNormalSearchRulesData({ commit }, rulesRes: IAicNormalSearchRules[]) {
            commit('SET_NORMAL_SEARCH_RULES_DATA', rulesRes)
        },
        setTenderSearchRulesData({ commit }, rulesRes: IAicNormalSearchRules[]) {
            commit('SET_TENDER_SEARCH_RULES_DATA', rulesRes)
        },
        setFactorySearchRulesData({ commit }, rulesRes: IAicNormalSearchRules[]) {
            commit('SET_FACTORY_SEARCH_RULES_DATA', rulesRes)
        },
        setProjectSearchRulesData({ commit }, rulesRes: IAicNormalSearchRules[]) {
            commit('SET_PROJECT_SEARCH_RULES_DATA', rulesRes)
        },
        setLastEntSearchRulesData({ commit }, rulesRes: IAicNormalSearchRules[]) {
            commit('SET_LAST_ENT_SEARCH_RULES_DATA', rulesRes)
        },
    },
}

export default appModule
