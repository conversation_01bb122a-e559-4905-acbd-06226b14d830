<template>
    <el-table
        ref="tableRef"
        height="500"
        :data="tableList"
        @selection-change="selectionChange"
        show-overflow-tooltip
        :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
    >
        <template #empty>
            <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                <div class="font-first-title-unactive color-two-grey">暂无数据</div>
            </div>
        </template>
        <el-table-column label="公告信息">
            <template #default="scope">
                <div class="display-flex">
                    <div class="display-flex align-center t-margin-10">
                        <div
                            class="font-18 color-black overflow-ellipsis"
                            :class="{ pointer: scope.row.url }"
                            @click="scope.row.url ? rowClickToDetail(scope.row) : null"
                            :title="scope.row.title"
                        >
                            {{ scope.row.title }}
                        </div>
                        <div class="l-margin-4" v-if="scope.row.tenderType">
                            <el-tag class="font-14 color-green" effect="plain">
                                {{ scope.row.tenderType }} | {{ scope.row.noticeTypeSub }}
                            </el-tag>
                        </div>
                    </div>
                    <div class="pointer" style="margin-left: auto" v-if="scope.row.url">
                        <Icon icon="icon-icon_link" color="blue" @click="rowClickToUrl(scope.row.url)"></Icon>
                    </div>
                </div>
                <div class="display-flex t-margin-4 font-16">
                    <div>标的物：{{ scope.row.name ? scope.row.name : '-' }}</div>
                    <div class="l-margin-391">所属地区：{{ scope.row.companyArea ? scope.row.companyArea : '-' }}</div>
                    <div style="margin-left: auto">{{ scope.row.tenderPublicDate }}</div>
                </div>
                <div class="t-margin-4 font-16" style="background-color: #fafafa;">
                    <span v-if="scope.row.outbidUnitList.length" >中标单位：
                        <span v-for="(tagItem, i) in scope.row.outbidUnitList" :key="i" style="display: block">
                            {{ tagItem.name }}
                        </span>
                    </span>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <TenderProjectDetail v-if="showTenderProjectDetail" v-model:showTenderProjectDetail="showTenderProjectDetail" :ossId="ossId"/>
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue'
import Icon from '@/components/common/Icon.vue'
import type { ISearchBidProjectItem } from '@/types/company'
import TenderProjectDetail from './TenderProjectDetail.vue'

const ossId = ref<string>('')
const props = defineProps<{
    tableData: ISearchBidProjectItem[]
}>()

const showTenderProjectDetail = ref(false)

const tableList = ref<ISearchBidProjectItem[]>([])
const emit = defineEmits(['selection-change'])

const tableRef = ref(null)

const selectionChange = (val: ISearchBidProjectItem[]) => {
    emit('selection-change', val)
    console.log('选中的行', val)
}

const rowClickToUrl = (url: string) => {
    window.open(url)
}

const rowClickToDetail = (row: ISearchBidProjectItem) => {
    showTenderProjectDetail.value = true
    console.log('点击的标题', row.ossUrl)
    if(row.ossUrl){
        ossId.value = row.ossUrl
    }  
}

watch(
    () => props.tableData,
    (newTableData) => {
        tableList.value = newTableData
    }
)

</script>

<style scoped>
.display-block{
    display: block;
}

.l-margin-391 {
    margin-left: 391px;
}

.align-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.overflow-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 1000px;
}
</style>
