<script lang="ts" setup>
// import systemService from '@/service/systemService'
import commonData from '@/js/common-data'
import type { IAutoDialerTaskDetailFilter, IAutoDialerTaskDetailFilterForm } from '@/types/autoDialer'
// import type { IRoleItem } from '@/types/role'
import type { FormInstance } from 'element-plus'
import { reactive, ref } from 'vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    getData: (params: IAutoDialerTaskDetailFilter) => void
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const formRef = ref<FormInstance>()

const queryForm = reactive<IAutoDialerTaskDetailFilterForm>({
    phoneNumber: '',
    companyName: '',
    status: '',
    aiTagName: '',
    date: [],
    taskName: '',
})

// ====================== Methods ======================
const onSubmit = () => {
    console.log('queryForm', queryForm)

    const params: IAutoDialerTaskDetailFilter = {
        phoneNumber: queryForm.phoneNumber,
        companyName: queryForm.companyName,
        status: queryForm.status,
        aiTagName: queryForm.aiTagName,
        startCallTime: '',
        endCallTime: '',
        taskName: queryForm.taskName || '',
    }

    if (queryForm.date && Array.isArray(queryForm.date) && queryForm.date.length === 2) {
        params.startCallTime = queryForm.date[0].toString()
        params.endCallTime = queryForm.date[1].toString()
    }

    props.getData(params)
}

const reset = (formEl?: FormInstance) => {
    formEl?.resetFields()
    onSubmit()
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <el-form ref="formRef" :inline="true" :model="queryForm" :label-width="112" label-suffix=":">
        <div class="flex flex-row filter flex-wrap">
            <el-form-item label="联系电话" label-position="left" prop="phoneNumber">
                <el-input v-model="queryForm.phoneNumber" placeholder="请输入联系电话" clearable style="width: 400px" />
            </el-form-item>
            <el-form-item label="任务名称" label-position="left" prop="taskName">
                <el-input v-model="queryForm.taskName" placeholder="请输入企业名称" clearable style="width: 400px" />
            </el-form-item>
            <el-form-item label="企业名称" label-position="left" prop="companyName">
                <el-input v-model="queryForm.companyName" placeholder="请输入企业名称" clearable style="width: 400px" />
            </el-form-item>
            <el-form-item label="接通状态" label-position="left" prop="status">
                <el-select
                    v-model="queryForm.status"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    clearable
                    placeholder="全部状态"
                    style="width: 400px"
                    @change="onSubmit"
                >
                    <el-option
                        v-for="option in commonData.callStatusList"
                        :label="option.label"
                        :value="option.value"
                        :key="option.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="对话标签" label-position="left" prop="aiTagName">
                <el-select
                    v-model="queryForm.aiTagName"
                    clearable
                    placeholder="全部标签"
                    style="width: 400px"
                    @change="onSubmit"
                >
                    <el-option
                        v-for="option in [
                            {
                                label: '有兴趣',
                                value: '有兴趣',
                            },
                            {
                                label: '没兴趣',
                                value: '没兴趣',
                            },
                        ]"
                        :label="option.label"
                        :value="option.value"
                        :key="option.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="接通时间" label-position="left" prop="date">
                <el-date-picker
                    v-model="queryForm.date"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="common-width"
                    style="width: 400px"
                />
            </el-form-item>
        </div>
        <div class="flex flex-row gap-16 justify-end filter">
            <el-form-item>
                <el-button @click="reset(formRef)">清空</el-button>
                <el-button type="primary" @click="onSubmit">搜索</el-button>
            </el-form-item>
        </div>
    </el-form>
</template>

<style lang="scss" scoped></style>
