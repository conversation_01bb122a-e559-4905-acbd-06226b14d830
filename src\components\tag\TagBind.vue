<template>
    <el-input v-model="TagName" placeholder="请输入标签名称" clearable>
        <template #prefix>
            <Icon icon="icon-a-chaz<PERSON>qiye"></Icon>
        </template>
    </el-input>
    
    <div class="chose-box" v-loading="loadingList">
        <el-scrollbar>
            <div v-if="!filterList.length" class="no-data"></div>
            <div v-else>
                <el-checkbox-group v-model="checkIds">
                    <div v-for="tag in filterList" :key="tag.id" class="">
                        <el-checkbox :value="tag.id">
                            <div class="display-flex top-bottom-center">
                                <div
                                    class="color-item r-margin-6"
                                    :style="{
                                        backgroundColor: tag.color
                                    }"
                                ></div>
                                <div class="tag-name" :title="tag.tagName">{{ tag.tagName }}</div>
                            </div>
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
            </div>
        </el-scrollbar>
    </div>
    <div class="border-bottom t-margin-10 b-margin-10"></div>
    <div class="display-flex top-bottom-center">
        <div class="display-flex">
            <div v-if="permissionService.isTagMenuAddPermitted()" class="pointer color-primary" @click="handleGoToAdd">新增</div>
            <div v-if="permissionService.isTagMenuAddPermitted() && permissionService.isShowTagManagementMenu()" class="l-margin-10 r-margin-10" style="color: #E8E8E8;">|</div>
            <div v-if="permissionService.isShowTagManagementMenu()" class="pointer color-primary" @click="handleJumpToManage">管理</div>
        </div>
        <view style="margin-left: auto;">
            <el-button @click="handleCancel" style="width: 50px;">取 消</el-button>
            <el-button type="primary" style="width: 50px;" @click="confirm" :loading="loading">确 定</el-button>
        </view>
    </div>
</template>

<script lang='ts' setup>
import type { IGetTagList, ITagInfo, ICrmUpdateParams } from '@/types/lead'
import { ref, onMounted, computed, onUnmounted, watch, onBeforeMount } from 'vue'
import Icon from '../common/Icon.vue'
import { ElMessage } from 'element-plus'
import crmService from '@/service/crmService'
import permissionService from '@/service/permissionService'

const props = defineProps<{
    tagInfos: ITagInfo[]
    unBindId: string
    leadId: string
    type: string
}>()

const bindTagsParams = ref<ICrmUpdateParams>({
    updateTags:true,
    leadId: props.leadId,
})
const tagList = ref<IGetTagList[]>([])
const TagName = ref('')
const pageInfo = ref({
    page: 1,
    pageSize: 1000,
})

const checkIds = ref<string[]>([])
const loading = ref(false)
// const hasMore = ref(true)
const loadingList = ref(false)

const emit = defineEmits(['cancel','goToAdd','confirmTags'])
// 重置选中状态,在父组件关闭popover事件中调用
const resetCheckIds = () => {
    // console.log('resetCheckIds')
    checkIds.value = props.tagInfos.map((item: ITagInfo) => item.id)
}
const handleCancel = () => {
    // resetCheckIds()
    emit('cancel')
}
const handleGoToAdd = () => {
    emit('goToAdd')
}

// 解绑时，把已选择的id从checkIds中移除
const unBindId = computed(() => props.unBindId)
watch(unBindId, (newVal) => {
    if (newVal) {
        checkIds.value = checkIds.value.filter((id) => id!== newVal)
    }
})

const tagInfosLength = computed(() => props.tagInfos.length)
watch(tagInfosLength, (newVal) => {
    // console.log('props.companyTagsInfo',props.tagInfos)
    if(newVal > 0){
        checkIds.value = props.tagInfos.map((item: ITagInfo) => item.id)
    }else{
        checkIds.value = []
    }
})

// selectedTagList来存入选中的标签
const selectedTagList = ref<IGetTagList[]>([])
const confirm = () => {
    loading.value = true
    // console.log('checkIds.value',checkIds.value)
    bindTagsParams.value.crmTagIds = checkIds.value
    // console.log('checkIds.value',checkIds.value)
    // console.log('props.tagInfos',props.tagInfos)
    if(checkIds.value.length !== props.tagInfos.length || checkIds.value.some((id) => !props.tagInfos.some((item) => item.id === id))){
        crmService.crmUpdate(bindTagsParams.value).then(() => {
            ElMessage.success('操作成功')
        })
    }
    selectedTagList.value = tagList.value.filter((tag: IGetTagList) => checkIds.value.includes(tag.id))
    // console.log('selectedTagList.value',selectedTagList.value)
    emit('confirmTags', selectedTagList.value)
    loading.value = false
}

const getTagList = async (page: number) => {
    loadingList.value = true
    try {
        pageInfo.value.page = page
        const res = await crmService.crmTagList(pageInfo.value)
        tagList.value = res.data
        // hasMore.value = res.data.length === pageInfo.value.pageSize
    } catch (error) {
        console.error(error)
    } finally {
        loadingList.value = false
    }
}

// // 滚动加载
// const handleScroll = (event: Event) => {
//     const target = event.target as HTMLElement
//     if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 10 && !loadingList.value && hasMore.value) {
//         loadMore()
//     }
// }
// const loadMore = async () => {
//     if (loadingList.value) return
//     pageInfo.value.page++
//     loadingList.value = true
//     try {
//         const res = await crmService.crmTagList(pageInfo.value)
//         tagList.value.push(...res.data)
//         hasMore.value = res.data.length === pageInfo.value.pageSize
//     } catch (error) {
//         console.error(error)
//     } finally {
//         loadingList.value = false
//     }
// }

// 模糊匹配列表
const filterList = computed(() => {
    if (!TagName.value) {
        return tagList.value
    }
    return tagList.value.filter((tag: IGetTagList) => tag.tagName.includes(TagName.value))
})

const handleJumpToManage = () => {
    window.location.href = '/system-management/crm-tagging'
}

onBeforeMount(() => {
})

onMounted(() => {
    // console.log('props.companyTagsInfo',props.tagInfos)
    if(props.tagInfos && props.tagInfos.length > 0){
        checkIds.value = props.tagInfos.map((item: ITagInfo) => item.id)
    }else{
        checkIds.value = []
    }
    // window.addEventListener('scroll', handleScroll) 
})

onUnmounted(() => {
    // window.removeEventListener('scroll', handleScroll) 
})

defineExpose({
    getTagList,
    resetCheckIds
})

</script>

<style lang='scss' scoped>
.chose-box {
	height: 200px;
	overflow-y: auto;
	margin-top: 16px;
	scrollbar-width: none;
}
.color-item {
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.tag-name {
    width: 280px;
    height: 18px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.no-data {
    background: url('@/assets/images/no-chart-data.png');
    background-size: 240px 200px;
    background-repeat: no-repeat;
    background-position-x: center;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scrollbar-demo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  margin: 10px;
  text-align: center;
  border-radius: 4px;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
</style>
