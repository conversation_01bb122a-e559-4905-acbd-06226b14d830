<script lang="ts" setup>
import { computed, onBeforeMount, onMounted, provide, ref } from 'vue'
import aicService from '@/service/aicService'
import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { formatSelectedLabel } from '@/utils/enterprise/region'
import {
    MappedFilter,
    MultiSelectFilter,
    OptionsFilter,
    RegionFilter,
    SelectFilter,
} from '@/components/enterprise/filters'

const storeInitAction = 'enterprise/initLastEntParams'
const storeSetAction = 'enterprise/setLastEntParams'
const store = useStore<RootState>()
const rules = ref<IAicNormalSearchRules[]>([])
const config = ref<IAicConditionData | null>(null)
const compressRules = ref<IAicNormalSearchRules[]>([])
const collapse = ref(true)

const cachedRules = computed(() => {
    const { lastEntSearchRulesData } = store.state.app || {}
    return JSON.parse(JSON.stringify(lastEntSearchRulesData)) || []
})

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

const lastEntFilterParams = computed<INormalFilterParams[]>(() => {
    const { lastEntFilterParams } = store.state.enterprise || {}
    return JSON.parse(JSON.stringify(lastEntFilterParams)) || []
})

const getRules = async () => {
    if (cachedRules.value && Array.isArray(cachedRules.value) && cachedRules.value.length > 0) {
        rules.value = formatRules(cachedRules.value)
        compressRules.value = rules.value.slice(6)
        getStaticConfig()
    } else {
        const rulesRes = await aicService.conditionGetInfoForNomal({ searchType: 'lastEnt' })
        store.dispatch('app/setLastEntSearchRulesData', rulesRes)

        rules.value = formatRules(JSON.parse(JSON.stringify(rulesRes)))
        compressRules.value = rules.value.slice(6)

        getStaticConfig()
    }
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
    }
}

const formatRules = (rules: IAicNormalSearchRules[]) => {
    // return rules.filter((e) => e.dataType === 'select' || e.dataType === 'multiSelect')
    return rules
}

const isAreaFilterShown = (item: IAicNormalSearchRules, index: number) => {
    return item.dataType === 'area' && index < 6
}

const isMappedFilterShown = (item: IAicNormalSearchRules, index: number) => {
    return item.dataType === 'mapped' && index < 6
}

const isSelectFilterShown = (item: IAicNormalSearchRules, index: number) => {
    return item.dataType === 'select' && index < 6
}

const isMultiSelectFilterShown = (item: IAicNormalSearchRules, index: number) => {
    return item.dataType === 'multiSelect' && index < 6
}

const isCollapse = (index: number) => {
    return index >= 10 && collapse.value
}

const toggleCollapse = () => {
    collapse.value = !collapse.value
}

const isHasSelected = computed(() => {
    const { lastEntFilterParams } = store.state.enterprise
    return lastEntFilterParams.length > 0
})

const clearAllSelect = () => {
    if (isHasSelected.value) {
        store.dispatch(storeInitAction)
    } else {
        toggleCollapse()
    }
}

const removeSelected = (params: INormalFilterParams) => {
    // console.log('remove selected', params)
    store.dispatch(storeSetAction, { ...params, checked: false })
}

// 推送到vuex
const pushToGlobal = (params: INormalFilterParams[]) => {
    console.log('push to global last ent filter params', params)
    store.dispatch(storeSetAction, params)
}

// 子组件使用
provide('pushToGlobal', pushToGlobal)

onMounted(() => {
    getRules()
})

onBeforeMount(() => {
    store.dispatch(storeInitAction)
})
</script>

<template>
    <div>
        <div class="flex flex-column gap-18">
            <template v-for="(item, index) in rules" :key="item.key">
                <template v-if="isAreaFilterShown(item, index)">
                    <RegionFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.lastEntFilterParams"
                    />
                </template>
                <template v-if="isMappedFilterShown(item, index)">
                    <MappedFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.lastEntFilterParams"
                    />
                </template>
                <template v-if="isSelectFilterShown(item, index)">
                    <SelectFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.lastEntFilterParams"
                    />
                </template>
                <template v-if="isMultiSelectFilterShown(item, index)">
                    <MultiSelectFilter
                        :data="item"
                        v-show="!isCollapse(index)"
                        :storeParams="store.state.enterprise.lastEntFilterParams"
                    />
                </template>
            </template>
            <OptionsFilter
                :options="compressRules"
                v-show="!collapse"
                :storeParams="store.state.enterprise.lastEntFilterParams"
            />
        </div>
        <div class="split-line t-margin-20 b-margin-20"></div>
        <div class="flex flex-row">
            <div class="w-140 color-blue font-14 pointer" @click="clearAllSelect">
                <span v-show="isHasSelected">清空所有</span>
            </div>
        </div>
        <div class="flex tb-padding-16" v-if="lastEntFilterParams.length > 0">
            <el-scrollbar view-class="flex flex-row gap-8 flex-wrap top-bottom-start max-height-86">
                <div
                    v-for="params in lastEntFilterParams"
                    :key="params.categoryKey + params.value"
                    class="font-14 tb-padding-4 lr-padding-6 border-color-blue border-radius-4 flex flex-row pointer tag"
                    @click="removeSelected(params)"
                >
                    {{ params.category }}：
                    <span class="color-blue"> {{ formatSelectedLabel(params) }}</span>
                    <span class="flex top-bottom-center left-right-center l-padding-2">
                        <el-icon class="close-btn"><CloseBold /></el-icon>
                    </span>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.tag:hover .close-btn {
    color: var(--dark-red-);
}
</style>
