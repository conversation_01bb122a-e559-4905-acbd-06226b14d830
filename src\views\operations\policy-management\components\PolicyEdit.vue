<template>
    <el-dialog v-model="dialogVisible" :title="formData.id ? '编辑' : '新增'" width="800" @close="close">
        <el-form :model="formData" :label-position="'top'" ref="formRef" :rules="rules">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="政策类型" required prop="childGoodsType">
                        <el-select v-model="formData.childGoodsType" placeholder="政策类型" clearable :empty-values="['']">
                            <el-option v-for="item in crmGoodsPolicyEnumData.policyType" :label="item.label"
                                       :value="item.value" :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="政策标题" required prop="name">
                        <el-input v-model="formData.name" placeholder="政策标题"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="政策文号">
                        <el-input v-model="formData.spu.policyNumber" placeholder="政策文号"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="政策级别">
                        <el-select v-model="formData.spu.policyLevel" placeholder="政策级别" clearable>
                            <el-option v-for="item in crmGoodsPolicyEnumData.policyLevel" :label="item.label"
                                       :value="item.value" :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="行业类别">
                        <el-select v-model="formData.spu.policyIndustry" placeholder="行业类别" clearable>
                            <el-option v-for="item in crmGoodsPolicyEnumData.policyIndustry" :label="item.label"
                                       :value="item.value" :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发文部门">
                        <el-select v-model="formData.spu.issuingDepartment" placeholder="发文部门" clearable>
                            <el-option v-for="item in crmGoodsPolicyEnumData.issuingDepartment" :label="item.label"
                                       :value="item.value" :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="主题分类">
                        <el-select v-model="formData.spu.policyTopic" placeholder="主题分类" clearable>
                            <el-option v-for="item in crmGoodsPolicyEnumData.policyTopic" :label="item.label"
                                       :value="item.value" :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发文时间">
                        <el-date-picker style="width:100%" v-model="formData.spu.issuingTime" type="date"
                                        placeholder="发文时间" value-format="x" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="申报开始时间">
                        <el-date-picker style="width:100%" v-model="formData.spu.declareStartTime" type="date"
                                        placeholder="申报开始时间" value-format="x" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="申报结束时间">
                        <el-date-picker style="width:100%" v-model="formData.spu.declareEndTime" type="date"
                                        placeholder="申报结束时间" value-format="x" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="截止日期">
                        <el-date-picker style="width:100%" v-model="formData.spu.deadlineTime" type="date"
                                        placeholder="截止日期" value-format="x" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="奖励措施">
                        <el-input class="width-100" v-model="formData.spu.incentives" :rows="2" type="textarea"
                                  placeholder="奖励措施" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="申报条件">
                        <el-input class="width-100" v-model="formData.spu.declareCondition" :rows="2" type="textarea"
                                  placeholder="申报条件" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="链接" required prop="spu.links">
                        <el-input class="width-100" v-model="formData.spu.links" :rows="2" type="textarea"
                                  placeholder="请输入链接" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="政策内容" required prop="spu.policyContent">
                        <div class="width-100">
                            <div ref="quillEditor"></div>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
            <div class="display-flex justify-end">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit(formRef)">提交</el-button>
            </div>
        </el-form>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, onMounted, inject, reactive, onBeforeMount, nextTick, watch, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import crmService from '@/service/crmService'
import type { IGoodsPolicyItem } from '@/types/lead'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

const emits = defineEmits(['update:showDialog', 'refreshData'])
const close = () => {
    emits('update:showDialog', false)
    emits('refreshData')
}

const baseData: IGoodsPolicyItem = {
    'childGoodsType': '',
    'name': '',
    'spu': {
        'area': '',
        'declareCondition': '',
        'declareEndTime': 0,
        'declareStartTime': 0,
        'declareUrl': '',
        'description': '',
        'incentives': '',
        'interpretation': '',
        'issuingTime': 0,
        'policyContent': '',
        'policyIndustry': '',
        'issuingDepartment': '',
        'policyLevel': '',
        'policyNumber': '',
        'policyPoint': '',
        'policySource': '',
        'policyTopic': '',
        'deadlineTime': '',
        'links': '',
    },

}

const rules = reactive({
    name: [
        { required: true, message: '政策名称必填', trigger: 'blur' },
    ],
    childGoodsType: [
        { required: true, message: '政策类型必填', trigger: 'blur' },
    ],
    'spu.links': [
        { required: true, message: '链接必填', trigger: 'blur' },
    ],
    'spu.policyContent': [
        { required: true, message: '政策内容必填', trigger: 'blur' },
    ],
}
)


const props = defineProps({
    showDialog: {
        type: Boolean,
        default: false
    },
    policyItem: {
        type: Object,
        default: () => {
            return {}
        }
    }
})

interface enumData { [key: string]: { value: string, label: string }[] }

const crmGoodsPolicyEnumData: enumData = inject('crmGoodsPolicyEnumData') as enumData


let formData: Ref<IGoodsPolicyItem> = ref({} as IGoodsPolicyItem)


const dialogVisible: Ref<boolean> = ref(props.showDialog)

const formRef = ref()
const onSubmit = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((valid, fields) => {
        if (valid) {
            if (!formData.value.id) {
                crmService.crmGoodsPolicyAdd(formData.value).then(() => {
                    dialogVisible.value = false
                    close()
                    ElMessage({
                        type: 'success',
                        message: '保存成功'
                    })
                }).finally(() => {

                })
            } else {
                crmService.crmGoodsPolicyUpdate(formData.value).then(() => {
                    dialogVisible.value = false
                    close()
                    ElMessage({
                        type: 'success',
                        message: '保存成功'
                    })
                }).finally(() => {

                })
            }
        } else {
            console.log('error submit!', fields)
        }

    })

}
// Quill富文本编辑器相关
const quillEditor = ref<HTMLElement | null>(null)
const quillInstance = ref<Quill | null>(null)
const authAgreement = ref('')
// 初始化Quill编辑器
const initQuillEditor = () => {
    if (quillEditor.value) {
        quillInstance.value = new Quill(quillEditor.value, {
            theme: 'snow',
            placeholder: '请输入政策内容...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'align': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['link'],
                    ['clean']
                ]
            }
        })

        // 监听内容变化
        quillInstance.value.on('text-change', () => {
            if (quillInstance.value) {
                authAgreement.value = quillInstance.value.root.innerHTML
            }
        })

        // 设置初始内容
        if (authAgreement.value) {
            quillInstance.value.root.innerHTML = authAgreement.value
        }
    }
}
watch(authAgreement, () => {
    console.log(authAgreement)
    formData.value.spu.policyContent = authAgreement.value
})
onBeforeMount(() => {
    nextTick(() => {
        initQuillEditor()
    }) 
})
onMounted(() => {
    if (props.policyItem.id) {
        formData.value = props.policyItem as IGoodsPolicyItem
    } else {
        formData.value = JSON.parse(JSON.stringify(baseData))
    }
})
// 组件卸载时销毁编辑器实例
onUnmounted(() => {
    console.log('组件卸载',quillInstance.value)
    if (quillInstance.value) {
        quillInstance.value.root.innerHTML = ''  
        quillInstance.value = null
    }
})

</script>

<style lang='scss' scoped>
// Quill编辑器样式
:deep(.ql-toolbar) {
  border: 1px solid #dcdfe6;

  background-color: #fafafa;
  border-radius: 4px 4px 4px 4px;
}

:deep(.ql-container) {
  border: 1px solid #dcdfe6;
  font-size: 14px;
  border-radius: 0 0 4px 4px;
}

:deep(.ql-editor) {
  min-height: 200px;
  line-height: 1.6;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}
</style>