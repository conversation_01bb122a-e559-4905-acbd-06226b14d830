<template>
    <div class="display-flex">
        <div class="title-cion"></div>
        <div class="title-content">{{ props.title }}</div>
    </div>
</template>

<script lang="ts" setup>
const props = defineProps({
    title: {
        type: String,
        required: true,
    },
})
</script>

<style lang="scss" scoped>
.title-cion {
    width: 4px;
    border-radius: 338px;
    background: rgba(25, 102, 255, 1);
}
.title-content {
    margin-left: 6px;
    font-size: 16px;
    font-weight: 500;
    color: var(--main-black);
}
</style>
