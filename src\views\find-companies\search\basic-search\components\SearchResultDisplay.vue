<template>
    <div v-if="shouldShowSearchResult(props.row)" class="display-flex top-bottom-center">
        <div class="flex flex-row">
            <div class="flex flex-row">
                <el-icon size="16"><Search /></el-icon>
                <div class="l-padding-8">{{ searchTitle }}：</div>
            </div>
            <div class="flex flex-1">
                <div v-html="searchContent" class="display-inline"></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import commonData from '@/js/common-data'
import type { PropsRow, SearchKeyItem } from '@/types/company'
import { computed } from 'vue'

interface Props {
    row: PropsRow
    keyword?: string
    scope?: string
}

const props = defineProps<Props>()

const shouldShowSearchResult = (row: PropsRow): boolean => {
    if (!row) return false

    const target = commonData.searchTagTypes.find((item) => item.val === props.scope)
    if (!target) return false

    const { searchKeys, searchKey } = target || {}

    if (!searchKeys && searchKey) {
        return !!row[searchKey as string]
    }

    if (searchKeys && props.keyword) {
        return searchKeys.some((item) => !!row[item.key])
    }

    return false
}

const getSearchTitle = (row: PropsRow): string => {
    if (!row) return ''

    const target = commonData.searchTagTypes.find((item) => item.val === props.scope)

    if (!target) return ''

    const { searchKeys, searchKey } = target || {}

    if (!searchKeys && searchKey) {
        return target.searchTitle || ''
    }

    if (searchKeys) {
        return getSearchKeysVal(searchKeys, row, 'title')
    }

    return ''
}

const getSearchContent = (row: PropsRow): string => {
    if (!row) return ''

    const target = commonData.searchTagTypes.find((item) => item.val === props.scope)

    if (!target) return ''

    const { searchKeys, searchKey } = target || {}

    if (!searchKeys && searchKey) {
        return row[searchKey]
    }

    if (searchKeys) {
        return getSearchKeysVal(searchKeys, row, 'content')
    }

    return ''
}

const searchTitle = computed(() => getSearchTitle(props.row))
const searchContent = computed(() => getSearchContent(props.row))

const getSearchKeysVal = (searchKeys: SearchKeyItem[], row: PropsRow, key: 'title' | 'content'): string => {
    const foundItem = searchKeys.find((item) => row[item.key])
    return foundItem ? (key === 'title' ? foundItem.title : row[foundItem.key]) : ''
}
</script>

<style scoped></style>
