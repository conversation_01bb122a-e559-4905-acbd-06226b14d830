<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import indicatorService from '@/service/indicatorService'
import hightRiskPng from '@/assets/images/high-risk.png'
import middleRiskPng from '@/assets/images/middle-risk.png'
import lowRiskPng from '@/assets/images/low-risk.png'
import HighRisk from '@/assets/lottiefiles/hightRisk.json'
import MiddleRisk from '@/assets/lottiefiles/middleRisk.json'
import LowRisk from '@/assets/lottiefiles/lowRisk.json'
interface LottieAnimationData {
  v: string;
  fr: number;
  ip: number;
  op: number;
  w: number;
  h: number;
  nm: string;
  assets: []; // 可以根据需要细化字段
  layers: []; // 可以根据需要细化字段
}

const riskTrendAnalysisList = ref([
    {
        id: 1011,
        label: '风险级别',
        child: [
            {
                label: '高风险',
                level: 100,
                color: 'backcolor-red',
                val: 0,
            },
            {
                label: '中风险',
                level: 50,
                color: 'backcolor-yello',
                val: 0,
            },
            {
                label: '低风险',
                level: 1,
                color: 'backcolor-green',
                val: 0,
            },
        ],
    },
])
const middleRiskNum = ref([
    {
        id: '1320633166201856',
        label: '7日高风险',
        value: 0,
    },
    {
        id: '1320633392694272',
        label: '7日中风险',
        value: 0,
    },
    {
        id: '1320633610798080',
        label: '7日低风险',
        value: 0,
    },
    {
        id: '1320633837290496',
        label: '30日高风险',
        value: 0,
    },
    {
        id: '1320634038617088',
        label: '30日中风险',
        value: 0,
    },
    {
        id: '1320634239943680',
        label: '30日低风险',
        value: 0,
    },
])
const getData = () => {
    indicatorService
        .getDecomposeCompareIndicatorData({
            ids: '1011',
            socialCreditCode: 'xxx',
        })
        .then((res) => {
            console.log('风险事件趋势分析', res,res[Object.keys(res)[0]])
            enterData(res[Object.keys(res)[0]])
        })
}
const enterData = (data) => {
    riskTrendAnalysisList.value.forEach((item) => {
        data.forEach((d) => {
            if (item.id && item.id === d.id) {
                //总计新增
                filterRiskTrendTotal(item.child, d.currentTime)
            }
        })
    })
    typeTotalNum.value = riskTrendAnalysisList.value[0].child[0].val
}
const filterRiskTrendTotal = (child, currTimeList) => {
    
    currTimeList.forEach((curr) => {
        child.forEach((item) => {
            console.log('item',item,curr[0])
            
            if (item.label === curr[0]) {
                item.val = curr[1]
            }
        })
    })
}
const getChildrenData = () => {
    indicatorService
        .getCompareIndicatorData({
            ids: '1004,1005,1006,1007,1008,1009', //1004-7日高  1005-7日中  1006-7日低  1007-30日高  1008-30日中  1009-30日低
            socialCreditCode: 'xxx',
        })
        .then((res) => {
            // console.log('风险事件趋势分析1', res)
            middleRiskNum.value.forEach((item) => {
                if (res[item.id] !== undefined) {
                    item.value = parseInt(res[item.id], 10)
                }
            })
            type7Num.value = middleRiskNum.value[0].value 
            type30Num.value = middleRiskNum.value[3].value 
            // console.log('风险事件趋势分析2', middleRiskNum.value)
        })
}
const activeTab = ref('high')
const animationData = ref<LottieAnimationData>(HighRisk)
const typeStr = ref('高风险')
const typePng = ref(hightRiskPng)
const typeTotalNum = ref(0)
const type7Num = ref(0)
const type30Num = ref(0)
const tabList = [
    {
        label: '高风险',
        value: 'high',
    },
    {
        label: '中风险',
        value: 'middle',
    },
    {
        label: '低风险',
        value: 'low',
    },
]
onMounted(() => {
    getData()
    getChildrenData()
})
watch(() => activeTab.value, (newVal) => {
    if (newVal === 'high') {
        animationData.value = HighRisk
        typeStr.value = '高风险'
        typeTotalNum.value = riskTrendAnalysisList.value[0].child[0].val
        typePng.value = hightRiskPng
        type7Num.value = middleRiskNum.value[0].value 
        type30Num.value = middleRiskNum.value[3].value 
    } else if (newVal === 'middle') {
        animationData.value = MiddleRisk
        typeStr.value = '中风险'
        typeTotalNum.value = riskTrendAnalysisList.value[0].child[1].val
        typePng.value = middleRiskPng
        type7Num.value = middleRiskNum.value[1].value 
        type30Num.value = middleRiskNum.value[4].value 
    } else {
        animationData.value = LowRisk
        typeStr.value='低风险'
        typeTotalNum.value = riskTrendAnalysisList.value[0].child[2].val
        typePng.value = lowRiskPng
        type7Num.value = middleRiskNum.value[2].value 
        type30Num.value = middleRiskNum.value[5].value 
    }
})
</script>
<template>
    <div class="width-100 height-100 back-color-white border-radius-8 all-padding-16 display-flex flex-column">
        <div class="display-flex space-between top-bottom-center b-margin-24">
            <div class="font-16 color-black font-weight-500">风险事件趋势分析</div>
            <div
                class="w-180 display-flex space-between border-radius-4"
                style="background-color: #f5f5f5; overflow: hidden; border: 1px solid rgba(235, 235, 235, 1)"
            >
                <div
                    class="w-50 h-24 lh-24 text-center font-14 color-black pointer border-radius-4"
                    :class="item.value === activeTab ? 'active' : ''"
                    v-for="item in tabList"
                    :key="item.value"
                    @click="activeTab = item.value"
                >
                    {{ item.label }}
                </div>
            </div>
        </div>
        <div class="flex-1 b-margin-16 relative h-300">
            <LottieAnimation :animationData="animationData" :loop="true" :autoplay="true" :width="'100%'" :height="'100%'" />
            <div class="absolute" style="color: #fff; top: 50%; left: 50%; transform: translate(-50%, -50%)">
                <div class="font-16 font-weight-500 text-center">{{ typeStr }}(个)</div>
                <div class="font-32 font-weight-500 text-center">{{ typeTotalNum }}</div>
            </div>
        </div>
        <div class="h-80 display-flex gap-16 space-between">
            <div class="width-50 border-radius-4 display-flex center" style="background-color: #f7f7f9">
                <img class="w-33 h-33 r-margin-15" :src="typePng" :alt="typeStr" />
                <div>
                    <div class="b-margin-4 font-14 color-two-grey">{{ typeStr }}7日新增</div>
                    <div class="font-12 color-black">
                        <span class="font-24 font-weight-500 r-margin-2">{{ type7Num }}</span
                        >家
                    </div>
                </div>
            </div>
            <div class="width-50 border-radius-4 display-flex center" style="background-color: #f7f7f9">
                <img class="w-33 h-33 r-margin-15" :src="typePng" :alt="typeStr" />
                <div>
                    <div class="b-margin-4 font-14 color-two-grey">{{ typeStr }}30日新增</div>
                    <div class="font-12 color-black">
                        <span class="font-24 font-weight-500 r-margin-2">{{ type30Num }}</span
                        >家
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.active {
    color: var(--el-color-primary);
    border: 1px solid var(--el-color-primary);
    background-color: #fff;
}
</style>
