import type { ICommonResponse } from './axios'
import type { IAllRecord } from './record'

export interface IOrgTreeRequest extends IAllRecord {
    orgId?: string
    name?: string
}

export interface IOrgTreeResponse extends ICommonResponse {
    data: IOrgTreeItem[]
}

export interface IOrgTreeItem {
    id: string
    createUser: string
    managers: string[]
    name: string
    parentId: string
    parentIds: string[]
    tenantId: string
    sort: number
    children: IOrgTreeItem[]
}

export interface IUserListRequest extends IAllRecord {
    tenantId?: string
}

export interface IUserListItem {
    id: string
    username: string
    mobile: string
    mobileConfirmed: number
    password: string
    nickname: string
    status: number
    role: string[]
    roleName: []
    orgId: string[]
    defaultOrg: string
    childOrg: string[]
    tenantId: string
    unionId: string
    createUser: string
    createTime: number
    updateTime: number
    belong?: number
}

export interface IOrgAddRequest {
    managers: string[]
    name: string
    parentId: string
}

export interface IOrgAddResponse extends ICommonResponse {
    data: Record<string, unknown>
}

export interface IOrgEditRequest {
    managers: string[]
    name: string
    parentId: string
    orgId: string
}

export interface IOrgEditResponse extends ICommonResponse {
    data: Record<string, unknown>
}

export interface IOrgRemoveRequest extends IAllRecord {
    orgId: string
}

export interface IOrgSortRequest {
    id: string
    toId: string
}
