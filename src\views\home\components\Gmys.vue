<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import Icon from '@/components/common/Icon.vue'

const companyList = [
    {
        label: '规模以上企业',
        num: '',
        icon: 'icon-a-huaban8',
    },
    {
        label: '规模以上工业企业',
        num: '',
        icon: 'icon-a-huaban262',
    },
    {
        label: '规模以上批发/零售/住宿/餐饮业',
        num: '',
        icon: 'icon-a-huaban81',
    },
    {
        label: '规模以上建筑业/开发经营活动房地产/经营业',
        num: '',
        icon: 'icon-a-huaban2',
    },
    {
        label: '规模以上服务业',
        num: '',
        icon: 'icon-a-huaban32',
    },
]
</script>
<template>
    <div class="large-scale-company border-radius-4">
        <ModuleTitle style="margin-bottom: 24px" title="规模以上企业"></ModuleTitle>
        <div>
            <ul class="display-flex space-between">
                <li class="company-item" v-for="(item, index) in companyList" :key="index">
                    <div class="display-flex b-margin-16">
                        <div class="company-item-icon">
                            <Icon :icon="item.icon" color="#fff" />
                        </div>
                        <div class="company-item-num">{{ item.num || '-' }}</div>
                    </div>
                    <div class="company-item-label">
                        <el-tooltip class="box-item" effect="dark" :content="item.label" placement="bottom">
                            {{ item.label }}
                        </el-tooltip>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>
<style scoped lang="scss">
ul {
    padding: 0;
    margin: 0;
    li {
        list-style-type: none;
        padding-left: 0;
    }
}

.large-scale-company {
    flex: 1;
    padding: 16px;
    background-color: #fff;
    .company-item {
        box-sizing: border-box;
        width: 19%;
        // height: 92px;
        padding: 16px;
        background-color: #f8f9fd;
        border-radius: 4px;
        .company-item-icon {
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            border-radius: 24px;
            background-color: var(--main-blue-);
            margin-right: 8px;
        }
        .company-item-num {
            font-size: 18px;
            font-weight: 700;
            color: var(--main-black);
        }
        .company-item-label {
            font-size: 14px;
            font-weight: 400;
            color: var(--main-black);
            overflow: hidden;
            text-overflow: ellipsis; // 当内容超出容器时，显示为省略号（...）
            white-space: nowrap;
        }
    }
}
</style>
