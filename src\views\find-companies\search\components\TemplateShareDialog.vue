<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { ISearchGetTemplateItem } from '@/types/company'
import type { ISearchCopyTemplateParams } from '@/types/aic'
import type { IGetUserNames } from '@/types/user'
import systemService from '@/service/systemService'
import aicService from '@/service/aicService'
import { ElMessage } from 'element-plus'

const props = defineProps<{
    visible: boolean
    templateInfo: ISearchGetTemplateItem | null
}>()
const dialogVisible = ref(false)


const form = ref<ISearchCopyTemplateParams>(
    {
        templateId: '',
        toUsers:[]
    }
)
const userList=ref<IGetUserNames[]>()
const getUserList = async () => {
    try {
        let userRes = await systemService.userGetUserByScopeData('clue')
        const { data } = userRes
        userList.value = data
    } catch (error) {
        console.log('获取被分享人失败', error)
    }
}
watch(() => props.visible, async(newVal) => {
    dialogVisible.value = newVal
    if (dialogVisible.value) {
        console.log('props.templateInfo', props.templateInfo)
        await getUserList()
        if (props.templateInfo?.shareIds) {
            form.value.toUsers=props.templateInfo?.shareIds
        }
    }
}, {
    immediate: true
})
const emit = defineEmits(['update:visible'])
const dialogClose = () => {
    emit('update:visible',false)
}
const handleCancel = () => {
    dialogVisible.value = false
}
const handleSubmit = () => {
    console.log('提交')
    if (!props.templateInfo) return 
    if (!form.value.toUsers.length) {
        ElMessage.warning('请选择要分享的用户')
        return 
    }
    aicService.searchCopyTemplate(
        {
            templateId: props.templateInfo.id,
            toUsers: form.value.toUsers
        }
    ).then(() => {
        ElMessage({
            type: 'success',
            message: '分享成功',
        })
        handleCancel()
    }).catch(err => {
        console.log('分享模板失败', err)
    })

}
</script>
<template>
    <el-dialog v-model="dialogVisible" title="分享" width="500" @close="dialogClose()">
        <el-form :model="form" label-position="top">
            <el-form-item label="模板名称">
                <el-input :value="templateInfo?.name" autocomplete="off" />
            </el-form-item>
            <el-form-item label="分享范围">
                <el-select 
                    multiple
                    v-model="form.toUsers" 
                    placeholder="请选择分享范围"
                    :filterable="true"
                >
                    <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSubmit()"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss"></style>
