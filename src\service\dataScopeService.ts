import store from '@/store'

// const dataScope: Record<string, number>[] = [
//     {
//         clue: 4,
//     },
//     {
//         client: 4,
//     },
//     {
//         collect: 4,
//     },
//     {
//         outbound: 4,
//     },
//     {
//         user: 4,
//     },
// ]

const check = (key: string) => {
    const { auth } = store.state || {}
    const { dataScope } = auth || {}
    console.log('当前账号的数据权限', dataScope)
    if (dataScope.length) {
        return dataScope[0][key] || 0
    } else {
        return 0
    }
}

export default {
    // 线索
    leadScopeData(): number {
        return check('clue')
    },
    // 客户
    customerScopeData(): number {
        return check('client')
    },
}
