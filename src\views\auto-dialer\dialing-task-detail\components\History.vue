<script lang="ts" setup>
import { ref } from 'vue'
import NumberList from './number-list/NumberList.vue'
import CompanyList from './company-list/CompanyList.vue'
import type { IAutoDialerTaskDetailInfoItem } from '@/types/autoDialer'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
defineProps<{
    taskDetail: IAutoDialerTaskDetailInfoItem | null
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
// const formRef = ref<FormInstance>()
const activeName = ref('phone')

// const queryForm = reactive<IAutoDialerTaskInfoFilter>({
//     phone: '',
//     status: [],
//     name: '',
// })

// ====================== Methods ======================
// const reset = (formEl?: FormInstance) => {
//     formEl?.resetFields()
//     onSubmit()
// }

// const onSubmit = () => {
//     console.log('queryForm', queryForm)
//     // const params: IAutoDialerRequest = {
//     //     taskName: queryForm.taskName,
//     //     status: queryForm.status,
//     //     startTime: queryForm.date[0],
//     //     endTime: queryForm.date[1],
//     // }

//     // props.getData(params)
// }

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="back-color-white all-padding-16 border-radius-4">
        <el-tabs v-model="activeName">
            <el-tab-pane label="号码列表" name="phone">
                <NumberList :task-detail="taskDetail" />
            </el-tab-pane>
            <el-tab-pane label="企业列表" name="company">
                <CompanyList :task-detail="taskDetail" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<style lang="scss" scoped></style>
