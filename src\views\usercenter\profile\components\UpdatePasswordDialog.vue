<script lang="ts" setup>
import systemService from '@/service/systemService'
import type { RootState } from '@/types/store'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const emit = defineEmits(['update:visible'])

const props = defineProps<{
    visible: boolean
}>()

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const dialogTitle = computed(() => {
    return '修改密码'
})

// ====================== Refs & Reactive State ======================
const formRef = ref<FormInstance | null>(null)
const dialogVisible = ref(false)
const loading = ref(false)
const form = reactive<{
    userId: string
    newPassword: string
    oldPassword: string
    newPassword2: string
}>({
    userId: '',
    newPassword: '',
    oldPassword: '',
    newPassword2: '',
})
const rules = reactive<FormRules<typeof form>>({
    oldPassword: [{ required: true, message: '请输入组织名称', trigger: 'change' }],
    newPassword: [
        { required: true, message: '请输入新密码', trigger: 'change' },
        {
            min: 6,
            max: 16,
            message: '密码长度必须在6到16个字符之间',
            trigger: 'change',
        },
        {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/,
            message: '密码必须包含字母和数字',
            trigger: 'change',
        },
    ],
    newPassword2: [
        { required: true, message: '请再次输入新密码', trigger: 'change' },
        {
            min: 6,
            max: 16,
            message: '密码长度必须在6到16个字符之间',
            trigger: 'change',
        },
        {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/,
            message: '密码必须包含字母和数字',
            trigger: 'change',
        },
        {
            validator: (rule, value, callback) => {
                console.log(rule)
                if (value !== form.newPassword) {
                    callback(new Error('两次输入的密码不一致'))
                } else {
                    callback()
                }
            },
            trigger: 'change',
        },
    ],
})

// ====================== Methods ======================
const handleClose = () => {
    emit('update:visible', false)
}

const save = (formEle: FormInstance | null) => {
    if (loading.value) return
    if (!formEle) return
    loading.value = true
    formEle.validate((valid) => {
        if (valid) {
            systemService
                .userUpdateMinePassword({
                    oldPassword: form.oldPassword,
                    newPassword: form.newPassword,
                })
                .then((res) => {
                    const { errCode, errMsg } = res
                    if (errCode !== 0) {
                        ElMessage.error(errMsg || '修改密码失败')
                    } else {
                        ElMessage.success('修改成功')
                        ElMessageBox.alert('密码修改成功，请使用新密码重新登录！', '提示', {
                            confirmButtonText: '确认',
                            showClose: false,
                            closeOnClickModal: false,
                            closeOnPressEscape: false,
                            callback: () => {
                                store.dispatch('auth/logout')
                            },
                        })
                        handleClose()
                    }
                })
                .catch(() => {
                    ElMessage.error('修改失败，请稍后再试')
                })
                .finally(() => {
                    loading.value = false
                })
        } else {
            loading.value = false
        }
    })
}

// ====================== Watchers ======================
watch(
    () => props.visible,
    (val) => {
        dialogVisible.value = val
    }
)

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div class="update-password-dialog flex flex-column gap-16">
            <el-form ref="formRef" :rules="rules" :model="form" :hide-required-asterisk="true">
                <el-form-item label="旧密码" label-position="top" prop="oldPassword">
                    <el-input v-model="form.oldPassword" clearable placeholder="请输入旧密码" show-password />
                </el-form-item>
                <el-form-item label="新密码" label-position="top" prop="newPassword">
                    <el-input
                        v-model="form.newPassword"
                        clearable
                        placeholder="请输入 6-16 位英文与数字组合的新密码"
                        show-password
                    />
                </el-form-item>
                <el-form-item label="确认新密码" label-position="top" prop="newPassword2">
                    <el-input
                        v-model="form.newPassword2"
                        clearable
                        placeholder="请再次输入 6-16 位英文与数字组合的新密码"
                        show-password
                    />
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose" style="height: 40px">取消</el-button>
                <el-button type="primary" @click="save(formRef)" :loading="loading" style="height: 40px">
                    确认
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
.update-password-dialog :deep(.el-select__wrapper) {
    height: 45px;
    font-size: 14px;
}

.update-password-dialog :deep(.el-input__inner) {
    height: 45px;
    font-size: 14px;
}

.update-password-dialog :deep(.el-input__wrapper) {
    padding-top: 0;
    padding-bottom: 0;
}
</style>
