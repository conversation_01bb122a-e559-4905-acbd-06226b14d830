<template>
    <div class="relative" style="background-color: #fafcff;height: 100%;">
        <div id="main"></div>
        <div class="absolute zoom">
            <el-slider v-if="myChart" @change='zoomChange' v-model="zoom" :min='1' :max='5' :step='0.1' />
        </div>
    </div>
</template>

<script lang='ts' setup>

import type { CatagoryItem } from '@/types/aic'
import * as echarts from 'echarts'
import { ref, watch, onMounted, defineExpose } from 'vue'
import type { ECElementEvent } from 'echarts'

interface ColorItem {
    start: string;
    end: string;
    txt: string;
}

const colorList: ColorItem[] = [
    { start: '#4C92FC', end: '#DEE9FF', txt: '#303133' },
    { start: '#CFFFF2', end: '#ECF4F6', txt: '#54EBCF' },
    { start: '#FFF8D6', end: '#F5F3EB', txt: '#F2D450' },
    { start: '#FFD6D6', end: '#F5EDEB', txt: '#F57D56' },
    { start: '#D8D6FF', end: '#FAF2FF', txt: '#9857F5' },
    { start: '#FFD6EF', end: '#F5EBF3', txt: '#F556C5' },
    { start: '#FCA4A4', end: '#FFE8E8', txt: '#F55656' }
]

const props = defineProps<{
    chartData: CatagoryItem | null;
}>()

const emit = defineEmits(['nodeClick'])

const myChart = ref<echarts.ECharts>()
const nowLevel = ref(2)
const zoom = ref(1)
const nowClickNodes = ref<Record<string, number>>({})
const nowData = ref<CatagoryItem[] | null>(null)



const init = () => {
    const chartDom = document.getElementById('main')
    if (!chartDom) { return }

    console.log('chartDom', chartDom)

    myChart.value = echarts.init(chartDom)

    console.log('myChart.value', myChart.value)


    const option = {
        tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove'
        },
        series: [
            {
                type: 'tree',
                data: [],
                top: '10%',
                left: '10%',
                layout: 'radial',
                symbol: 'emptyCircle',
                symbolSize: 7,
                initialTreeDepth: nowLevel.value,
                zoom: zoom.value,
                expandAndCollapse: true,
                emphasis: {
                    focus: 'descendant'
                },
                roam: true,
                label: {
                    overflow: 'break',
                    minMargin: 10,
                    color: '#000',
                    formatter: (params: { data: CatagoryItem }) => {
                        return params.data?.shortName || params.data.name
                    }
                }
            }
        ]
    }

    myChart.value.on('click', (params: ECElementEvent) => {
        const data = params.data as CatagoryItem
        if (data.level) {
            nowLevel.value = data.level + 1
        }

        if (params.collapsed) {
            delete nowClickNodes.value[data.id]
        } else {
            nowClickNodes.value[data.id] = 1
        }
        emit('nodeClick', params)
    })

    myChart.value.on('treeroam', (params: unknown) => {
        const elementParams = params as { type: string; zoom: number }
        if (elementParams.type === 'zoom') {
            zoom.value = elementParams.zoom
        }
    })

    myChart.value.setOption(option)

    if (props.chartData) {
        setData([props.chartData])
    }
}

const setData = (data: CatagoryItem[]) => {
    if (!data || !myChart.value) { return }

    nowData.value = data
    myChart.value.setOption({
        series: [
            {
                data: data,
            }
        ]
    })
    setTimeout(() => {
        if (myChart.value) {
            myChart.value.resize()
        }
    }, 100)
}

const zoomChange = (val: number) => {
    if (!myChart.value) return

    myChart.value.setOption({
        series: [
            {
                zoom: val,
                center: ['10%', '10%']
            }
        ]
    })
}

const fillValues = (array: CatagoryItem[], values: ColorItem[], nextIdx = -1): CatagoryItem[] => {
    const idx = nextIdx + 1
    return array.map((item) => {
        item.level = idx
        if (nowClickNodes.value[item.id]) {
            item.collapsed = false
        }
        if (item.children && item.children.length > 0) {
            item.children = fillValues(item.children, values, idx)
        }
        return item
    })
}

// 暴露 myChart 给父组件
defineExpose({
    myChart
})

watch(
    () => props.chartData,
    (newVal) => {
        if (newVal) {
            const result = fillValues([newVal], colorList)
            setData(result)
        }
    }, { immediate: true })

onMounted(() => {
    init()
    console.log('onMounted1111111111',props.chartData)
})
</script>

<style lang='scss' scoped>
#main {
    height: 100%;
    width: 100%;
}

.zoom {
    right: 0;
    top: 0;
    width: 200px;
}
</style>
