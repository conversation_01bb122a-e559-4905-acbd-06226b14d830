import type { RootState, UserState } from '@/types/store'
import type { IUserAccountInfoResponse } from '@/types/user'
import { type Module } from 'vuex'

export const getUserDefaultState = (): UserState => ({
    account: null,
})

const userModule: Module<UserState, RootState> = {
    namespaced: true, // 启用命名空间
    state: () => getUserDefaultState(),
    getters: {
        account: (state: UserState) => state.account,
    },
    mutations: {
        SET_ACCOUNT(state: UserState, account: IUserAccountInfoResponse) {
            console.log('SET_ACCOUNT')
            console.log(account)
            state.account = account
        },
    },
    actions: {
        setAccountInfo({ commit }, account: IUserAccountInfoResponse) {
            commit('SET_ACCOUNT', account)
        },
    },
}

export default userModule
