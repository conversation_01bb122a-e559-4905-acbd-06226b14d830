import '@/lib/element-plus-defaults'
import { createApp } from 'vue'
import 'element-plus/dist/index.css'
import store from './store'
import App from './App.vue'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as echarts from 'echarts'
import router from './router' // 引入路由
import './styles/main.scss'
import commom from './js/index'
import lodash from 'lodash'
import moment from 'moment'
import Vue3Lottie from 'vue3-lottie'

import FormMakingV3 from '@/lib/form-making/form-making-v3.es.js'
import '@/lib/form-making/index.css'

const app = createApp(App)

app.use(router) // 使用路由
app.use(store) // 使用 Vuex/Pinia
app.use(Vue3Lottie, { name: 'LottieAnimation' })

app.use(FormMakingV3, {
    locale: 'zh-Hans',
    // i18n
})

app.use(ElementPlus, {
    locale: zhCn,
})

// 注册 Element Plus 图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

//自定义指令注册
import { copy } from './directives/copy'
app.directive('copy', copy)

// 在全局挂载 ECharts
app.config.globalProperties.$echarts = echarts
app.config.globalProperties.$commom = commom
app.config.globalProperties.$ssoUrl = import.meta.env.VITE_SSO_BASE_URL
app.config.globalProperties.$moment = moment
app.config.globalProperties.$lodash = lodash

app.mount('#app') // **只执行一次挂载**
