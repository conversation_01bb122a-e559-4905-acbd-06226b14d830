<script lang="tsx" setup>
import { useRouter } from 'vue-router'
import { Account, Back, MessageBox } from '../components/header'

import LOGO from '@/assets/images/header/logo.png'

const router = useRouter()
const toRouter = () => {
    router.push('/')
}
</script>

<template>
    <div class="header display-flex top-bottom-center border-bottom">
        <div class="flex top-bottom-center width-100 h-48 lr-padding-24">
            <div class="flex pointer top-bottom-center" @click="toRouter">
                <img :src="LOGO" alt="" />
            </div>
            <div class="flex flex-1 justify-flex-end top-bottom-center gap-16">
                <Back />
                <MessageBox />
                <div class="w-1 back-color-border h-15"></div>
                <Account />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
