<script lang="ts" setup>
import { ref, inject, getCurrentInstance, reactive, computed } from 'vue'
import aicService from '@/service/aicService'
import type { Ref } from 'vue'
import type { IGetJudgementsInfo } from '@/types/aic'

const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    },
})

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const dialogVisible = ref<boolean>(false)

const judgementsInfo = reactive<IGetJudgementsInfo>({})

const handleOpenDetail = () => {
    dialogVisible.value = true
    handleGetDetail()
}

const handleGetDetail = async () => {
    aicService
        .getGsGetDetail({
            docId: props.row.docId,
            socialCreditCode: socialCreditCode.value,
            moduleName: 'judgementDetail',
        })
        .then((res) => {
            const { data, errCode } = res
            if(errCode === 0){
                Object.assign(judgementsInfo || {}, data)
            }
        })
        .catch((err) => {
            console.log('err', err)
        })
}

const judgementsInfoTitle = computed(() => {
    const { title } = props.row
    return title.length > 20 ? title.slice(0, 20) + '...' : !title ? '-' : title
})

</script>

<template>
    <span class="pointer !color-blue" @click="handleOpenDetail()">{{ judgementsInfoTitle || '-' }}</span>
    <el-dialog v-model="dialogVisible" title="裁判文书详情" append-to-body style="height: 600px; overflow-y: auto">
        <div class="color-two-grey lr-padding-30 tb-padding-24">
            <div class="display-flex lr-padding-20 left-right-center font-16 font-bold text-center">
                {{ judgementsInfo.title }}
            </div>
            <div class="t-margin-30 lr-padding-20 font-14 border-bottom">
                <div class="flex justify-between top-bottom-center b-margin-16">
                    <div class="flex-1">案件类型：{{ judgementsInfo.caseType || '-' }}</div>
                    <div class="flex-1">案由类型：{{ judgementsInfo.caseReason || '-' }}</div>
                </div>
                <div class="flex justify-between top-bottom-center b-margin-16">
                    <div class="flex-1">案号：{{ judgementsInfo.caseNum || '-' }} </div>
                    <div class="flex-1">发布日期：{{ moment(judgementsInfo?.publishDate).format('YYYY-MM-DD') || '-' }}</div>
                </div>
            </div>
            <div class="lr-margin-20">
                <div class="flex-1 font-bold font-16 b-margin-12 t-margin-20 text-center">{{ judgementsInfo.headLine }}</div>
                <div class="flex-1 flex justify-end b-margin-12">{{ judgementsInfo.caseNum }}</div>
                <div class="content" v-html="judgementsInfo.content" />
                <div class="font-14 t-margin-20">
                    <div class="width-100 flex justify-end b-padding-4">
                        <span>审判长</span>
                        <span class="l-margin-30 judge-text">{{ judgementsInfo.chiefJudge }}</span></div>
                    <div class="width-100 flex justify-end b-padding-4">
                        <span>审判员</span>
                        <span class="l-margin-30 judge-text">{{ judgementsInfo.judge }}</span>
                    </div>
                    <div class="width-100 flex justify-end b-padding-4">
                        <span>审判员</span>
                        <span class="l-margin-30 judge-text">{{ judgementsInfo.judgeOther }}</span>
                    </div>
                    <div class="width-100 flex justify-end b-padding-4">{{ judgementsInfo.chineseJudgmentDate }}</div>
                    <div class="width-100 flex justify-end">
                        <span>书记员</span>
                        <span class="l-margin-30 judge-text">{{ judgementsInfo.courtClerk }}</span>
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<style scoped lang="scss">
.content {
    height: 200px;
    overflow-y: auto;
}
.judge-text { 
    width: 70px;
    text-align: right;
}
</style>
