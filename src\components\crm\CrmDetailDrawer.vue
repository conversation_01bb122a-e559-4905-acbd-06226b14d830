<template>
    <el-drawer
        :title="crmDetail.isCustomer === '1' ? '客户详情' : '线索详情'"
        v-model="drawerVisible"
        v-if="drawerVisible"
        size="80%"
        @close="handleClose"
    >
        <div class="display-flex flex-column height-100" v-if="crmDetail.id" style="width: 100%">
            <div
                class="menu-box display-flex top-bottom-center gap-10 lr-padding-10 tb-padding-8 b-margin-10 border-radius-4"
                style="width: 100%"
            >
                <el-button
                    type="primary"
                    v-if="crmDetail.isCustomer !== '1' && props.crmType !== 'lead-pool'"
                    @click="trunNewCustomer"
                >
                    <div>
                        <Icon icon="icon-a-yonghuzhongxin_bod" color="#fff" size="16px" class="r-margin-5" />转为新客户
                    </div>
                </el-button>
                <el-button
                    type="primary"
                    v-if="crmDetail.isCustomer !== '1' && props.crmType !== 'lead-pool'"
                    @click="trunAlreadyCustomer"
                >
                    <Icon icon="icon-a-yonghuzhongxin_bod" color="#fff" size="16px" class="r-margin-5" />转成已有客户
                </el-button>
                <el-button
                    type="primary"
                    v-else-if="props.crmType !== 'lead-pool'"
                    :disabled="crmDetail.isCustomer === '1' && crmDetail.status === 4"
                    @click="trunDealCustomer"
                >
                    <Icon icon="icon-a-yonghuzhongxin_bod" color="#fff" size="16px" class="r-margin-5" />转为成交客户
                </el-button>

                <TransferDialog
                    v-if="!props.isFromMinistration"
                    :from="transferFrom"
                    :showConfig="
                        crmDetail.isCustomer === '1'
                            ? ['customer2Person', 'customer2Pool']
                            : ['lead2Person', 'lead2Pool']
                    "
                    :selectedData="[crmDetail]"
                    @closeVisible="handleCloseTransferDialog"
                />

                <el-button v-if="!props.isFromMinistration" @click="handleEdit"
                    >编辑<el-icon class="l-margin-10">
                        <EditPen />
                    </el-icon>
                </el-button>
            </div>
            <div class="display-flex width-100 flex-1" v-if="crmDetail.id" style="width: 100%">
                <div class="flex-1 r-margin-16" style="width: 60%">
                    <!-- 线索左侧 -->
                    <el-scrollbar>
                        <div>
                            <div class="border border-radius-4 border-box all-padding-16">
                                <div class="display-flex space-between top-bottom-center gap-10">
                                    <div
                                        class="color-blue pointer display-flex flex-nowrap"
                                        @click="openCompanyDetail()"
                                        style="max-width: 65%"
                                    >
                                        <el-tooltip
                                            class="box-item"
                                            effect="dark"
                                            :content="crmDetail.companyName"
                                            placement="top"
                                        >
                                            <div class="text-ellipsis text-nowrap">{{ crmDetail.companyName }}</div>
                                        </el-tooltip>

                                        <div v-copy>
                                            <span v-show="false">{{ crmDetail.companyName }}</span>
                                            <Icon class="l-margin-5" icon="icon-fuzhi" :size="16" color="#999999" />
                                        </div>
                                    </div>
                                    <div>
                                        <el-select
                                            v-if="crmDetail.isCustomer !== '1'"
                                            v-model="crmDetail.status"
                                            :disabled="crmDetail.status === 4"
                                            placeholder="线索状态"
                                            style="width: 240px"
                                            @change="changeLeadStatus"
                                        >
                                            <el-option
                                                v-for="item in commonData.leadStatusEnum"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>
                                </div>
                                <div class="font-14 t-margin-10">
                                    <div>
                                        <span class="pointer">
                                            <span v-copy
                                                >{{ crmDetail.socialCreditCode }}
                                                <Icon
                                                    class="l-margin-5"
                                                    icon="icon-fuzhi"
                                                    :size="'16'"
                                                    color="#999999"
                                                />
                                            </span>
                                        </span>
                                    </div>
                                    <div class="t-margin-10">{{ crmDetail.customFields?.address || '-' }}</div>
                                </div>
                                <div class="t-margin-16">
                                    <!-- 标签组件 -->
                                    <TagPopover
                                        :tagInfos="tagList"
                                        :type="'detail'"
                                        :crmId="crmDetail.id"
                                        @refeshList="refreshCrmData"
                                    />
                                </div>
                                <div class="t-margin-16 font-14">
                                    <el-row>
                                        <el-col class="b-margin-16" :xs="12" :sm="12" :md="12" :lg="12" :xl="8"
                                            >负责人 : {{ crmDetail.user }}</el-col
                                        >
                                        <el-col class="b-margin-16" :xs="12" :sm="12" :md="12" :lg="12" :xl="8"
                                            >所属组织 :{{ crmDetail.departmentName }}
                                        </el-col>
                                        <el-col
                                            class="b-margin-16 display-flex top-bottom-center"
                                            :xs="12"
                                            :sm="12"
                                            :md="12"
                                            :lg="12"
                                            :xl="8"
                                            @click="openMinistrantInfo"
                                            v-if="
                                                crmDetail.isCustomer === '1' &&
                                                crmDetail.poolId === '' &&
                                                !props.isFromMinistration
                                            "
                                            >协作人
                                            <el-icon
                                                size="16px"
                                                :color="commonColor.mainBlue"
                                                class="l-margin-10 pointer"
                                            >
                                                <Edit /> </el-icon
                                            >: {{ crmDetail.ministrantInfos.map((i) => i.nickname).join(',') }}
                                        </el-col>
                                        <el-col class="b-margin-16" :xs="12" :sm="12" :md="12" :lg="12" :xl="8"
                                            >最新跟进时间 ：{{
                                                crmDetail.newFollowDate
                                                    ? moment(crmDetail.newFollowDate).format('YYYY-MM-DD HH:mm:ss')
                                                    : '-'
                                            }}</el-col
                                        >
                                        <el-col class="b-margin-16" :xs="12" :sm="12" :md="12" :lg="12" :xl="8"
                                            >下次跟进时间 ：
                                            {{
                                                crmDetail.nextFollowDate
                                                    ? moment(crmDetail.nextFollowDate).format('YYYY-MM-DD HH:mm:ss')
                                                    : '-'
                                            }}</el-col
                                        >
                                    </el-row>
                                </div>
                            </div>
                            <div class="t-margin-16">
                                <CollectInfoBar />
                            </div>
                            <div class="t-margin-16" v-if="crmDetail.isCustomer === '1'">
                                <CrmCustomerStatus @changeStatus="changeCustomerStatus" />
                            </div>
                            <div class="t-margin-16">
                                <CrmDetailIndicator />
                            </div>
                            <div class="t-margin-16 base-info" v-if="crmDetail.id">
                                <el-tabs v-model="tabActive" stretch>
                                    <el-tab-pane label="基本信息" name="crmInfo">
                                        <CrmBaseInfo v-if="tabActive === 'crmInfo'" />
                                    </el-tab-pane>
                                    <el-tab-pane label="产品匹配" name="productMatch">
                                        <CrmProductMatch
                                            v-if="tabActive === 'productMatch'"
                                            :companyId="crmDetail.companyId"
                                            @toReportList="toReportList"
                                        />
                                    </el-tab-pane>
                                    <el-tab-pane label="政策匹配" name="policyMatch">
                                        <CrmPolicyMatch
                                            v-if="tabActive === 'policyMatch'"
                                            :companyId="crmDetail.companyId"
                                            @toReportList="toReportList"
                                        />
                                    </el-tab-pane>
                                    <el-tab-pane label="税票数据" name="invoiceTaxData">
                                        <CrmInvoiceTaxData v-if="tabActive === 'invoiceTaxData'" />
                                    </el-tab-pane>
                                </el-tabs>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>
                <div
                    class="sales-box border-radius-4 relative border-box border lr-padding-16"
                    style="max-width: 450px"
                >
                    <CrmSalesDynamics v-if="crmDetail.id" ref="crmSalesDynamics" :crmType="props.crmType" />
                </div>
            </div>
        </div>
    </el-drawer>

    <el-dialog v-model="ministrantInfoDialogVisible" title="协作人" width="500">
        <el-select
            v-model="ministrantIds"
            value-key="id"
            multiple
            placeholder="请选择协作人"
            filterable
            :loading="ministrantLoading"
        >
            <el-option v-for="item in ministrantList" :key="item.id" :label="item.nickname" :value="item"></el-option>
        </el-select>
        <template #footer>
            <el-button @click="ministrantInfoDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="setMinistrantInfo"> 设置 </el-button>
        </template>
    </el-dialog>

    <AddLeadDialog
        v-model:visible="editCrmVisible"
        :leadInfo="crmDetail"
        :from="crmDetail.isCustomer === '0' ? 'lead' : 'customer'"
        @refreshData="handleRefreshData"
    >
    </AddLeadDialog>
    <Transfer2ExistingCustomer
        :visible="transfer2ExistingCustomerVisible"
        :checkedIds="[crmDetail.id]"
        @closeVisible="handleCloseTransfer2ExistingCustomer"
    />
</template>

<script lang="ts" setup>
import { ref, watch, defineEmits, defineProps, onMounted, computed, getCurrentInstance, provide } from 'vue'
import { useRouter } from 'vue-router'
import type { Ref } from 'vue'
import crmService from '@/service/crmService'
import customService from '@/service/customService'
import type { ILeadData } from '@/types/lead'
import type { IPageUserItem, IGetUserNames } from '@/types/user'
import { ElMessageBox, ElMessage } from 'element-plus'
import eventBus from '@/utils/eventBus'
import type { ITagInfo } from '@/types/lead'
import CollectInfoBar from './CollectInfoBar.vue'
import CrmDetailIndicator from './CrmDetailIndicator.vue'
import CrmBaseInfo from './CrmBaseInfo.vue'
import CrmInvoiceTaxData from './CrmInvoiceTaxData.vue'
import CrmProductMatch from '@/components/crm/CrmProductMatch.vue'
import CrmPolicyMatch from '@/components/crm/CrmPolicyMatch.vue'
import CrmSalesDynamics from './CrmSalesDynamics.vue'
import CrmCustomerStatus from './CrmCustomerStatus.vue'
import AddLeadDialog from '@/views/leads/components/AddLeadDialog.vue'
import TransferDialog from '@/views/leads/components/TransferDialog.vue'
import Transfer2ExistingCustomer from '@/views/leads/components/Transfer2ExistingCustomer.vue'
import TagPopover from '@/components/tag/TagPopover.vue'
import Icon from '@/components/common/Icon.vue'
import systemService from '@/service/systemService'

const instance = getCurrentInstance()
const commonData = instance?.appContext.config.globalProperties.$commom.commonData
const commonColor = instance?.appContext.config.globalProperties.$commom.color

const router = useRouter()
const props = defineProps({
    crmId: {
        type: String,
        required: true,
    },
    drawer: {
        type: Boolean,
        default: false,
    },
    crmType: {
        type: String,
        required: true,
    },
    isFromMinistration: {
        type: Boolean,
        default: false,
        required: false,
    },
})

const crmSalesDynamics = ref()

onMounted(() => {
    console.log('props.crmType', props.crmType)
    getCrmDetail()
})

const emit = defineEmits(['update:drawer'])

const drawerVisible: Ref<boolean> = ref(props.drawer)

watch(
    () => props.drawer,
    (newVal: boolean) => {
        drawerVisible.value = newVal
    }
)
const handleClose = () => {
    drawerVisible.value = false
    emit('update:drawer', false)
}

const crmDetail: Ref<ILeadData> = ref({} as ILeadData)
provide('crmDetail', crmDetail)
const tagList: Ref<ITagInfo[]> = ref([])
const getCrmDetail = () => {
    if (!props.crmId) {
        ElMessage({
            type: 'error',
            message: '缺少线索id',
        })
        return
    }
    crmService.crmDetail({ id: props.crmId }).then((res: ILeadData) => {
        console.log('线索详情', res)
        crmDetail.value = res
        tagList.value = res.tagInfos
    })
}

const openCompanyDetail = () => {
    // const routeUrl = router.resolve({ name: 'company-profile', params: { socialCreditCode: crmDetail.value.socialCreditCode } }).href
    // window.open(routeUrl, '_blank')
    if (window.self === window.top) {
        const routeUrl = router.resolve({
            name: 'company-profile',
            params: { socialCreditCode: crmDetail.value.socialCreditCode },
        }).href
        window.open(routeUrl, '_blank')
    } else {
        router.push({
            name: 'company-profile',
            params: {
                socialCreditCode: crmDetail.value.socialCreditCode,
            },
        })
    }
}

const tabActive: Ref<string> = ref('crmInfo')

const trunNewCustomer = () => {
    ElMessageBox.confirm('是否确认转为新客户？', '确认', {
        confirmButtonText: '确认',
        cancelButtonText: '再想想',
        type: 'warning',
    })
        .then(() => {
            crmService.crmTransfer({ ids: [crmDetail.value.id], transferType: 5 }).then(() => {
                ElMessage({
                    type: 'success',
                    message: '转新客户成功',
                })
                getCrmDetail()
                handleClose()
                refreshData()
                clearAllSelected()
            })
        })
        .catch(() => {})
}

const trunDealCustomer = () => {
    ElMessageBox.confirm('是否确认转为成交客户，成交后不可变更状态！', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            crmService
                .crmUpdateStatus({
                    leadId: crmDetail.value.id,
                    status: 4,
                })
                .then(() => {
                    ElMessage({
                        type: 'success',
                        message: '操作成功',
                    })
                    crmDetail.value.status = 4
                    refreshData()
                    handleClose()
                    clearAllSelected()
                })
        })
        .catch(() => {})
}

const changeLeadStatus = () => {
    crmService
        .crmUpdateStatus({
            leadId: crmDetail.value.id,
            status: crmDetail.value.status,
        })
        .then(() => {
            ElMessage({
                type: 'success',
                message: '操作成功',
            })
            refreshData()
            getCrmDetail()
        })
}

// const beforeCustomerStatus = () => {
//     crmService.crmUpdateStatus({
//         leadId: crmDetail.value.id,
//         status: crmDetail.value.status

//     }).then(() => {
//         ElMessage({
//             type: 'success',
//             message: '操作成功'
//         })
//         getCrmDetail()
//         refreshData()
//     })
// }
const changeCustomerStatus = (status: number) => {
    console.log(crmDetail.value)
    if (crmDetail.value.status === 4) {
        return
    }
    if (status === 4) {
        ElMessageBox.confirm('是否确认转为成交客户，成交后不可变更状态！', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                crmDetail.value.status = status
                crmService
                    .crmUpdateStatus({
                        leadId: crmDetail.value.id,
                        status: crmDetail.value.status,
                    })
                    .then(() => {
                        ElMessage({
                            type: 'success',
                            message: '操作成功',
                        })
                        getCrmDetail()
                        refreshData()
                    })
            })
            .catch(() => {})
    } else {
        crmDetail.value.status = status
        crmService
            .crmUpdateStatus({
                leadId: crmDetail.value.id,
                status: crmDetail.value.status,
            })
            .then(() => {
                ElMessage({
                    type: 'success',
                    message: '操作成功',
                })
                getCrmDetail()
                refreshData()
            })
    }
}

const moment = instance?.appContext.config.globalProperties.$moment

const ministrantInfoDialogVisible: Ref<boolean> = ref(false)

const ministrantList: Ref<IGetUserNames[]> = ref([])

const ministrantIds: Ref<IPageUserItem[]> = ref([])

const ministrantLoading: Ref<boolean> = ref(false)

const openMinistrantInfo = () => {
    ministrantIds.value = JSON.parse(JSON.stringify(crmDetail.value.ministrantInfos))
    systemService
        .userGetUserByScopeData('clue')
        .then((res) => {
            const { errCode, data, errMsg } = res || {}
            if (errCode === 0) {
                ministrantList.value = data
            } else {
                console.log('获取协作人失败', errMsg)
            }
        })
        .catch((err) => {
            console.log('获取协作人失败', err)
        })
    ministrantInfoDialogVisible.value = true
}

const setMinistrantInfo = () => {
    customService
        .customUpdateMinistrant({
            leadIds: [crmDetail.value.id],
            replaceUser: ministrantIds.value.map((i) => i.id),
            replaceType: 1,
        })
        .then(() => {
            ElMessage.success('设置成功')
            getCrmDetail()
            refreshData()
            ministrantInfoDialogVisible.value = false
        })
}

const transfer2ExistingCustomerVisible: Ref<boolean> = ref(false)
const trunAlreadyCustomer = () => {
    transfer2ExistingCustomerVisible.value = true
}

const transferFrom = computed(() => {
    let val = crmDetail.value

    if (val.poolId) {
        if (val.isCustomer === '1') {
            return 'customerPool'
        } else {
            return 'leadPool'
        }
    } else {
        if (val.isCustomer === '1') {
            return 'customerList'
        } else {
            return 'leadList'
        }
    }
})

const editCrmVisible: Ref<boolean> = ref(false)

const handleEdit = () => {
    editCrmVisible.value = true
}

const handleRefreshData = () => {
    getCrmDetail()
    eventBus.$emit('refreshCrmData')
}

const handleCloseTransferDialog = (val?: string) => {
    if (val !== 'cancel') {
        refreshData()
        getCrmDetail()
        handleClose()
        clearAllSelected()
    }
}

const handleCloseTransfer2ExistingCustomer = (val?: string) => {
    transfer2ExistingCustomerVisible.value = false
    if (val !== 'cancel') {
        refreshData()
        getCrmDetail()
        handleClose()
    }
}

const refreshCrmData = () => {
    eventBus.$emit('refreshCrmData')
}

const refreshData = () => {
    eventBus.$emit('refreshCrmData')
    eventBus.$emit('refreshActivities')
}

const clearAllSelected = () => {
    eventBus.$emit('clearAllSelected')
}

const toReportList = () => {
    tabActive.value = 'crmInfo'
}

onMounted(() => {
    // getCrmDetail()
})
</script>

<style scoped lang="scss">
.sales-box {
    overflow-y: hidden;
    width: 40%;
    flex-shrink: 0;
}

::v-deep .base-info .el-tabs__header {
    margin-bottom: 16px;
}

.menu-box {
    height: 53px;
}
</style>
