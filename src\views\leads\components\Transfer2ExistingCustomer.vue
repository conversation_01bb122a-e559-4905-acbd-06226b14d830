<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'

import type { ILeadData, ILeadTransfelParams } from '@/types/lead'
import type { FormInstance, FormRules } from 'element-plus'

import crmService from '@/service/crmService'
import customService from '@/service/customService'
import { ElMessage } from 'element-plus'

const props = defineProps<{
    visible: boolean
    checkedIds: string[]
}>()
const formRef = ref<FormInstance>()
const dialogVisible = ref(props.visible)
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
        getCustomerList()
    }
})
const form = reactive({
    customerId: '',
})
const rules = reactive<FormRules>({
    customerId: [{ required: true, message: '请选择客户信息', trigger: 'blur' }]
})
const emit = defineEmits(['closeVisible'])
const getCustomerList = async () => {
    let queryParams = {
        searchTag: 2,
        page: 1,
        pageSize: 100
    }
    let searchRes = await customService.customList(queryParams)
    if (searchRes.errCode === 0) {
        companyList.value = searchRes.data ? searchRes.data : []
    }
}
const companySearchLoading = ref(false)
const companyList = ref<ILeadData[]>([])
const searchCompany = (query: string) => {
    if (query) {
        companySearchLoading.value = true
        let queryParams = {
            searchTag: 2,
            page: 1,
            pageSize: 100,
            companyName: query
        }
        setTimeout(async () => {
            let searchRes = await customService.customList(queryParams)
            companyList.value = searchRes.data ? searchRes.data : []
            companySearchLoading.value = false
        }, 200)
    }
}
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((valid, fields) => {
        if (valid) {
            let obj: ILeadTransfelParams = {
                ids: props.checkedIds,
                transferType: 6,
                customerId: form.customerId
            }
            crmService.crmTransfer(obj).then(() => {
                ElMessage.success('转移成功')
                handleClose()
            })
        } else {
            console.log('form表单效验不通过', fields)
        }
    })
   
}
const handleCancel = () => {
    dialogVisible.value = false
}
const handleClose = (val?: string) => {
    form.customerId = ''
    emit('closeVisible',val)
}
</script>
<template>
    <el-dialog
        header-class="dialog-custom-header"
        v-model="dialogVisible"
        title="线索转换"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose('cancel')"
    >
        <el-form class="tenant-form" ref="formRef" :model="form" label-position="top" :rules="rules">
            <el-form-item label="客户信息" prop="customerId">
                <el-select
                    v-model.trim="form.customerId"
                    filterable
                    remote
                    placeholder="请输入客户信息"
                    :remote-method="searchCompany"
                    :loading="companySearchLoading"
                    clearable 
                >
                    <el-option
                        v-for="item in companyList"
                        :key="item.id"
                        :label="item.companyName"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleCancel()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss">
</style>
