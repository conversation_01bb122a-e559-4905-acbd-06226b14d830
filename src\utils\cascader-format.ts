import type { IAicConditionDataOptionItem } from '@/types/aic'

// 路径选择结果类型
interface PathItem {
    value: string
    label: string | null
}

// 级联选择数据类型：每个路径是一个字符串数组
type CascadeData = string[][]

// 引入配置数据
// import { config } from './conifg.js'

/**
 * 格式化级联选择数据，实现智能向上合并
 * @param cascadeData 级联选择的路径数组
 * @returns 格式化后的字符串数组，格式为"值,标签,深度"
 */
function cascaderFormat(cascadeData: CascadeData, config: IAicConditionDataOptionItem[]): string[] {
    if (!cascadeData || cascadeData.length === 0) return []

    // 获取节点的所有直接子节点
    function getAllChildren(pathArray: string[]): IAicConditionDataOptionItem[] {
        let currentNodes: IAicConditionDataOptionItem[] = config

        // 导航到目标节点
        for (let i = 0; i < pathArray.length; i++) {
            const targetValue = pathArray[i]
            const found = currentNodes.find((node) => node.value === targetValue)
            if (!found) return []
            if (i === pathArray.length - 1) {
                return found.children || []
            }
            currentNodes = found.children || []
        }
        return []
    }

    // 构建结果：对每条路径找到最合适的表示层级
    const resultMap = new Map<string, string>()

    cascadeData.forEach((path) => {
        let bestPath: string[] = path // 默认使用完整路径

        // 检查每一层，看是否可以向上合并
        for (let depth = path.length - 1; depth >= 1; depth--) {
            const parentPath = path.slice(0, depth)
            const children = getAllChildren(parentPath)

            if (children.length === 0) {
                // 没有子节点，向上检查
                continue
            }

            if (children.length === 1) {
                // 父节点只有一个子节点，可以合并到父节点
                bestPath = parentPath
                continue // 继续向上检查
            } else {
                // 父节点有多个子节点
                // 统计在这个路径下，选中了多少个子节点
                const selectedInThisPath = new Set<string>()
                cascadeData.forEach((p) => {
                    if (p.length > depth && p.slice(0, depth).join('/') === parentPath.join('/')) {
                        selectedInThisPath.add(p[depth])
                    }
                })

                if (selectedInThisPath.size === children.length) {
                    // 选中了所有子节点，可以合并到父节点
                    bestPath = parentPath
                    continue // 继续向上检查
                } else {
                    // 没有选中所有子节点，停止向上合并
                    break
                }
            }
        }

        const key = bestPath.join('/')
        if (!resultMap.has(key)) {
            const value = bestPath[bestPath.length - 1]
            const depth = bestPath.length
            const label = findLabelByPath(config, bestPath, bestPath.length - 1)
            resultMap.set(key, `${value},${label || `未知-${value}`},${depth}`)
        }
    })

    return Array.from(resultMap.values())
}

/**
 * 根据完整路径查找对应的label
 * @param config 配置数据
 * @param pathArray 路径数组
 * @param targetIndex 目标层级索引
 * @returns 对应的标签或null
 */
function findLabelByPath(
    config: IAicConditionDataOptionItem[],
    pathArray: string[],
    targetIndex: number
): string | null {
    function searchPath(nodes: IAicConditionDataOptionItem[], pathIndex: number): string | null {
        if (pathIndex >= pathArray.length) return null

        const targetValue = pathArray[pathIndex]

        for (const node of nodes) {
            if (node.value === targetValue) {
                if (pathIndex === targetIndex) {
                    return node.label // 找到目标层级的标签
                }
                if (node.children && pathIndex < pathArray.length - 1) {
                    const result = searchPath(node.children, pathIndex + 1)
                    if (result) return result
                }
            }
        }
        return null
    }

    return searchPath(config, 0)
}

/**
 * 根据value值在config中查找对应的label (兼容性函数，返回第一个匹配项)
 * @param config 配置数据
 * @param targetValue 目标值
 * @returns 对应的标签或null
 */
function findLabelByValue(config: IAicConditionDataOptionItem[], targetValue: string): string | null {
    function searchInNode(node: IAicConditionDataOptionItem): string | null {
        if (node.value === targetValue) {
            return node.label
        }

        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                const result = searchInNode(child)
                if (result) return result
            }
        }

        return null
    }

    for (const item of config) {
        const result = searchInNode(item)
        if (result) return result
    }

    return null
}

export { cascaderFormat, findLabelByPath, findLabelByValue }
export type { PathItem, CascadeData }
