<script lang="ts" setup>
import OrgProfile from '../org/OrgProfile.vue'
import type { RootState } from '@/types/store'
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore<RootState>()

const defaultOrg = computed(() => {
    const { account } = store.state.user || {}
    const { user, orgs } = account || {}
    const { defaultOrg } = user || {}

    if (!defaultOrg) return null
    if (!orgs || !Array.isArray(orgs) || orgs.length === 0) return null

    const target = orgs.find((org) => org.id === defaultOrg)

    if (!target) return null

    return target
})

const props = defineProps<{
    showSwitcher: () => void
}>()
</script>

<template>
    <div class="split-line" v-if="defaultOrg"></div>
    <div class="flex flex-column all-padding-16 gap-12" v-if="defaultOrg">
        <div class="font-14 color-three-grey">组织</div>
        <OrgProfile
            :name="defaultOrg?.name || ''"
            :id="defaultOrg?.id || ''"
            :tags="[]"
            :show-text="true"
            :click="props.showSwitcher"
        />
    </div>
</template>

<style lang="scss" scoped></style>
