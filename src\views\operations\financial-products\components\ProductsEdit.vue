<template>
    <el-dialog v-model="dialogVisible" :title="formData.id ? '编辑' : '新增'" width="800" @close="close">
        <el-form ref="formRef" :model="formData" :label-position="'top'" :rules="rules">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="金融产品类型" required prop="childGoodsType">
                        <el-select v-model="formData.childGoodsType" placeholder="金融产品类型" clearable
                                   :empty-values="[0, '']">
                            <el-option v-for="item in crmGoodsFinanceEnumData.financialProduct" :label="item.label"
                                       :value="item.value" :key="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="金融产品名称" required prop="name">
                        <el-input v-model="formData.name" placeholder="金融产品名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="最高授信额度" required prop="spu.moneyLimits">
                        <el-input v-model="formData.spu.moneyLimits" placeholder="最高授信额度"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="最低年化利率" required prop="spu.rateDown">
                        <el-input v-model="formData.spu.rateDown" placeholder="最低年化利率"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="最高年化利率" required prop="spu.rateUpper">
                        <el-input v-model="formData.spu.rateUpper" placeholder="最高年化利率"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="最高返佣" required prop="spu.maxProfit">
                        <el-input v-model="formData.spu.maxProfit" placeholder="最高返佣"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="还款方式" required prop="spu.refundWay">
                        <el-input v-model="formData.spu.refundWay" placeholder="还款方式"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="还款周期" required prop="spu.loanCycle">
                        <el-input v-model="formData.spu.loanCycle" placeholder="还款周期"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="返佣说明">
                        <el-input class="width-100" v-model="formData.spu.rebateExplanation" :rows="2" type="textarea"
                                  placeholder="返佣说明" />
                    </el-form-item>
                </el-col>

            </el-row>
            <div class="display-flex justify-end">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit(formRef)">提交</el-button>
            </div>


        </el-form>

    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, onMounted, defineProps, defineEmits, inject, reactive } from 'vue'
import type { Ref } from 'vue'
import crmService from '@/service/crmService'
import type { IGoodsFinanceItem } from '@/types/lead'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

const emits = defineEmits(['update:showDialog', 'refreshData'])
const close = () => {
    emits('update:showDialog', false)
    emits('refreshData')
}

const rules = reactive({
    name: [
        { required: true, message: '金融产品名称必填', trigger: 'blur' },
    ],
    childGoodsType: [
        { required: true, message: '金融产品类型必填', trigger: 'blur' },
    ],
    'spu.moneyLimits': [
        { required: true, message: '最高授信额度必填', trigger: 'blur' },
    ],
    'spu.rateDown': [
        { required: true, message: '最低年化利率必填', trigger: 'blur' },
    ],
    'spu.rateUpper': [
        { required: true, message: '最低年化利率必填', trigger: 'blur' },
    ],
    'spu.maxProfit': [
        { required: true, message: '最高返佣必填', trigger: 'blur' },
    ],
    'spu.refundWay': [
        { required: true, message: '还款方式必填', trigger: 'blur' },
    ],
    'spu.loanCycle': [
        { required: true, message: '还款周期必填', trigger: 'blur' },
    ]

}
)

const formRef = ref()

const baseData: IGoodsFinanceItem = {
    'childGoodsType': '',
    'id': '',
    'name': '',
    spu: {
        'loanCycle': '',
        'maxProfit': '',
        'moneyLimits': '',
        'notSellIndustry': [],
        'notSellIndustryNames': [],
        'rateDown': 0,
        'rateUpper': 0,
        'rebateExplanation': '',
        'refundWay': ''
    }

}


const props = defineProps({
    showDialog: {
        type: Boolean,
        default: false
    },
    policyItem: {
        type: Object,
        default: () => {
            return {}
        }
    }
})

interface enumData { [key: string]: { value: string, label: string }[] }

const crmGoodsFinanceEnumData: enumData = inject('crmGoodsFinanceEnumData') as enumData


let formData: Ref<IGoodsFinanceItem> = ref({} as IGoodsFinanceItem)


const dialogVisible: Ref<boolean> = ref(props.showDialog)

const onSubmit = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((valid, fields) => {
        if (valid) {
            if (!formData.value.id) {
                crmService.crmGoodsFinanceAdd(formData.value).then(() => {
                    dialogVisible.value = false
                    close()
                    ElMessage({
                        type: 'success',
                        message: '保存成功'
                    })
                }).finally(() => {

                })
            } else {
                crmService.crmGoodsFinanceUpdate(formData.value).then(() => {
                    dialogVisible.value = false
                    close()
                    ElMessage({
                        type: 'success',
                        message: '保存成功'
                    })
                }).finally(() => {

                })
            }
        } else {
            console.log('error submit!', fields)
        }

    })


}

onMounted(() => {
    if (props.policyItem.id) {
        formData.value = props.policyItem as IGoodsFinanceItem
    } else {
        formData.value = JSON.parse(JSON.stringify(baseData))
    }

})

</script>

<style lang='scss' scoped></style>