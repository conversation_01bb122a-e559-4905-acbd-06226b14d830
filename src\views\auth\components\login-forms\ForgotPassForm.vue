<template>
    <div class="password-form">
        <div class="title">找回密码</div>
        <div class="steps"></div>
        <div class="form">
            <el-form :rules="rules" :model="form" ref="formRef">
                <el-form-item label="" class="password-form-item" prop="phone">
                    <el-input
                        v-model="form.phone"
                        placeholder="输入手机号码"
                        class="input"
                        clearable
                        autocomplete="off"
                    />
                </el-form-item>
                <el-row style="width: 100%">
                    <el-col :span="15">
                        <el-form-item label="" class="password-form-item" prop="code">
                            <el-input
                                v-model="form.code"
                                placeholder="短信验证码"
                                class="input"
                                input-style="width : 60%"
                                clearable
                                autocomplete="off"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item label="" style="margin-bottom: 0px; margin-left: 12px">
                            <el-button
                                type="primary"
                                @click="getCode"
                                :disabled="countdown > 0"
                                :loading="querying"
                                class="code-btn"
                                clearable
                                autocomplete="off"
                            >
                                {{ countdown > 0 ? `${countdown} 秒` : '获取验证码' }}</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="" class="password-form-item" prop="password">
                    <el-input
                        v-model="form.password"
                        placeholder="输入新密码"
                        class="input"
                        show-password
                        clearable
                        autocomplete="off"
                    />
                </el-form-item>
                <el-form-item label="" class="password-form-item" prop="repeat">
                    <el-input
                        v-model="form.repeat"
                        placeholder="确认密码"
                        class="input"
                        show-password
                        clearable
                        autocomplete="off"
                    />
                </el-form-item>
                <div class="help">密码必须为6-16位英文或数字</div>
            </el-form>
        </div>
        <div class="btns">
            <div>
                <el-button round class="submit-btn" @click="submitForm(formRef)" :loading="loading">
                    <div>修改密码</div>
                </el-button>
            </div>
            <div>
                <el-button class="login-btn" @click="toLogin()"> 账户登录 </el-button>
            </div>
        </div>
        <div class="tips">
            <div class="label">温馨提示：</div>
            <div class="desc">若无账号请联系后台管理员进行开通</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import authService from '@/service/authService'
import systemService from '@/service/systemService'
import { passwordValidate, phoneValidate } from '@/utils/validate'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { onUnmounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const formRef = ref<FormInstance>()
const form = reactive({
    phone: '',
    code: '',
    password: '',
    repeat: '',
})
const loading = ref(false) // 控制按钮的 loading 状态
const querying = ref(false) // 控制按钮的 loading 状态
const countdown = ref(0) // 记录倒计时秒数
const timer = ref<ReturnType<typeof setInterval> | null>(null)

const rules = reactive<FormRules<typeof form>>({
    password: [
        { required: true, message: '请输入登录密码', trigger: 'change' },
        {
            validator: (rule, value, callback) => {
                console.log(rule)
                if (!value || value === '********') {
                    // 如果为空，不做其他校验
                    callback()
                } else {
                    if (value.length < 6 || value.length > 16) {
                        callback(new Error('密码长度必须在6到16个字符之间'))
                    } else if (!/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(value)) {
                        callback(new Error('密码必须包含字母和数字'))
                    } else {
                        callback()
                    }
                }
            },
            trigger: 'change',
        },
    ],
    phone: [
        { required: true, message: '请输入手机号', trigger: 'change' },
        {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
        },
    ],
    code: [
        {
            required: true,
            message: '请输入验证码',
            trigger: 'change',
        },
    ],
    repeat: [
        { required: true, message: '请再次输入新密码', trigger: 'change' },
        {
            min: 6,
            max: 16,
            message: '密码长度必须在6到16个字符之间',
            trigger: 'change',
        },
        {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/,
            message: '密码必须包含字母和数字',
            trigger: 'change',
        },
        {
            validator: (rule, value, callback) => {
                console.log(rule)
                if (value !== form.password) {
                    callback(new Error('两次输入的密码不一致'))
                } else {
                    callback()
                }
            },
            trigger: 'change',
        },
    ],
})

const validate = () => {
    let flag = true
    let message = ''
    const { phone, password, code, repeat } = form || {}

    if (!passwordValidate(password)) {
        message = '密码长度需在6-12位之间，同时包含字母和数字'
    }

    if (repeat !== password) {
        message = '两次密码输入不一致'
    }

    if (!repeat) {
        message = '确认密码不能为空'
    }

    if (!password) {
        message = '密码不能为空'
    }

    if (!code) {
        message = '短信验证码不能为空'
    }

    if (!phone) {
        message = '手机号码不能为空'
    }

    if (!phoneValidate(phone.trim())) {
        message = '手机号码格式不正确'
    }

    if (message) {
        ElMessage.error(message)
        return
    }

    return flag
}

const submitForm = (formEle?: FormInstance) => {
    if (!formEle) return

    formEle.validate((valid) => {
        if (valid) {
            if (!validate()) return
            const { phone, password, code, repeat } = form || {}

            if (loading.value) return

            loading.value = true
            systemService
                .userUpdatePasswordByCode({
                    mobile: phone.trim(),
                    code: code.trim(),
                    newPassword: password,
                    newPassword1: repeat,
                })
                .then(() => {
                    loading.value = false
                    ElMessage.success('密码修改成功，请使用新密码登录')
                    router.push('/login')
                })
                .catch((err) => {
                    loading.value = false
                    console.log(err)
                })
        } else {
            loading.value = false
        }
    })
}

const getCode = async () => {
    const { phone } = form || {}

    if (!phone) {
        ElMessage.error('手机号码不能为空')
        return
    }

    if (!phoneValidate(phone.trim())) {
        ElMessage.error('手机号码格式不正确')
        return
    }

    if (countdown.value > 0 || querying.value) return // 防止重复点击
    querying.value = true
    console.log('请求验证码...')
    authService
        .oauthGetForgetPasswordCode({
            phone,
        })
        .then(() => {
            console.log('验证码发送成功')
            // 开始倒计时
            countdown.value = 60
            querying.value = false // 取消 loading
            timer.value = setInterval(() => {
                countdown.value--
                if (countdown.value <= 0 && timer.value) {
                    clearInterval(timer.value)
                    timer.value = null
                }
            }, 1000)
        })
        .catch((err) => {
            console.log(err)
            querying.value = false
        })
}

const toLogin = () => {
    router.push('login')
}

// 组件卸载时清除定时器
onUnmounted(() => {
    if (timer.value) clearInterval(timer.value)
})
</script>

<style lang="scss" scoped>
@use './style.scss';
</style>
