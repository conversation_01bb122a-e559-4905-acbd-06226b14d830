<template>
    <div>
        <div ref="searchContentRef" v-if="requestId">
            <SearchBox :searchOptionKey="'INVOICE_LIST_SEARCH_OPTIONS'" @updateSearchParams="filterQuery"></SearchBox>
        </div>
        <InvoiceListTable :filter-params="filterParams" :requestId="requestId" v-if="requestId" />
    </div>
</template>

<script lang="ts" setup>
import SearchBox from '@/components/common/SearchBox.vue'
import InvoiceListTable from './InvoiceListTable.vue'
import { ref, watch } from 'vue'
import type { ICollectInvoiceFilter } from '@/types/invoice'
import moment from 'moment'

const props = defineProps<{
    requestId: string
}>()

const filterParams = ref<ICollectInvoiceFilter>()

// ====================== Methods ======================

const filterQuery = (params: ICollectInvoiceFilter) => {
    const { invoiceDateList } = params || {}
    if (invoiceDateList && Array.isArray(invoiceDateList) && invoiceDateList.length === 2) {
        invoiceDateList[0] = moment(invoiceDateList[0]).format('YYYY-M-D')
        invoiceDateList[1] = moment(invoiceDateList[1]).format('YYYY-M-D')
    }
    filterParams.value = params
}

// ====================== Watchers ======================
watch(
    () => props.requestId,
    (val) => {
        if (!val) {
            filterParams.value = undefined
        }
    }
)
</script>

<style lang="scss" scoped></style>
