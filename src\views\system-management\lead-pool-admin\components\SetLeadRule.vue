<template>
    <div>
        <el-dialog 
            title="规则设置" 
            width="650px" 
            v-model="dialogVisible"
            @close="handleClose"
        >
            <div>
                <!-- 规则设置内容 -->
                <div class="display-flex top-bottom-center b-margin-10">
                    <div class="r-margin-20 font-16 color-black">是否开启自动回收</div>
                    <el-switch
                        v-model="isRecyclevalue"
                        active-color="#13ce66"
                        inactive-color="#ff4949">
                    </el-switch>
                </div >
                <div v-if="isRecyclevalue">
                    <el-form-item label="回收规则" :label-position="'left'">
                        <view class="l-margin-20">
                            <view v-for="(rule, index) in form.reback_rules" :key="index">
                                <view class="display-flex top-bottom-center gap-12" :class=" index == 0 ? '' : 't-margin-6'">
                                    <el-input-number v-model="rule.dayNum" :min="2" :max="365" label="时间" :step="1" style="width: 200px"></el-input-number>天
                                    <!-- 根据长度控制选择的规则类型 -->
                                    <el-select v-if="form.reback_rules.length === 1" v-model="rule.type" placeholder="请选择">
                                        <el-option label="未产生跟进行为" value="1"></el-option>
                                        <el-option label="未转客户" value="2"></el-option>
                                    </el-select>
                                    <el-select v-if="form.reback_rules.length === 2" v-model="rule.type" placeholder="请选择">
                                        <el-option v-if="rule.type !== '2'" label="未产生跟进行为" value="1"></el-option>
                                        <el-option v-if="rule.type !== '1'" label="未转客户" value="2"></el-option>
                                    </el-select>
                                    <el-icon v-if="form.reback_rules.length>1" @click="delRules(index)" size="20" ><Remove /></el-icon>
                                    <el-icon v-if="form.reback_rules.length<2" @click="addRules(form.reback_rules[0])" size="20"><CirclePlus /></el-icon>
                                </view>
                                <view v-if="index !== form.reback_rules.length-1" class="relative">
                                    <span class="absolute" style="left: -20px; top: -14px;">或</span></view>
                            </view>
                        </view>
                    </el-form-item>
                    <el-form-item label="回收规则优先级" :label-position="'left'" style="margin-bottom: 0px;">
                        <el-input-number v-model="form.sort" :min="1" :max="99" label="回收规则优先级" :step="1"></el-input-number>
                    </el-form-item>

                </div>
                
                <div class="display-flex justify-flex-end all-padding-10 t-margin-20">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="confirm" :loading="loading" style="background-color: #1966FF;">确 定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { SearchPoolListResponseItem,SetRuleParams,ReBackRules } from '@/types/lead'
import crmService from '@/service/crmService'

const loading = ref(false)
const updateParams = ref<SetRuleParams>({
    poolId: '',
    type: 1,
})

const props = defineProps<{
    visible: boolean
    setRuleData:SearchPoolListResponseItem
    poolname: string
}>()

const isRecyclevalue = ref(false)

const form = ref({
    sort:1 ,
    reback_rules: [
        {
            dayNum: 2,
            type: '1'
        }
    ], 
})

const emit = defineEmits(['update:visible','refreshData'])

const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const handleClose = () => {
    dialogVisible.value = false
}

const cancel = () => {
    dialogVisible.value = false
}

const addRules = (row:ReBackRules) => {
    console.log('addRules', row)
    if(row.type === '1'){
        form.value.reback_rules.push({
            dayNum: 2,
            type: '2'
        })
    }else if(row.type === '2'){
        form.value.reback_rules.push({
            dayNum: 2,
            type: '1'
        })
    }
}

const delRules = (index: number) => {
    if (form.value.reback_rules.length > 1) {
        form.value.reback_rules.splice(index, 1)
    }
}

const confirm = () => {
    loading.value = true
    updateParams.value.poolId = props.setRuleData.id
    if( !isRecyclevalue.value ){
        updateParams.value.openReback = 0
        updateParams.value.rebackRules = []
        crmService.crmManageUpdate(updateParams.value)
            .then((res) => {
                if (res.success) {
                    ElMessage.success('规则设置成功')
                }
            }).finally(() => { 
                loading.value = false  
                dialogVisible.value = false
                emit('refreshData') 
            })
    }else if(props.setRuleData){
        const reBackRules = []
        updateParams.value.sort = form.value.sort
        updateParams.value.openReback = 1
        for(let i = 0; i < form.value.reback_rules.length;i++){
            reBackRules.push({
                dayNum: form.value.reback_rules[i].dayNum,
                type: form.value.reback_rules[i].type
            })
        }
        updateParams.value.rebackRules = reBackRules
        crmService.crmManageUpdate(updateParams.value)
            .then((res) => {
                if (res.success) {
                    ElMessage.success('规则设置成功')
                }else{
                    ElMessage.error(`${res.errMsg}`)
                }
            }).finally(() => {
                loading.value = false
                dialogVisible.value = false
                emit('refreshData')
            })
    }
}

watch(
    () => props.setRuleData,
    (newVal) => {
        if (newVal) {
            if(newVal.openReback === 1){
                isRecyclevalue.value = true
                form.value.reback_rules = newVal.rebackRules || []
                form.value.sort = newVal.sort || 1
                // console.log('setRuleData changed to:', newVal)
            }else{
                isRecyclevalue.value = false
                form.value.reback_rules = [        
                    {
                        dayNum: 2,
                        type: '1'
                    }]
                form.value.sort = 1
            }  
        }
    },
    { immediate: true }
)

</script>

<style scoped>

</style>
