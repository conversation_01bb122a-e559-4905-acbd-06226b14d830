import { ref, type Ref } from 'vue'

export function useGridSelect(groups: Ref<number[]>) {
    const isMouseDown = ref(false)
    const toggleMode = ref(true)
    const startIndex = ref<number | null>(null)
    const path = ref(new Set<number>())

    const add = (i: number) => {
        if (!groups.value.includes(i)) groups.value.push(i)
    }

    const remove = (i: number) => {
        groups.value = groups.value.filter((item) => item !== i)
    }

    const onMouseDown = (index: number) => {
        isMouseDown.value = true
        startIndex.value = index
        toggleMode.value = !groups.value.includes(index)
        path.value = new Set([index])
        if (toggleMode.value) {
            add(index)
        } else {
            remove(index)
        }
    }

    const onMouseEnter = (index: number) => {
        if (!isMouseDown.value || startIndex.value === null) return

        const newPath = new Set<number>()
        const start = Math.min(startIndex.value, index)
        const end = Math.max(startIndex.value, index)

        for (let i = start; i <= end; i++) {
            newPath.add(i)
        }

        // 撤销旧的不在新 path 的
        for (const i of path.value) {
            if (!newPath.has(i)) {
                if (toggleMode.value) {
                    remove(i)
                } else {
                    add(i)
                }
            }
        }

        // 添加新 path 中的
        for (const i of newPath) {
            if (!path.value.has(i)) {
                if (toggleMode.value) {
                    add(i)
                } else {
                    remove(i)
                }
            }
        }

        path.value = newPath
    }

    const onMouseUp = () => {
        isMouseDown.value = false
        path.value.clear()
        startIndex.value = null
    }

    return {
        onMouseDown,
        onMouseEnter,
        onMouseUp,
        isMouseDown,
    }
}
