<script lang="ts" setup>
import type { RootState } from '@/types/store'
import { History, TaskInfo } from './components'
import { useStore } from 'vuex'
import { computed, onMounted, ref } from 'vue'
import OpenPage from '@/components/auto-dialer/open-page/OpenPage.vue'
import { useRoute } from 'vue-router'
import outboundService from '@/service/outboundService'
import type { IAutoDialerTaskDetailInfoItem } from '@/types/autoDialer'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()
const isOpen = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    const { openAiPhone } = tenant || {}
    return openAiPhone
})
const route = useRoute()
const taskCode = route.query.taskCode
const taskDetail = ref<IAutoDialerTaskDetailInfoItem | null>(null)

// ====================== Refs & Reactive State ======================

// ====================== Methods ======================
const getTaskDetail = () => {
    if (!taskCode) return
    if (typeof taskCode !== 'string') return
    outboundService.taskDetail({ taskCode: taskCode }).then((res) => {
        taskDetail.value = res
    })
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getTaskDetail()
})
</script>

<template>
    <div class="flex flex-column gap-16 height-100">
        <TaskInfo v-if="isOpen" :task-detail="taskDetail" />
        <History v-if="isOpen" :task-detail="taskDetail" />
        <OpenPage v-if="!isOpen" />
    </div>
</template>

<style lang="scss" scoped></style>
