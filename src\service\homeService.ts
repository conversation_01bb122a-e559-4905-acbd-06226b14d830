import http from '@/axios'
import type { GetHomeDataParams, GetHomeDataResponse, GetCustomerDataResponse, ISearchCustomerParams } from '@/types/home'

export default {
    searchHomeData(data:GetHomeDataParams): Promise<GetHomeDataResponse> {
        return http.post(`/api/zhenqi-aic/home/<USER>
    },

    searchCustomerData(data:ISearchCustomerParams ):Promise<GetCustomerDataResponse[]> {
        return http.get(`/api/zhenqi-aic/condition/get-data`, {
            params: data
        })
    },

}
