// 定义一个接口作为类型约束
export interface IAIChatBox {
    initScript: () => void
    setUser: (key: string) => void
    showChat: () => void
    sendMessage: (message: IMessage) => void
}

interface IMessage {
    message: string
    hide: boolean
    inputs: {
        companyName: string
        socialCreditCode: string
        tenantId: string
    }
}

interface ISdk {
    getConfig: () => void
    showDrawerChat: () => void
    sendMessage: (message: IMessage) => void
    setUser: (user: string) => void
}

// 基类（父类）
class AIChatBox implements IAIChatBox {
    private sdk: ISdk | null = null

    constructor() {
        console.log('AIChatBox constructor')
        this.initScript()
    }

    public setUser(key: string): void {
        if (!this.sdk) return
        this.sdk.setUser('ZQYC_' + key)
    }

    public startChat(): void {}

    public showChat(): void {
        if (!this.sdk) return
        this.sdk.showDrawerChat()
    }

    public sendMessage(message: IMessage): void {
        if (!this.sdk) return
        this.sdk.sendMessage(message)
    }

    public initScript(): void {
        const script = document.createElement('script')
        script.src = 'https://static.shuzutech.com/js/public/shuzu-chat%400.0.3/chat-embed.js?version=' + Date.now()
        script.onload = () => {
            this.initChat()
        }
        script.onerror = () => {
            console.error('脚本加载失败')
        }
        document.head.appendChild(script)
    }

    private initChat() {
        if (!window.ShuzuChatSDK) return null

        this.sdk = new window.ShuzuChatSDK({
            key: import.meta.env.VITE_APP_SHUZU_CHAT_SDK_KEY, // app key
            url: import.meta.env.VITE_APP_SHUZU_CHAT_SDK_URL, // chat服务url
            mode: 'drawer', // 抽屉模式
            width: 720, // 抽屉宽度
            title: 'AI分析',
            avatar: 'https://static.shuzutech.com/images/icons/common_chat/240.png',
            entrance: {
                // 浮动按钮，打开抽屉聊天框
                right: 20, // 浮动右侧距离
                bottom: 20, // 浮动底部距离
                hide: '1', // 隐藏入口按钮
            },
        }) as ISdk

        // 获取配置信息并载入 iframe
        if (this.sdk) {
            this.sdk.getConfig()
        }
    }
}

export default AIChatBox
