<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import Icon from '@/components/common/Icon.vue'

const dataList = [
    {
        name: '累计发放总额',
        icon: 'icon-a-huaban20',
        value: '-',
    },
    {
        name: '累计发放笔数',
        icon: 'icon-a-huaban21',
        value: '-',
    },
]
</script>
<template>
    <div class="jszx">
        <div class="b-margin-24">
            <ModuleTitle title="科创贷款"></ModuleTitle>
        </div>
        <ul class="display-flex space-between">
            <li class="company-item" v-for="(item, index) in dataList" :key="index">
                <div class="display-flex b-margin-16">
                    <div class="company-item-icon text-center">
                        <icon :icon="item.icon" color="#fff"></icon>
                    </div>
                    <div class="company-item-num">{{ item.value }}</div>
                </div>
                <div class="company-item-label">{{ item.name }}</div>
            </li>
        </ul>
    </div>
</template>
<style scoped lang="scss">
ul {
    padding: 0;
    margin: 0;
    li {
        list-style-type: none;
        padding-left: 0;
    }
}
.jszx {
    box-sizing: border-box;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    .company-item {
        width: 48%;
        padding: 16px;
        border-radius: 4px;
        background:
            linear-gradient(0deg, rgba(248, 249, 253, 1), rgba(248, 249, 253, 1)),
            linear-gradient(90deg, rgb(238, 241, 249) 0%, rgb(229, 232, 247) 100%);
        .company-item-icon {
            width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 24px;
            background-color: var(--main-blue-);
            margin-right: 8px;
        }
        .company-item-num {
            font-size: 18px;
            font-weight: 700;
            color: var(--main-black);
        }
        .company-item-label {
            font-size: 14px;
            font-weight: 400;
            color: var(--main-black);
        }
    }
}
</style>
