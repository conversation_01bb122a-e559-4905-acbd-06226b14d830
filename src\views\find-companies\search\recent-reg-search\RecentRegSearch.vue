<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { RecentSearchFilter, RecentSearchInputGroup, RecentSearchTable } from './components'

const keyword = ref('')
const externalParams = ref<Record<string, string | number>>({})
const route = useRoute()
const { params } = route

onMounted(() => {
    const { keyword: _keyword } = params
    let temp = { ...externalParams.value }
    if (typeof _keyword === 'string' && _keyword) {
        keyword.value = _keyword
        temp = {
            ...temp,
            keyword: _keyword,
        }
    }
    if (Object.keys(temp).length) {
        externalParams.value = temp
    }

    externalParams.value = { ...externalParams.value, sortBy: 1 }
})

const onSearchKeyword = (v: string) => {
    const params = {
        ...externalParams.value,
        keyword: v,
    }

    externalParams.value = params
}

const onKeywordChange = (v: string) => {
    keyword.value = v
}
</script>

<template>
    <div class="flex flex-column gap-16">
        <div class="flex flex-column back-color-white border-radius-4 all-padding-16 gap-20">
            <RecentSearchInputGroup
                :defaultValue="keyword"
                placeholder="请输入企业名称或者主营产品"
                :onSearch="onSearchKeyword"
                :onKeywordChange="onKeywordChange"
            />
            <RecentSearchFilter />
        </div>
        <div class="back-color-white height-100 oh all-padding-16">
            <RecentSearchTable :keyword="keyword" :externalParams="externalParams" />
        </div>
    </div>
</template>

<style scoped></style>
