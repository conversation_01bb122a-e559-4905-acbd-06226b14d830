<template>
    <el-descriptions class="margin-top" :column="2" border :label-class-name="'base-info-label'">
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">负责人</div>
            </template>
            {{ crmDetail.user }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">创建人</div>
            </template>
            {{ crmDetail.createUser || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">前负责人</div>
            </template>
            {{ crmDetail.beforeUser || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">所属组织</div>
            </template>
            {{ crmDetail.departmentName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">前所属组织</div>
            </template>
            {{ crmDetail.lastDepartmentName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">创建时间</div>
            </template>
            {{ crmDetail.createTime ? moment(crmDetail.createTime).format("YYYY-MM-DD HH:mm:ss") : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label">
            <template #label>
                <div class="cell-item">更新时间</div>
            </template>
            {{ crmDetail.updateTime ? moment(crmDetail.updateTime).format("YYYY-MM-DD HH:mm:ss") : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="base-info-label" v-show="crmDetail.isCustomer === '1'">
            <template #label>
                <div class="cell-item">转客户时间</div>
            </template>
            {{ crmDetail.turnCustomerDate ? moment(crmDetail.turnCustomerDate).format("YYYY-MM-DD HH:mm:ss") : '-' }}
        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang='ts' setup>
import { onMounted, inject, getCurrentInstance } from 'vue'
import type { Ref } from 'vue'

import type { ILeadData } from '@/types/lead'
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

onMounted(() => { })
</script>

<style lang='scss' scoped>
:deep(.base-info-label) {
    background: var(--table-header-bg-) !important;
}
</style>