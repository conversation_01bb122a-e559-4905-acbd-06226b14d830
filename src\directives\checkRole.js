

export const checkRole = (store) => {
    return {
        beforeMount(el, binding) {
            const userRoles = store.state.user.account.user.role


            const requireRole = binding.value
            const checkAllNotIn = (A, B) => {
                return A.some(item => B.includes(item))
            }


            if (!checkAllNotIn(userRoles,requireRole)) {
                el.style.display = 'none'
            }
        }
    }
}