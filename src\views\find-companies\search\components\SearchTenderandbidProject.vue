<template>
    <el-table
        ref="tableRef"
        :data="tableData"
        @selection-change="selectionChange"
        show-overflow-tooltip
        empty-text="暂无数据"
        :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
    >
        <el-table-column label="公告信息">
            <template #default="scope">
                <div class="display-flex">
                    <div
                        class="t-margin-4 display-flex font-18 pointer color-black"
                        @click="rowClickToDetail(scope.row)"
                    >
                        {{ scope.row.title }}
                    </div>
                    <div class="l-margin-4 t-margin-4" v-for="(tagItem, i) in scope.row.tenderType" :key="i">
                        <el-tag class="font-14" effect="plain">{{ tagItem.label }}</el-tag>
                    </div>
                    <div class="t-margin-4 pointer" style="margin-left: auto" v-if="scope.row.url">
                        <Icon icon="icon-icon_link" color="blue" @click="rowClickToUrl(scope.row.url)"></Icon>
                    </div>
                </div>
                <div class="display-flex t-margin-4 font-16">
                    <div>标的物：{{ scope.row.name ? scope.row.name : '-' }}</div>
                    <div class="l-margin-391">所属地区：{{ scope.row.companyArea ? scope.row.companyArea : '-' }}</div>
                    <div style="margin-left: auto">{{ scope.row.tenderPublicDate }}</div>
                </div>
                <div class="display-flex t-margin-4 font-16" style="background-color: #fafafa">
                    <div v-if="scope.row.outbidUnitList.length">中标单位：</div>
                    <div v-for="(tagItem, i) in scope.row.outbidUnitList" :key="i">
                        <div>{{ tagItem.name }}</div>
                    </div>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <TenderProjectDetail v-if="showTenderProjectDetail" v-model:showTenderProjectDetail="showTenderProjectDetail" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import Icon from '@/components/common/Icon.vue'
import TenderProjectDetail from './TenderProjectDetail.vue'
const showTenderProjectDetail = ref(false)

interface CompanyDataItem {
    title?: string
    tenderType?: tenderTypeItem[]
    url?: string
    name?: string
    companyArea?: string
    budgetPrice?: string
    tenderPublicDate?: string
    outbidUnitList?: { name: string }[]
    companyName?: string
    companyTags?: companyTagsItem[]
    legalperson?: string
    esdate?: string
    regCapDisplay?: string
    bidNum?: string
    announcementType?: string
    announcementTime?: string
    announcementCity?: string
    announcementSubject?: string
    announcementTitle?: string
}

interface tenderTypeItem {
    label?: string
}

interface companyTagsItem {
    label?: string
}

const props = defineProps<{
    tableData?: CompanyDataItem[]
    rowClickToUrl: (url: string) => void
}>()

const emit = defineEmits(['selection-change'])

const tableRef = ref(null)

const selectionChange = (val: CompanyDataItem[]) => {
    emit('selection-change', val)
    console.log('选中的行', val)
}

const rowClickToDetail = (row: CompanyDataItem) => {
    showTenderProjectDetail.value = true
    console.log('点击的标题', row.title)
}

console.log(props.tableData)
</script>

<style scoped>
.l-margin-391 {
    margin-left: 391px;
}
</style>
