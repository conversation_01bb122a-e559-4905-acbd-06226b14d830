<script lang="ts" setup>
import { ref, computed } from 'vue'
import Icon from '@/components/common/Icon.vue'
import type { ModelRes, Model001ResInfo } from '@/types/home'
import type { ITenant } from '@/types/user'
const props = defineProps<{
    data: ModelRes[]
    tenantInfo: ITenant | undefined
}>()

const areaOverviewData = computed(() => {
    if (!props.data.length) return [] as Model001ResInfo[]
    return props.data.map((item) => {
        const [title, value] = Object.entries(item)[0]
        return {
            title,
            ...value,
        } as Model001ResInfo
    })
})

const labelList = ref([
    {
        title: '注册企业数（家）',
        key: 'zcqys',
        icon: 'icon-jianzhu02-F',
    },
    {
        title: '本年度净增企业数（家）',
        key: 'bndjzqys',
        icon: 'icon-a-huaban263',
    },
    {
        title: '存续企业',
        key: 'cxqyzs',
        icon: 'icon-a-huaban34',
    },
    {
        title: '存续企业同比增长率%',
        key: 'cxqytbzzl',
        icon: 'icon-a-huaban82',
    },
    {
        title: '融资企业',
        key: 'rzqy',
        icon: 'icon-a-lujing2',
    },
    {
        title: '上市企业',
        key: 'ssqy',
        icon: 'icon-a-huaban265',
    },
])
</script>

<template>
    <div class="area-data-overview border-radius-4">
        <div class="font-first-title-active b-margin-21px" style="color: #fff;">{{ props.tenantInfo?.areaName || '南京市' }}数据总览</div>
        <ul class="display-flex space-around" style="padding-left: 0">
            <li class="data-item" v-for="item in labelList" :key="item.key">
                <div class="display-flex" style="align-items: center">
                    <div class="item-img">
                        <Icon :icon="item.icon" color="#6B8DE4" />
                    </div>
                    <div class="item-num">{{ Number(areaOverviewData.find((da)=>{return da.key === item.key})?.indexValue) || '-' }}</div>
                </div>
                <div class="font-second-title" style="color: #fff">{{ item.title }}</div>
            </li>
        </ul>
    </div>
</template>

<style lang="scss" scoped>
li {
    list-style-type: none;
    padding-left: 0;
}
.area-data-overview {
    height: 169px;
    padding: 16px;
    background: linear-gradient(180deg, rgba(107, 141, 228, 1) 0%, rgba(153, 180, 255, 1) 100%);
    .data-item {
        height: 92px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        .item-img {
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            background-color: #fff;
            border-radius: 24px;
            margin-right: 8px;
        }
        .item-num {
            font-size: 18px;
            font-weight: 700;
            color: #fff;
        }
    }
}
</style>
