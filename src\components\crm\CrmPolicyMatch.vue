<script lang="ts" setup>
import { ref, onMounted, defineEmits, getCurrentInstance } from 'vue'
import type { Ref } from 'vue'
import CrmPolicyMatchDrawer from '@/components/crm/crm-policy-match/CrmPolicyMatchDrawer.vue'
import crmService from '@/service/crmService'
import type { IGoodsPolicyItem } from '@/types/lead'

const props = defineProps({
    companyId: {
        type: String,
        required: true
    }
})
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const emits = defineEmits(['toReportList'])
const dataList = ref<IGoodsPolicyItem[]>([])
const getList = () => {
    crmService.crmGoodsPolicyEntMatchRule({ companyId: props.companyId }).then((res) => {

        dataList.value = res.map(item => {
            return {

                ...item, ...item.matchScore, name: item.name
            }
        })
        console.log('获取产品匹配列表结果', dataList.value)

    })
}
onMounted(() => {
    getList()
})
const handleInvoiceCollect = () => {
    emits('toReportList')
}

const policyDetail: Ref<IGoodsPolicyItem> = ref({} as IGoodsPolicyItem)
const handleOpenDetail = async (item: IGoodsPolicyItem) => {
    if (!item.id) {
        return
    }
    // checkedProduceId.value = item.id
    // let detailRes = await crmService.goodsFinanceMatchDetail({
    //     companyId: props.companyId,
    //     id: item.id
    // })
    policyDetail.value = item
    drawerVisible.value = true
}
const drawerVisible = ref(false)
</script>
<template>
    <div class="display-flex b-margin-16" style="min-width: 898px">
        <Icon class="r-margin-5" icon="icon-a-1tongyong_2Icontubiao_Fill_Check-Circle-Fill" :size="16"
              color="var(--main-green-)" />
        <div class="tips">
            购买报告并
            <span class="collect" @click="handleInvoiceCollect()">进行票税采集</span> 获取更精准的政策和金融匹配信息！
        </div>
    </div>
    <div class="">
        <div class="border-radius-8 border-tag tb-padding-12 lr-padding-16 mw-440 b-margin-16 pointer"
             v-for="(item, index) in dataList" :key="index" @click="handleOpenDetail(item)">
            <div class="display-flex b-margin-8">

                <div class="flex-1">
                    <div class="display-flex space-between top-bottom-center">
                        <div class="font-16 maxw-250 text-overflow">{{ item.name }}</div>
                        <div class="font-14 color-primary pointer">政策匹配度：{{ item?.totalScore || 0 }}%</div>
                    </div>
                    <div class=" font-14">
                        <el-row :gutter="20">
                            <el-col :span="8" class="t-margin-12">
                                政策类型：{{ item.childGoodsTypeStr || '-' }}

                            </el-col>
                            <el-col :span="8" class="t-margin-12">
                                政策标题：{{ item.name || '-' }}

                            </el-col>
                            <el-col :span="8" class="t-margin-12">
                                政策文号：{{ item.spu?.policyNumber || '-' }}

                            </el-col>
                            <el-col :span="8" class="t-margin-12">
                                政策级别：{{ item.spu?.policyLevelStr || '-' }}

                            </el-col>
                            <el-col :span="8" class="t-margin-12">
                                行业类别：{{ item.spu?.policyIndustryStr || '-' }}

                            </el-col>
                            <el-col :span="8" class="t-margin-12">
                                发文部门：{{ item.spu?.issuingDepartmentStr || '-' }}

                            </el-col>
                            <el-col :span="8" class="t-margin-12">
                                主题分类：{{ item.spu?.policyTopicStr || '-' }}
                            </el-col>
                            <el-col :span="8" class="t-margin-12">
                                申报截止日期：{{ item.spu?.deadlineTime ? moment(item.spu.deadlineTime).format("YYYY-MM-DD") :
                                    '-' }}
                            </el-col>
                        </el-row>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <CrmPolicyMatchDrawer v-if="drawerVisible" v-model:visible="drawerVisible" :detailInfo="policyDetail">
    </CrmPolicyMatchDrawer>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';

.tips {
    font-size: 14px;
    color: var(--main-black);

    .collect {
        color: var(--main-blue-);
        cursor: pointer;
    }
}

.text-overflow {
    overflow: hidden;
    text-overflow: ellipsis; // 当内容超出容器时，显示为省略号（...）
    white-space: nowrap;
}

.product-border {
    border: 1px solid rgba(25, 102, 255, 0.2);
    border-radius: 2px;
    background: rgba(25, 102, 255, 0.06);
}
</style>
