<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { onMounted, ref, watch } from 'vue'
const props = defineProps<{
    defaultValue: string
    placeholder: string
    onSearch?: (v: string) => void
    onKeywordChange: (v: string) => void
}>()

const input = ref('')

watch(
    () => props.defaultValue,
    (value) => {
        input.value = value
    }
)

const submit = () => {
    if (!props.onSearch) return
    props.onSearch(input.value)
}

const onInput = (v: string) => {
    input.value = v
    props.onKeywordChange(input.value)
}

onMounted(() => {
    input.value = props.defaultValue
})
</script>

<template>
    <div class="flex flex-row gap-16 top-bottom-center">
        <el-input
            v-model="input"
            style="width: 412px; height: 40px"
            :placeholder="placeholder"
            :prefix-icon="Search"
            clearable
            @input="onInput"
            @keyup.enter="submit"
        />
        <el-button type="primary" style="height: 40px; width: 68px" @click="submit">搜索</el-button>
    </div>
</template>

<style scoped></style>
