<template>
    <div>
        <div class="display-flex justify-flex-end b-margin-10">
            <el-button v-if="!isAdmin" type="primary" @click="handleAdd">新增</el-button>
        </div>
        <el-table
            :data="tableList"
            style="width: 100%;height: 500px;"
            show-overflow-tooltip
            v-loading="tableLoading"
            :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
        >
            <template v-if="!tableLoading" #empty>
                <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                    <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                    <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                </div>
            </template>
            <el-table-column label="ID" prop="id" />
            <el-table-column label="模板名称" prop="templateName" />
            <el-table-column label="模板id" prop="templateId" />
            <el-table-column label="key" prop="accessKey" />
            <el-table-column label="secret" prop="secretKey" />
            <el-table-column label="短信签名" prop="signName" />
            <el-table-column label="短信渠道" prop="channel" >
                <template #default="scope">
                    {{ scope.row.channel === 'ALI' ? '阿里云' : scope.row.channel === 'TENCENT' ? '腾讯云' : '其他' }}
                </template>
            </el-table-column>
            <el-table-column label="短信内容" prop="content" />
            <el-table-column label="是否启用" prop="enable" >
                <template #default="scope">
                    {{ scope.row.enable ? '启用' : '禁用' }}
                </template>
            </el-table-column>
            <el-table-column v-if="isAdmin" label="所属租户" prop="tenantName"/>
            <el-table-column
                v-else
                fixed="right"
                label="操作"
                width="150px"
            >
                <template #default="scope" >
                    <div class="display-flex gap-10">
                        <div
                            class="pointer"
                            style="color: #1966ff"
                            type="primary"
                            @click="editTemplate(scope.row)"
                        >
                            编辑
                        </div>
                        <div
                            class="pointer"
                            style="color: #ff4b33"
                            type="primary"
                            @click="deleteTemplate(scope.row.id)"
                        >
                            删除
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-affix position="bottom">
            <div class="pagination-bar">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :total="pageInfo.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @change="pageChange"
                /> 
            </div>
        </el-affix>    
    </div>
    <SMSDialog :visible="isDialogVisible" :title="title" @update:visible="handleClose" :refresh="search" :editItem="editItem" />
</template>

<script lang='ts' setup>
import { onMounted, reactive, ref, computed } from 'vue'
import SMSDialog from './SMSDialog.vue'
import type { IPaginationParams, ISMSTemplatePageResponseItem } from '@/types/sms'
import systemService from '@/service/systemService'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { ElMessage, ElMessageBox } from 'element-plus'

const store = useStore<RootState>()
const isAdmin = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})
const tableList = ref<ISMSTemplatePageResponseItem[]>([])
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})
const queryParams = ref<IPaginationParams>({
    page:1,
    pageSize:20
})
const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    search()
}
const tableLoading = ref(false)
const isDialogVisible = ref(false)
const title = ref('')

const handleAdd = () => {
    isDialogVisible.value = true
    title.value = '新增'
}

const editItem = ref<ISMSTemplatePageResponseItem>()
const editTemplate = (row:ISMSTemplatePageResponseItem) => {
    editItem.value = row
    isDialogVisible.value = true
    title.value = '编辑'
    console.log(editItem.value)
}

const handleClose = () => {
    isDialogVisible.value = false
}

const deleteTemplate = (id:string) => {
    ElMessageBox.confirm('确认删除该模板吗？').then(() => {
        systemService.smsTemplateDelete({id}).then(() => {
            ElMessage.success('删除成功')
        }).finally(() => {
            search()
        })
    })
}

const search = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.pageSize = pageInfo.pageSize
    systemService.smsTemplatePage(queryParams.value).then((res) => {
        if(res.success){
            console.log(res)
            tableList.value = res.data
            totalNum.value = res.total
        }else{
            ElMessage.error('系统错误')
        }
    }).finally(() => {
        tableLoading.value = false
    })
}

onMounted(() => {
    search()
})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    margin-top: 16px;
}
</style>